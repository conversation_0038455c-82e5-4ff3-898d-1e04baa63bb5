// import { UtilsExtends } from '../../../../util/UtilsExtends';
// import { BulletLauncher, BulletObj, BulletTween } from '../../bullet/BulletObj';

// /**
//  * 直线弹
//  * 效果： 沿着发射时候的方向  注意如果是 球体等不需要方向的不用设置这个
//  */
// export class BulletTweenParabola extends BulletTween {
//     private v = cc.Vec3.RIGHT
//     // 抛物线参数
//     private parabolaFactor: number = 0.1; // 控制抛物线弯曲程度的因子
//     /** 保持发射速度的时间，单位s */
//     public keepShootDirTime: number = 0.1;
//     /** 控制转向的速度 [0~1], 1可以实现瞬间转向， 可以做到弧形转向 */
//     public trunScale: number = 0.1;
//     /** flolow type, 始终第一个，最后一个，中间的，距离最近的。 或者选定了不变 */
//     private lastV: cc.Vec3 = undefined;
//     process(t: number, obj: BulletObj, lunch: BulletLauncher): cc.Vec3 {
//         if (t === 0) {
//             this.lastV = obj.caster.right;
//         }
//         if (t < this.keepShootDirTime) {
//             return this.lastV;
//         }
//         const gravityEffect = this.parabolaFactor * t * t; // 这里`parabolaFactor`是自定义的重力影响因子
//         this.lastV.y -= gravityEffect;
//         this.lastV = this.lastV.normalize();
//         return this.lastV;
//     }
    
// }
