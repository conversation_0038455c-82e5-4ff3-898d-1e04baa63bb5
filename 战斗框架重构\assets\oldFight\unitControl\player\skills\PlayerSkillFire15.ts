import FightCtrl from "../../../../ctrl/FightCtrl";
import { UtilsExtends } from "../../../../util/UtilsExtends";
import { TimelineEvent, TimelineObj, TimelineNode, TimelineModel } from "../../../timeline/TimelineObj";
import { BulletLauncher, BulletModel } from "../../bullet/BulletObj";
import { Skill, SkillModel } from "../../CharacterSkillObj";
import { CharacterControl } from "../../CharacterControl";
import { PlayerBulletHitCommon } from "../bulletOnHit/PlayerBulletHitCommon";
import { SkillNames } from "./SkillNames";
import { BulletTweenForwardFireArrowPlayer } from "../bulletTween/BulletTweenForwardFireArrowPlayer";
import { PlayerSkill1Hit, SlowSpeedAttackHit } from "../bulletOnHit/PlayerSkillNormalHit";
import { AudioMgr } from "../../../../../../scripts/framework/manager/AudioMgr";
import { AudioId } from "../../../../../../scripts/game/config/Config";
import { DataMgr } from "../../../../../../scripts/game/manager/DataMgr";

class FireBulletEvent extends TimelineEvent {
    constructor(
        public launcher: BulletLauncher,
    ) { super() }
    doEvent(timelineObj: TimelineObj, index: number): void {
        let target = timelineObj.targets[index]
        if (!timelineObj.targets[index]) return

        this.launcher.fireWorldPosition = FightCtrl.ins.skillStartPoint.convertToWorldSpaceAR(cc.Vec3.ZERO)
        this.launcher.caster = timelineObj.caster;

        this.playEffect(DataMgr.getInstance().skillCfg[Number(this.launcher.model.id) - 1].sound)
        let bulletCom = FightCtrl.ins.createBullet(this.launcher, target, `partner/buddy_15_vfx/buddy_15_vfx`, 'skill_bullet', false);
        bulletCom.onCollisionEvent = () => {
            timelineObj.targets.forEach(t => bulletCom.playHit(t))
            bulletCom.isCanHert = false
            bulletCom.onCollisionEvent = null
            bulletCom.isNeedCalculateRotation = false
            bulletCom.isNeedCalculatePos = false
        }
        bulletCom.isNeedCollisionStop = false;
    }
}
/**发射爱心烈焰弹，对全体目标造成攻击力{0}伤害，并减慢其{1}攻速，持续{2}秒*/
export class PlayerSkillFire15 extends Skill {
    constructor(
        public prefabKey: string,
    ) {
        super(SkillNames.playerSKill15);
        const launcher = new BulletLauncher();
        launcher.speed = 15
        launcher.model = new BulletModel("15", this.prefabKey);
        launcher.model.lifeDuration = 5;
        launcher.model.tween = new BulletTweenForwardFireArrowPlayer();
        launcher.model.onHit = new SlowSpeedAttackHit();
        this.skillMode = {
            name: this.name,
            effect: {
                id: UtilsExtends.generateUniqueId(),
                name: this.name,
                lifeDuration: 0.1,
                nodes: [
                    {
                        timeStartPoint: 0.01,
                        event: new FireBulletEvent(launcher)
                    } as TimelineNode,
                ]
            } as TimelineModel,
            buffs: [],
            coldTime: 0.5,
        }
    }
}
