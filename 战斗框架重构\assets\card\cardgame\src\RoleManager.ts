// import Base from "./Base/Base";
// import RoleBase from "./Base/RoleBase";

// const { ccclass, property } = cc._decorator;

// declare global {
//     export interface IRoleManager {
//     }
// }


// /**
//  * @features : 功能
//  * @description: 说明
//  * @Date : 2020-08-17 10:25:03
//  * <AUTHOR> judu233(<EMAIL>)
//  * @LastEditTime : 2023-11-14 13:58:24
//  * @LastEditors : judu233
//  */
// // @ccclass
// export default class RoleManager extends Base {
//     data: IRoleManager;
//     /**所有的角色列表 */
//     roles: RoleBase[] = [];

//     initRoles(datas: ICampMgrDataType) {
//         for (let roleData of datas.roleData) {
//             let role = new RoleBase();
//             role.initRole(roleData);
//             role.cardMgr.initCardList(datas)
//             this.roles.push(role);
//         }
//     }

//     deleateRole(role: RoleBase) {
//         if (role && cc.isValid(role)) {
//             cc.log(`删除:roleName:${role.roleName}, gui:${role.GID}`)
//             this.roles.delete(role)
//         }
//     }

//     /**检查所有角色是否死亡 */
//     checkCardAllDeath() {
//         return this.roles.every(role => role.isDeath)
//     }
//     /**检查所有角色是否活着 */
//     checkCardAllSurvive() {
//         return this.roles.every(role => !role.isDeath)
//     }

//     /**获取1位角色的随机卡片 */
//     getRoleCard(count: number) {
//         return this.roles[0]?.cardMgr.getSurviveCardForRandom(count)
//     }

// }
