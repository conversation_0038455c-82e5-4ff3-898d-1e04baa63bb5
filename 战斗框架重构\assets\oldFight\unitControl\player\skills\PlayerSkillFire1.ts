import FightCtrl from "../../../../ctrl/FightCtrl";
import { UtilsExtends } from "../../../../util/UtilsExtends";
import { TimelineEvent, TimelineObj, TimelineNode, TimelineModel } from "../../../timeline/TimelineObj";
import { BulletLauncher, BulletModel, BulletObj, BulletOnCreate } from "../../bullet/BulletObj";
import { Skill, SkillModel } from "../../CharacterSkillObj";
import { CharacterControl } from "../../CharacterControl";
import { PlayerBulletHitCommon } from "../bulletOnHit/PlayerBulletHitCommon";
import { SkillNames } from "./SkillNames";
import { PlayerSkill1Hit } from "../bulletOnHit/PlayerSkillNormalHit";
import { AudioMgr } from "../../../../../../scripts/framework/manager/AudioMgr";
import { AudioId } from "../../../../../../scripts/game/config/Config";
import { DataMgr } from "../../../../../../scripts/game/manager/DataMgr";

class FireBulletEvent extends TimelineEvent {
    constructor(
        public launcher: BulletLauncher,
    ) { super() }
    doEvent(timelineObj: TimelineObj, index: number): void {
        let target = timelineObj.targets[index]
        if (!timelineObj.targets[index]) return

        // this.launcher.fireWorldPosition = target.getComponent(CharacterControl).node.convertToWorldSpaceAR(cc.Vec3.ZERO)
        this.launcher.fireWorldPosition.y = FightCtrl.ins.enemyStartWordPoint.y   //发射的y限制在地面
        this.launcher.fireWorldPosition.x = FightCtrl.ins.playerBaseWordX + 300
        this.launcher.caster = timelineObj.caster;

        this.playEffect(DataMgr.getInstance().skillCfg[Number(this.launcher.model.id) - 1].sound)
        //由于没有子弹tween，所以不存在碰撞，需要手动触发hit
        let bulletCom = FightCtrl.ins.createBullet(this.launcher, target, `partner/buddy_1_vfx/buddy_1_vfx`, 'skill_hit', false, (trackEntry: sp.spine.TrackEntry, event: sp.spine.Event) => {
            if (event.data.name == 'attack') {
                timelineObj.targets.forEach(t => bulletCom.playHit(t))
                bulletCom.isCanHert = false
            }
        });
        bulletCom.checkDistance = -1
    }
}
/**西瓜突袭 :西瓜从天而降，对随机2个目标造成攻击力{0}伤害  , 需要在一个技能时间轴内对多个敌人攻击 */
export class PlayerSkillFire1 extends Skill {
    constructor(
        public prefabKey: string,
    ) {
        super(SkillNames.playerSKill1);
        const launcher = new BulletLauncher();
        launcher.speed = 0
        launcher.model = new BulletModel("1", this.prefabKey);
        launcher.model.lifeDuration = 2
        launcher.model.onHit = new PlayerSkill1Hit();
        this.skillMode = {
            name: this.name,
            effect: {
                id: UtilsExtends.generateUniqueId(),
                name: this.name,
                lifeDuration: 0.3,
                nodes: [
                    {
                        timeStartPoint: 0.01,
                        event: new FireBulletEvent(launcher)
                    } as TimelineNode,
                ]
            } as TimelineModel,
            buffs: [],
            coldTime: 0.5,
        }
    }
}