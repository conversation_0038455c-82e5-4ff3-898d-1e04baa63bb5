
import { UtilsExtends } from '../../util/UtilsExtends';
import { TimelineModel } from '../timeline/TimelineObj';
import { AddBuffInfo } from './CharacteBuffObj';
import { SkillNames } from './player/skills/SkillNames';
const { ccclass, property } = cc._decorator;

export class SkillModel {
    /** 技能的id,名字也是唯一id，不能重复  */
    name: string;
    /** 技能使用的条件，这个游戏中只有资源需求，比如hp、ammo之类的  */
    // condition、cost
    /** 技能的效果，必然是一个timeline  */
    effect: TimelineModel;
    /** 冷却时间,单位：秒, 释放技能的间隔  */
    coldTime?: number = 5;
    /** 学会技能的时候，同时获得的buff  */
    buffs: AddBuffInfo[];
    /**技能被添加时 */
    onAdd?(skillObj: CharacterSkillObj, targets: cc.Node[]) { }
}
export class CharacterSkillObj {
    public model: SkillModel;
    /** 冷却时间已经过了多少，单位：秒 */
    public coldTimeElapsed: number = 0;
    constructor(model: SkillModel) {
        this.model = UtilsExtends.deepClone(model);
        // 默认可用
        this.coldTimeElapsed = this.model.coldTime;
    }
    canUse(): boolean {
        if (this.model && this.coldTimeElapsed >= this.model.coldTime) {
            return true;
        }
        return false;
    }
}

export class Skill {
    skillMode: SkillModel;
    constructor(public name: string = SkillNames.default) { }
}