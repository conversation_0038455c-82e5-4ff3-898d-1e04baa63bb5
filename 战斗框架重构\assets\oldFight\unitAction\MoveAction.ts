
/**
 * 移动一帧，会修改node
 */
export class MoveAction extends cc.Component {
    /** 是否有权移动 */
    private canMove: boolean = true;
    velocity = cc.Vec3.ZERO;

    /**跟随目标 */
    followTarget: cc.Node
    /**跟随偏移 */
    followOffset = cc.v3(0, 0)
    followInit(parent: cc.Node, flowTarget: cc.Node) {
        this.node.parent = parent;
        this.followTarget = flowTarget;
        this.update(0)
    }

    moveBy(v: cc.Vec3) {
        this.velocity = v;
    }

    onDisable(): void {
        this.followTarget = null
    }

    update(deltaTime: number) {
        if (this.followTarget) {
            this.node.setPosition(this.followTarget.converToNode(this.node.parent).add(cc.v3(- this.node.width / 2, 100)).add(this.followOffset))
        } else {
            if (!this.canMove || this.velocity == cc.Vec3.ZERO) {
                return;
            }
            const targetPos = new cc.Vec3(
                this.velocity.x * 1 + this.node.convertToWorldSpaceAR(cc.Vec3.ZERO).x,
                this.velocity.y * 1 + this.node.convertToWorldSpaceAR(cc.Vec3.ZERO).y,
                this.velocity.z * 1 + this.node.convertToWorldSpaceAR(cc.Vec3.ZERO).z
            );
            this.node.position = this.node.parent.convertToNodeSpaceAR(targetPos);
            this.velocity = cc.Vec3.ZERO;
        }

    }



}

