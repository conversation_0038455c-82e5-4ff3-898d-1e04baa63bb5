import { CharacterControl } from "../unitControl/CharacterControl";

/*** 一些通用的决策*/
export abstract class OperatorBehavior {
    enable: boolean = true;
    abstract tickOperator(roleControl: CharacterControl, dt: number): void;
    abstract onClear(roleControl: CharacterControl): void;
}

/*** 决策 绑定的node需要包含RoleControl*/
export class BaseOperator extends cc.Component {
    roleControl: CharacterControl;
    oBehaviors: OperatorBehavior[] = [];
    start(): void {
        if (!this.roleControl) {
            this.init();
        }
    }
    update(dt: number): void {
        this.oBehaviors.forEach(e => {
            if (e.enable) {
                e.tickOperator(this.roleControl, dt);
            }
        });
    }
    onDisable(): void {
        this.clearBehavior()
    }
    init(): BaseOperator {
        this.roleControl = this.node.getComponent(CharacterControl);
        return this;
    }
    addBehavior<T extends OperatorBehavior>(behavior: T) {
        this.oBehaviors.push(behavior);
        return behavior
    }
    getBehavior<T extends OperatorBehavior>(behavior: new (...args: any[]) => T): T {
        for (let ob of this.oBehaviors) {
            if (ob instanceof behavior) {
                return ob as any
            }
        }
    }
    clearBehavior() {
        this.oBehaviors.forEach(e => {
            e.onClear(this.roleControl);
        });
    }
}

