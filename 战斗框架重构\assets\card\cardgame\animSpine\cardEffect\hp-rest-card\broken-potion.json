{"skeleton": {"hash": "zNTjvgimrsw", "spine": "3.8-from-4.0-from-4.1.24", "x": -166.42, "y": 2.16, "width": 367, "height": 503, "images": "./images/", "audio": "H:/fan/broken-potion"}, "bones": [{"name": "root", "y": 0.8}, {"name": "bone", "parent": "root", "x": 4.86, "y": 186.13}, {"name": "bone2", "parent": "root", "x": 30.85, "y": 116.27}, {"name": "bone3", "parent": "root", "x": 7.35, "y": 257.82}, {"name": "bone4", "parent": "root", "x": -116.45, "y": 96.78}, {"name": "bone5", "parent": "root", "x": -59.05, "y": 149.85}, {"name": "bone6", "parent": "bone3", "x": -33.09, "y": 1.42}, {"name": "bone7", "parent": "bone3", "x": 34.18, "y": 1.42}, {"name": "bone8", "parent": "bone3", "x": -53.77, "y": 3.12}, {"name": "bone9", "parent": "bone3", "x": 56.83, "y": 2.7}, {"name": "bone10", "parent": "root", "x": 21.23, "y": 277.45}, {"name": "bone11", "parent": "root", "x": 96.6, "y": 137.63}, {"name": "bone12", "parent": "root", "x": 73.22, "y": 123.86}, {"name": "bone13", "parent": "root", "rotation": 32.61, "x": 90.67, "y": 163.46}], "slots": [{"name": "bg", "bone": "bone4", "attachment": "bg"}, {"name": "HP-BG", "bone": "bone2", "attachment": "HP-BG"}, {"name": "root", "bone": "bone", "attachment": "root"}, {"name": "water", "bone": "bone", "attachment": "water"}, {"name": "up2", "bone": "root", "attachment": "up2"}, {"name": "up", "bone": "root", "attachment": "up"}, {"name": "main", "bone": "bone5", "attachment": "main"}, {"name": "droplet", "bone": "bone11", "attachment": "droplet"}, {"name": "droplet2", "bone": "bone12", "attachment": "droplet"}, {"name": "droplet3", "bone": "bone13", "attachment": "droplet"}], "skins": [{"name": "default", "attachments": {"bg": {"bg": {"x": 133.53, "y": 156.09, "width": 367, "height": 503}}, "droplet": {"droplet": {"x": -4.47, "width": 35, "height": 25}}, "droplet2": {"droplet": {"x": -1.96, "y": 8.78, "scaleX": 0.7113, "scaleY": 0.7113, "rotation": -30.41, "width": 35, "height": 25}}, "droplet3": {"droplet": {"x": -6.01, "y": -0.83, "scaleX": 0.5353, "scaleY": 0.5353, "width": 35, "height": 25}}, "HP-BG": {"HP-BG": {"x": -23.63, "y": 100.45, "width": 167, "height": 223}}, "main": {"main": {"x": 68.27, "y": 78.37, "width": 175, "height": 250}}, "root": {"root": {"type": "clipping", "end": "water", "vertexCount": 4, "vertices": [-186.4, 68.66, 195.41, 67.7, 204.16, -201.46, -187.92, -203.08], "color": "ce3a3aff"}}, "up": {"up": {"type": "mesh", "uvs": [0, 0.49905, 0.03853, 0.10399, 0.19807, 0, 0.49764, 0, 0.8628, 0, 0.9603, 0.1292, 1, 0.49065, 0.94966, 1, 0.8628, 1, 0.50473, 1, 0.19984, 1, 0.05094, 0.89412], "triangles": [9, 3, 4, 8, 9, 4, 8, 4, 5, 8, 5, 6, 7, 8, 6, 10, 3, 9, 11, 1, 2, 0, 1, 11, 10, 2, 3, 11, 2, 10], "vertices": [3, 8, -18.86, -1.11, 0.21639, 6, -39.54, 0.59, 0.00075, 10, -86.51, -17.63, 0.78286, 3, 8, -13.19, 11.14, 0.83651, 6, -33.87, 12.84, 0.16236, 3, -66.96, 14.26, 0.00113, 4, 8, 10.26, 14.36, 0.50318, 6, -10.42, 16.06, 0.19167, 3, -43.51, 17.48, 0.27069, 7, -77.69, 16.06, 0.03446, 5, 8, 54.3, 14.36, 0.16985, 6, 33.62, 16.06, 0.19167, 3, 0.53, 17.48, 0.27069, 7, -33.66, 16.06, 0.09803, 9, -56.3, 14.78, 0.26977, 4, 6, 87.3, 16.06, 0.02931, 3, 54.21, 17.48, 0.26956, 7, 20.02, 16.06, 0.09808, 9, -2.63, 14.78, 0.60305, 2, 7, 34.35, 12.06, 0.06361, 9, 11.71, 10.78, 0.93639, 3, 7, 40.19, 0.85, 0.00088, 9, 17.54, -0.43, 0.21626, 10, 60.49, -17.37, 0.78286, 2, 7, 32.79, -14.94, 0.07308, 9, 10.14, -16.22, 0.92692, 4, 6, 87.3, -14.94, 0.01496, 3, 54.21, -13.52, 0.29148, 7, 20.02, -14.94, 0.09997, 9, -2.63, -16.22, 0.59359, 5, 8, 55.34, -16.64, 0.13829, 6, 34.66, -14.94, 0.2092, 3, 1.57, -13.52, 0.29228, 7, -32.61, -14.94, 0.09595, 9, -55.26, -16.22, 0.26428, 4, 8, 10.52, -16.64, 0.46817, 6, -10.16, -14.94, 0.21265, 3, -43.25, -13.52, 0.29228, 7, -77.43, -14.94, 0.0269, 3, 8, -11.37, -13.35, 0.8015, 6, -32.05, -11.66, 0.1977, 3, -65.14, -10.24, 0.0008], "hull": 12, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 0, 22], "width": 147, "height": 31}}, "up2": {"up2": {"type": "clipping", "end": "up", "vertexCount": 34, "vertices": [-35.28, 272.12, -45.15, 263.87, -54.2, 251.79, -57.83, 246.96, -60.85, 238.1, -63.46, 225.62, -64.67, 212.54, -63.26, 196.03, -61.05, 177.52, -56.42, 164.63, -46.15, 151.15, -34.88, 141.09, -21.4, 133.84, -2.07, 127.6, 19.87, 128.2, 42.81, 134.04, 52.23, 139.64, 52.81, 147.31, 49.45, 151.35, 60.73, 161.82, 47.84, 169.06, 71.39, 175.3, 72.6, 190.2, 80.45, 188.99, 80.85, 193.62, 82.26, 206.3, 80.85, 221.19, 75.22, 244.34, 69.58, 258.43, 60.73, 269.3, 48.05, 276.34, 29.53, 283.99, 13.83, 285.6, -3.68, 279.36], "color": "ce3a3aff"}}, "water": {"water": {"x": 3.36, "y": 11.09, "width": 151, "height": 142}}}}], "animations": {"animation": {"slots": {"droplet": {"color": [{"time": 0.8, "color": "ffffffff"}, {"time": 0.9667, "color": "ffffff00", "curve": "stepped"}, {"time": 1, "color": "ffffffff"}], "attachment": [{"time": 0.7, "name": null}]}, "droplet2": {"color": [{"time": 0.8, "color": "ffffffff"}, {"time": 0.9667, "color": "ffffff00", "curve": "stepped"}, {"time": 1, "color": "ffffffff"}]}, "droplet3": {"color": [{"time": 0.8, "color": "ffffffff"}, {"time": 0.9667, "color": "ffffff00", "curve": "stepped"}, {"time": 1, "color": "ffffffff"}], "attachment": [{"time": 0.4667, "name": null}]}}, "bones": {"bone3": {"rotate": [{"angle": -0.07}], "translate": [{}, {"time": 1, "y": -152.62}], "scale": [{}, {"time": 0.2, "y": 0.624}, {"time": 0.3667}, {"time": 0.5667, "y": 0.624}, {"time": 0.7333}], "shear": [{}, {"time": 0.2, "y": -44.02}, {"time": 0.3667}, {"time": 0.5667, "y": -44.02}, {"time": 0.7333}]}, "bone6": {"rotate": [{}, {"time": 0.2, "angle": -23.48}, {"time": 0.3667}, {"time": 0.5667, "angle": -23.48}, {"time": 0.7333}, {"time": 0.9, "angle": -23.48}, {"time": 1}], "translate": [{"y": -0.04}], "scale": [{}, {"time": 0.2, "x": 1.988}, {"time": 0.3667}, {"time": 0.5667, "x": 1.988}, {"time": 0.7333}, {"time": 0.9, "x": 1.988}, {"time": 1}]}, "bone7": {"rotate": [{}, {"time": 0.2, "angle": -23.48}, {"time": 0.3667}, {"time": 0.5667, "angle": -23.48}, {"time": 0.7333}, {"time": 0.9, "angle": -23.48}, {"time": 1}], "translate": [{"y": 0.04}], "scale": [{}, {"time": 0.2, "x": 1.988}, {"time": 0.3667}, {"time": 0.5667, "x": 1.988}, {"time": 0.7333}, {"time": 0.9, "x": 1.988}, {"time": 1}]}, "bone8": {"translate": [{"y": -0.06}], "scale": [{}, {"time": 0.2, "x": 1.568}, {"time": 0.3667}, {"time": 0.5667, "x": 1.568}, {"time": 0.7333}, {"time": 0.9, "x": 1.568}, {"time": 1}]}, "bone9": {"translate": [{"y": 0.06}], "scale": [{}, {"time": 0.2, "x": 1.568}, {"time": 0.3667}, {"time": 0.5667, "x": 1.568}, {"time": 0.7333}, {"time": 0.9, "x": 1.568}, {"time": 1}]}, "bone10": {"translate": [{}, {"time": 1, "y": -152.62}]}, "bone12": {"translate": [{"time": 0.7}, {"time": 0.9667, "x": 8.2, "y": -15.8, "curve": "stepped"}, {"time": 1}]}, "bone11": {"translate": [{"time": 0.7}, {"time": 0.9667, "x": 8.2, "y": -5.77, "curve": "stepped"}, {"time": 1}]}, "bone13": {"translate": [{"time": 0.7}, {"time": 0.9667, "x": 13.37, "y": 1.52, "curve": "stepped"}, {"time": 1}]}}, "deform": {"default": {"root": {"root": [{}, {"time": 1, "offset": 1, "vertices": [-134.60509, 2e-05, -134.60509]}]}}}}}}