import { CharacterControl } from "../../CharacterControl";
import { <PERSON>etObj, BulletOnHit } from "../../bullet/BulletObj";
import { Damage } from "../../damage/DamageInfo";
import FightCtrl from "../../../../ctrl/FightCtrl";
import { UtilsExtends } from "../../../../util/UtilsExtends";
import { UIUtils } from "../../../../../../scripts/framework/utils/UIUtils";
import { DataMgr } from "../../../../../../scripts/game/manager/DataMgr";
import PropertCrtl from "../../../../ctrl/PropertCrtl";
import { BulletControl } from "../../bullet/BulletControl";


/*** 单体对目标造成攻击力{0}伤害, 默认播放屏幕抖动和hit动画*/
export class PlayerSkill1Hit extends BulletOnHit {
    constructor(
        public playHitAnimName = 'skill_hit',
    ) { super() }
    process(bullet: BulletObj, target: cc.Node) {
        if (bullet.model.isCollision) return false;
        this.playSkillAttack(bullet, target)
        return this.playHitAnim(bullet, target)
    }

    playSkillAttack(bullet: BulletObj, target: cc.Node) {
        let attack = PropertCrtl.ins.getSkillAttack(Number(bullet.model.id))
        let attackInfo = PropertCrtl.ins.calculateAttackPower(attack)
        FightCtrl.ins.createDamage(bullet.caster, target, new Damage(attackInfo.resultAttack, 0, 0), []);
        FightCtrl.ins.createHertLb(attackInfo.resultAttack, attackInfo.hitType, target)
    }

    playHitAnim(bullet: BulletObj, target: cc.Node) {
        target.getComponent(CharacterControl).onHitEvent?.()
        FightCtrl.ins.shakeCamera()
        if (this.playHitAnimName != '') {
            bullet.selfNode.getComponent(CharacterControl).spine?.playAsync(this.playHitAnimName)
        }
        return false
    }

    get skillCfg() { return DataMgr.getInstance().skillCfg }
    getExtraTime(id: string) {
        return this.skillCfg[Number(id) - 1]?.extraTime ?? 0
    }
}
/**对目标造成攻击力{0}伤害，并减慢其{1}攻速，持续{2}秒*/
export class SlowSpeedAttackHit extends PlayerSkill1Hit {
    process(bullet: BulletObj, target: cc.Node) {
        if (bullet.model.isCollision) return false;
        if (!bullet.selfNode.getComponent(BulletControl).isCanHert) return false
        //攻击
        this.playSkillAttack(bullet, target)
        //造成减速
        let curValue = target.getComponent(CharacterControl).characterAttr.attackSpeed
        let calculateValue = PropertCrtl.ins.calculateEnemyAttackSpeed(target, Number(bullet.model.id), curValue)
        let i = 0
        let count = this.getExtraTime(bullet.model.id)
        let call = () => {
            FightCtrl.ins.refreshEnemyAttackColeTime(target, calculateValue)
            if (i >= count) {
                PropertCrtl.ins.removeEnemyAttackSpeedEffect(target)
                FightCtrl.ins.refreshEnemyAttackColeTime(target, curValue)
            }
            if (target.parent == null) {
                bullet.caster.getComponent(CharacterControl).unschedule(call)
            }
            i++
        }
        bullet.caster.getComponent(CharacterControl).schedule(call, 1, count)
        return this.playHitAnim(bullet, target)
    }
}
/** 对目标造成攻击力{0}伤害，并造成灼烧伤害（每秒扣除最大生命值{1}），持续{2}秒 */
export class MaxHpHit extends PlayerSkill1Hit {
    process(bullet: BulletObj, target: cc.Node) {
        if (bullet.model.isCollision) return false;
        if (!bullet.selfNode.getComponent(BulletControl).isCanHert) return false
        //攻击
        this.playSkillAttack(bullet, target)
        //造成灼烧伤害
        let calculateValue = PropertCrtl.ins.calculateEnemyBurnDamage(target, Number(bullet.model.id))
        let i = 0
        let count = this.getExtraTime(bullet.model.id) - 1
        bullet.caster.getComponent(CharacterControl).schedule(() => {
            FightCtrl.ins.createDamage(bullet.caster, target, new Damage(calculateValue, 0, 0), []);
            FightCtrl.ins.createHertLb(calculateValue, 1, target)
            if (i >= count) {
                PropertCrtl.ins.removeEnemyBurnDamage(target)
            }
            i++
        }, 1, count)
        return this.playHitAnim(bullet, target)
    }
}
/** 所受到的伤害减少{0}，持续{2}秒 */
export class ReducedDamageHit extends PlayerSkill1Hit {
    process(bullet: BulletObj, target: cc.Node) {
        if (bullet.model.isCollision) return false;
        if (!bullet.selfNode.getComponent(BulletControl).isCanHert) return false

        PropertCrtl.ins.calculateSufferDamageReduce(target, Number(bullet.model.id))
        UIUtils.scheduleOnce(() => {
            PropertCrtl.ins.removeDamageReduce(target)
        }, target, this.getExtraTime(bullet.model.id))
        FightCtrl.ins.shakeCamera()
        return false
    }
}
/**对全体目标造成攻击力{0}伤害，并提升自身伤害{1}，持续{2}秒*/
export class DamageIncreaseHit extends PlayerSkill1Hit {
    process(bullet: BulletObj, target: cc.Node) {
        if (bullet.model.isCollision) return false;
        if (!bullet.selfNode.getComponent(BulletControl).isCanHert) return false
        //攻击
        this.playSkillAttack(bullet, target)
        //提升伤害
        PropertCrtl.ins.calculateEnemyAttackUp(target, Number(bullet.model.id))
        bullet.caster.getComponent(CharacterControl).scheduleOnce(() => {
            PropertCrtl.ins.removeDamageUp(target)
        }, this.getExtraTime(bullet.model.id))

        return this.playHitAnim(bullet, target)
    }
}

