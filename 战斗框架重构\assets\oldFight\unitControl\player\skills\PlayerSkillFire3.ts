import FightCtrl from "../../../../ctrl/FightCtrl";
import { UtilsExtends } from "../../../../util/UtilsExtends";
import { TimelineEvent, TimelineObj, TimelineNode, TimelineModel } from "../../../timeline/TimelineObj";
import { BulletLauncher, BulletModel } from "../../bullet/BulletObj";
import { Skill, SkillModel } from "../../CharacterSkillObj";
import { SkillNames } from "./SkillNames";
import { BulletTweenForwardFireArrowPlayer } from "../bulletTween/BulletTweenForwardFireArrowPlayer";
import { PlayerSkill1Hit } from "../bulletOnHit/PlayerSkillNormalHit";
import { AudioMgr } from "../../../../../../scripts/framework/manager/AudioMgr";
import { AudioId } from "../../../../../../scripts/game/config/Config";
import { DataMgr } from "../../../../../../scripts/game/manager/DataMgr";

class FireBulletEvent extends TimelineEvent {
    constructor(
        public launcher: BulletLauncher,
    ) { super() }
    doEvent(timelineObj: TimelineObj, index: number): void {
        let target = timelineObj.targets[index]
        if (!timelineObj.targets[index]) return

        // this.launcher.fireWorldPosition.x = target.convertToWorldSpaceAR(cc.Vec3.ZERO).x // 敌人的位置
        this.launcher.fireWorldPosition = FightCtrl.ins.skillStartPoint.convertToWorldSpaceAR(cc.Vec3.ZERO)  //技能从天降
        this.launcher.caster = timelineObj.caster;

        this.playEffect(DataMgr.getInstance().skillCfg[Number(this.launcher.model.id) - 1].sound)
        let bulletCom = FightCtrl.ins.createBullet(this.launcher, target, `partner/buddy_3_vfx/buddy_3_vfx`, 'skill_bullet', true);
        // bulletCom.bullet.targetWordPos = cc.v3(FightCtrl.ins.enemyArrivalWordPoint.x + 150, FightCtrl.ins.enemyStartWordPoint.y) //设置固定终点
        bulletCom.onCollisionEvent = () => {
            timelineObj.targets.forEach(t => bulletCom.playHit(t))
            bulletCom.isCanHert = false
            bulletCom.bullet.model.isCollision = true
            bulletCom.onCollisionEvent = null
        }
        bulletCom.isNeedCollisionStop = false
    }
}
/**即将爆炸的榴莲炸弹掉落，对随机3个目标造成攻击力{0}伤害*/
export class PlayerSkillFire3 extends Skill {
    constructor(
        public prefabKey: string,
    ) {
        super(SkillNames.playerSKill3);
        const launcher = new BulletLauncher();
        launcher.speed = 20
        launcher.model = new BulletModel("3", this.prefabKey);
        launcher.model.lifeDuration = 3
        launcher.model.tween = new BulletTweenForwardFireArrowPlayer();
        launcher.model.onHit = new PlayerSkill1Hit();
        this.skillMode = {
            name: this.name,
            effect: {
                id: UtilsExtends.generateUniqueId(),
                name: this.name,
                lifeDuration: 0.1,
                nodes: [
                    {
                        timeStartPoint: 0.01,
                        event: new FireBulletEvent(launcher)
                    } as TimelineNode,
                ]
            } as TimelineModel,
            buffs: [],
            coldTime: 0.5,
        }
    }
}
