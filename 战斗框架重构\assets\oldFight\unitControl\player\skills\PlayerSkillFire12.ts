import FightCtrl from "../../../../ctrl/FightCtrl";
import { UtilsExtends } from "../../../../util/UtilsExtends";
import { TimelineEvent, TimelineObj, TimelineNode, TimelineModel } from "../../../timeline/TimelineObj";
import { BulletLauncher, BulletModel } from "../../bullet/BulletObj";
import { Skill, SkillModel } from "../../CharacterSkillObj";
import { CharacterControl } from "../../CharacterControl";
import { PlayerBulletHitCommon } from "../bulletOnHit/PlayerBulletHitCommon";
import { SkillNames } from "./SkillNames";
import { BulletTweenForwardFireArrowPlayer } from "../bulletTween/BulletTweenForwardFireArrowPlayer";
import { PlayerSkill1Hit } from "../bulletOnHit/PlayerSkillNormalHit";
import { AudioMgr } from "../../../../../../scripts/framework/manager/AudioMgr";
import { AudioId } from "../../../../../../scripts/game/config/Config";
import { DataMgr } from "../../../../../../scripts/game/manager/DataMgr";

class FireBulletEvent extends TimelineEvent {
    constructor(
        public launcher: BulletLauncher,
    ) { super() }
    doEvent(timelineObj: TimelineObj, index: number): void {
        let target = timelineObj.targets[index]
        if (!timelineObj.targets[index]) return

        this.launcher.fireWorldPosition = FightCtrl.ins.skillStartPoint.convertToWorldSpaceAR(cc.Vec3.ZERO)
        this.launcher.caster = timelineObj.caster;
        
        this.playEffect(DataMgr.getInstance().skillCfg[Number(this.launcher.model.id) - 1].sound)
        let bulletCom = FightCtrl.ins.createBullet(this.launcher, null, `partner/buddy_12_vfx/buddy_12_vfx`, 'skill_bullet', false);
        bulletCom.bullet.targetWordPos = cc.v3(FightCtrl.ins.enemyArrivalWordPoint.x + 150, FightCtrl.ins.enemyStartWordPoint.y)
        bulletCom.onCollisionEvent = () => {
            timelineObj.targets.forEach(t => bulletCom.playHit(t))
            bulletCom.isCanHert = false
            bulletCom.onCollisionEvent = null
            bulletCom.isNeedCalculateRotation = false
            bulletCom.isNeedCalculatePos = false
            bulletCom.node.angle = 0
        }
        bulletCom.isNeedCollisionStop = false;
    }
}
/**向前抛掷神奇的白菜，对全体目标造成攻击力{0}伤害*/
export class PlayerSkillFire12 extends Skill {
    constructor(
        public prefabKey: string,
    ) {
        super(SkillNames.playerSKill12);
        this.skillMode = {
            name: this.name,
            effect: {
                id: UtilsExtends.generateUniqueId(),
                name: this.name,
                lifeDuration: 0.3,
                nodes: [this.getTimeNode()]
            } as TimelineModel,
            buffs: [],
            coldTime: 0.5,
            // onAdd: (skillObj, targets) => {
            //     skillObj.model.effect.nodes = []
            //     targets.forEach(t => skillObj.model.effect.nodes.push(this.getTimeNode()))
            // }
        }
    }

    getLauncher() {
        const launcher = new BulletLauncher();
        launcher.speed = 20
        launcher.model = new BulletModel("12", this.prefabKey);
        launcher.model.tween = new BulletTweenForwardFireArrowPlayer();
        launcher.model.onHit = new PlayerSkill1Hit();
        launcher.model.lifeDuration = 4
        return launcher
    }

    getTimeNode() {
        return {
            timeStartPoint: 0.01,
            event: new FireBulletEvent(this.getLauncher())
        } as TimelineNode
    }
}
