// import BuffManager from '../../Controller/BuffManager';
// import SkillBase from './SkillBase';
// import Base from './Base';

// /**buff类型 */
// export enum EBuffType {
//     None = `None`,
// }

// declare global {
//     /**buff叠加类型 */
//     export interface IBuffTimeType {
//         /**类型 */
//         type: EBuffType;
//         value: number;
//     }
//     /**buff数据接口 */
//     export interface IBuffDataType extends IBaseDataType {
//         /**buff 的类名 */
//         type: string;
//         /**Buff等级 */
//         level: number;
//         /**buff生效时间类型 */
//         buffTime: IBuffTimeType;
//         /**是否在生效中 */
//         isTakeEffect: boolean;
//         /**是否可以叠加 */
//         isCanOverlay: boolean;
//         /**最大叠加几层 */
//         maxFloor: number;
//         /**当前叠加层数*/
//         nowFloor: number;
//     }
// }

// /** 
//  * @features : 游戏技能的buff
//  * @description : 针对游戏技能的Buff基类， 所有技能buff都将继承该类 ，并且该类及子类不可挂载到场景中
//  * @Date : 2020-08-12 23:28:43
//  * <AUTHOR> judu233(<EMAIL>)
//  * @LastEditTime : 2023-11-14 14:01:01
//  * @LastEditors : judu233
//  */
// export default class BuffBase extends Base {
//     /**技能数据 */
//     data: IBuffDataType;
//     /**BUFF作用的目标 */
//     target: any;
//     /**buffManager */
//     buffManage = new BuffManager

//     /**buff的Id */
//     @Base.ViewLinked
//     id: string

//     /**buff的类型 */
//     @Base.ViewLinked
//     type: string

//     /**使用buff ` 子类负责实现具体逻辑*/
//     useBuff() { }
//     /**移除Buff 子类负责实现具体逻辑*/
//     removeBuff() { }
//     /**叠加一层buff 子类负责实现具体逻辑*/
//     overlayBuff() { }
//     /**减少一层buff */
//     reduceBuff() { }
// }
