import FightCtrl from "../../../../ctrl/FightCtrl";
import { UtilsExtends } from "../../../../util/UtilsExtends";
import { TimelineEvent, TimelineObj, TimelineNode, TimelineModel } from "../../../timeline/TimelineObj";
import { BulletTweenFollowTarget } from "../bulletTween/BulletTweenFollowTarget";
import { BulletLauncher, BulletModel } from "../../bullet/BulletObj";
import { SkillModel } from "../../CharacterSkillObj";
import { CharacterControl } from "../../CharacterControl";
import { PlayerBulletHitCommon } from "../bulletOnHit/PlayerBulletHitCommon";
import { SkillNames } from "./SkillNames";

class FireBulletEvent extends TimelineEvent {
    constructor(
        public launcher: BulletLauncher
    ) {
        super()
    }
    doEvent(timelineObj: TimelineObj): void {
        // 计算枪口的位置
        let muzzleNode = timelineObj.caster.getComponent(CharacterControl).fireNode ?? timelineObj.caster.getChildByName('Muzzle') ?? timelineObj.caster
        this.launcher.fireWorldPosition = muzzleNode.convertToWorldSpaceAR(cc.Vec3.ZERO)
        this.launcher.caster = timelineObj.caster;
        FightCtrl.ins.createBullet(this.launcher, timelineObj.target)
    }
}

/**追踪弹技能 */
export class SkillModelFollowFire extends SkillModel {
    constructor(
        public prefabKey: string
    ) {
        super();
        this.name = SkillNames.skillTest
        this.effect = {
            id: UtilsExtends.generateUniqueId(),
            name: this.name,
            lifeDuration: 1,
            nodes: [
                {
                    timeStartPoint: 0.1,
                    event: new FireBulletEvent(this.createBulletLauncher())
                } as TimelineNode
            ]
        } as TimelineModel;
        this.coldTime = 0.3;
    }

    createBulletLauncher() {
        const launcher = new BulletLauncher();
        launcher.caster = null;
        launcher.speed = 10;
        launcher.model = new BulletModel("bullet_0", this.prefabKey);
        // 追踪
        launcher.model.tween = new BulletTweenFollowTarget();
        launcher.model.lifeDuration = 5;
        launcher.model.onHit = new PlayerBulletHitCommon();
        return launcher;
    }

}