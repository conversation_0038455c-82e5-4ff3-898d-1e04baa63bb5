import { CharacterControl } from "../../CharacterControl";
import { BulletObj, BulletOnHit } from "../../bullet/BulletObj";
import { Damage } from "../../damage/DamageInfo";
import FightCtrl from "../../../../ctrl/FightCtrl";
import { UtilsExtends } from "../../../../util/UtilsExtends";
import PropertCrtl from "../../../../ctrl/PropertCrtl";
import { BulletControl } from "../../bullet/BulletControl";

/**
 * 直接生成一个伤害，没有其他效果
 */
export class PlayerBulletHitCommon extends BulletOnHit {
    process(bullet: BulletObj, target: cc.Node) {
        if (bullet.model.isCollision) return false;
        if (bullet.selfNode.getComponent(BulletControl).isCanHert) {
            //计算触发伤害
            let attackValue = PropertCrtl.ins.getPlayerAllPower()
            let attackInfo = PropertCrtl.ins.calculateAttackPower(attackValue)
            attackValue = attackInfo.resultAttack
            //对敌人扣血
            FightCtrl.ins.createDamage(bullet.caster, target, new Damage(attackValue, 0, 0), []);
            //创建伤害数字
            FightCtrl.ins.createHertLb(attackValue, attackInfo.hitType, target)
        }
        //敌人伤害闪白
        bullet.target.getComponent(CharacterControl).onHitEvent?.()
        //播放子弹击中的动画
        bullet.selfNode.getComponent(CharacterControl).spine?.playAsync('hit').then(() => {
            bullet.model.hitTimes -= 1;
        })
        //抖动屏幕
        FightCtrl.ins.shakeCamera()
        return false
    }

}