import { AddBuffInfo } from "../CharacteBuffObj";

export class DamageInfo {
    /** 暴击率、命中率，伤害度  */
    /** 伤害过后，给角色添加的buff  */
    addBuffs: Array<AddBuffInfo> = new Array<AddBuffInfo>();
    /** 从策划脚本获得最终的伤害值 */
    constructor(
        /** 造成伤害的攻击者，当然可以是null的  */
        public attacker: cc.Node,
        /** 造成攻击伤害的受击者，这个必须有  */
        public defender: cc.Node,
        /** 伤害类型  */
        /** 伤害值，其实伤害值是多元的，通常游戏都会有多个属性伤害，所以会用一个struct，否则就会是一个int   */
        public damage: Damage,
        tags: Array<DamageInfoTag>
    ) { }
    /** 计算最终伤害，暴击率等等 */
    damageValue() {
        // bool isCrit = Random.Range(0.00f, 1.00f) <= damageInfo.criticalRate;
        // return Mathf.CeilToInt(damageInfo.damage.Overall(asHeal) * (isCrit == true ? 1.80f:1.00f));  //暴击1.8倍
        return this.damage.physicsDamage;
    }
}
/** 游戏中伤害值的struct，负数为加血 */
export class Damage {
    constructor(
        /** 物理伤害 */
        public physicsDamage: number,
        /** 魔法伤害 */
        public maginDamage: number,
        /** 扣蓝 */
        public mpDamage: number
    ) { }
}
/** 伤害类型的Tag元素，因为DamageInfo的逻辑需要的严谨性远高于其他的元素，所以伤害类型应该是枚举数组的 */
export enum DamageInfoTag {
    directDamage = 0,   //直接伤害
    periodDamage = 1,   //间歇性伤害
    reflectDamage = 2,  //反噬伤害
    directHeal = 10,    //直接治疗
    periodHeal = 11,    //间歇性治疗
    monkeyDamage = 9999    //这个类型的伤害在目前这个demo中没有意义，只是告诉你可以随意扩展，仅仅比string严肃些。
}