import { AudioMgr } from "../../../../../scripts/framework/manager/AudioMgr"
import { ResourceMgr } from "../../../../../scripts/framework/manager/ResourceMgr"
import { AudioId } from "../../../../../scripts/game/config/Config"
import { DataMgr } from "../../../../../scripts/game/manager/DataMgr"
import FightCtrl from "../../../ctrl/FightCtrl"
import { UtilsExtends } from "../../../util/UtilsExtends"
import { TimelineEvent, TimelineObj, TimelineNode, TimelineModel } from "../../timeline/TimelineObj"
import { BulletLauncher, BulletModel } from "../bullet/BulletObj"
import { CharacterControl } from "../CharacterControl"
import { Skill, SkillModel } from "../CharacterSkillObj"
import { BulletTweenForwardFireArrowPlayer } from "../player/bulletTween/BulletTweenForwardFireArrowPlayer"
import { SkillNames } from "../player/skills/SkillNames"
import { PartnerBulletHitCommon } from "./PartnerBulletHitCommon"


class FireBulletEvent extends TimelineEvent {
    constructor(
        public launcher: BulletLauncher,
    ) { super() }
    doEvent(timelineObj: TimelineObj): void {
        let casterCharacter = timelineObj.caster.getComponent(CharacterControl)
        this.launcher.fireWorldPosition = casterCharacter.fireNode.convertToWorldSpaceAR(cc.Vec3.ZERO)
        this.launcher.caster = timelineObj.caster;

        this.playEffect(DataMgr.getInstance().data.petCfg[Number(this.launcher.model.id) - 1].soundPet)
        casterCharacter.playAttack(false, (trackEntry: sp.spine.TrackEntry, event: sp.spine.Event) => {
            if (event.data.name == 'attack') {
                let bulletCom = FightCtrl.ins.createBullet(this.launcher, timelineObj.target, `partner/buddy_${this.launcher.model.id}_vfx/buddy_${this.launcher.model.id}_vfx`);
                bulletCom.bullet.model.isRecycleWhenNoTarget = true
            }
        }).then(() => {
            casterCharacter.playIdle(true)
        })
    }
}

export class PartnerSkillMode extends SkillModel {
    buffs = []
    coldTime = 2
    replaceSKillBullet(id: number) {
        let fireBulletEvent = this.effect.nodes[0].event as FireBulletEvent
        if (id != Number(fireBulletEvent.launcher.model.id)) {
            fireBulletEvent.launcher.model.id = id.toString()
        }
    }
    changeColdTime(time: number) {
        this.coldTime = time
    }
}

export class PartnerSkillFire extends Skill {
    constructor(
        /**子弹预制体path */
        public prefabKey: string,
        /**子弹spineId */
        public spineId: string
    ) {
        super(SkillNames.petAttack)
        const launcher = new BulletLauncher();
        launcher.caster = null;
        launcher.speed = 10
        launcher.model = new BulletModel(this.spineId, this.prefabKey);
        launcher.model.tween = new BulletTweenForwardFireArrowPlayer();
        launcher.model.lifeDuration = 5;
        launcher.model.onHit = new PartnerBulletHitCommon();
        this.skillMode = new PartnerSkillMode()
        this.skillMode.name = this.name
        this.skillMode.effect = {
            id: UtilsExtends.generateUniqueId(),
            name: this.name,
            lifeDuration: 0.1,
            nodes: [
                {
                    timeStartPoint: 0.01,
                    event: new FireBulletEvent(launcher)
                } as TimelineNode,
            ]
        }
    }
}