import { UtilsExtends } from "../../util/UtilsExtends";

const { ccclass, property } = cc._decorator;

export class CharacterAttributes {
    id: number;
    hp: number = 100;
    hpUp = 100
    hpRecovery = 0
    attack: number = 10;
    attackRange: number = 500;
    defense: number = 5;
    attackSpeed: number = 1;
    moveSpeed = 1
    level: number = 1;
    constructor() {
        this.id = UtilsExtends.generateUniqueId();
    }
}

/** 角色的资源类属性，比如hp，mp等都属于这个 */
export class CharacterResource {
    constructor(
        /**当前生命 */
        public hp: number,
        /**当前弹药量 */
        public ammo: number = 0,
        /**当前耐力，耐力是一个百分比消耗，实时恢复的概念，所以上限按规则就是100了，这里是现有多少 */
        public stamina: number = 0
    ) { }
}
export enum CharacterRole {
    INVALID = -1,
    HERO = 0,
    ENEMY = 1,
    BULLET = 2,
    PARTNER = 3,
}
/*** 初始化角色的信息*/
export interface CharacterAddInfo {
    /**要加载的预制体key */
    prefabKey: string | cc.Node
    /**  角色，阵营 */
    role: CharacterRole;
    /** 角色名字 */
    roleName: string;
    /**  添加位置 */
    worldPos?: cc.Vec3;
    /**添加的父节点位置 */
    parent?: cc.Node
}

