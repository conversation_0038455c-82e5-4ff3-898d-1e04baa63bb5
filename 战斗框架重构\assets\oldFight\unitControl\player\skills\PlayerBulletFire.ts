import FightCtrl from "../../../../ctrl/FightCtrl";
import { UtilsExtends } from "../../../../util/UtilsExtends";
import { TimelineEvent, TimelineObj, TimelineNode, TimelineModel } from "../../../timeline/TimelineObj";
import { BulletLauncher, BulletModel } from "../../bullet/BulletObj";
import { Skill, SkillModel } from "../../CharacterSkillObj";
import { CharacterControl } from "../../CharacterControl";
import { PlayerBulletHitCommon } from "../bulletOnHit/PlayerBulletHitCommon";
import { SkillNames } from "./SkillNames";
import { BulletTweenForwardFireArrowPlayer } from "../bulletTween/BulletTweenForwardFireArrowPlayer";
import { AudioMgr } from "../../../../../../scripts/framework/manager/AudioMgr";
import { AudioId } from "../../../../../../scripts/game/config/Config";
import { DataMgr } from "../../../../../../scripts/game/manager/DataMgr";
import { PlayerMgr } from "../../../../../../scripts/game/manager/PlayerMgr";

class FireBulletEvent extends TimelineEvent {
    constructor(
        public launcher: BulletLauncher,
    ) { super() }
    himPosTween: cc.Tween<any>
    doEvent(timelineObj: TimelineObj): void {
        let casterCharacter = timelineObj.caster.getComponent(CharacterControl)
        // this.himPosTween?.stop()
        if (!timelineObj.target || casterCharacter.isDead) {
            return
        }

        let targetWordPoint = timelineObj.target.convertToWorldSpaceAR(cc.Vec3.ZERO)
        let playerLoclTargetPoint = casterCharacter.spine.node.convertToNodeSpaceAR(targetWordPoint)
        let himBone = casterCharacter.spine.findBone('him')
        himBone.parent.worldToLocal(new sp.spine.Vector2(playerLoclTargetPoint.x, playerLoclTargetPoint.y))

        this.himPosTween = FightCtrl.ins.playHimBoneSpine(cc.v2(playerLoclTargetPoint.x, cc.misc.clampf(playerLoclTargetPoint.y, -100, 500) + 20), 0.15)
            .call(() => {
                //播放音效
                let id = PlayerMgr.getInstance().getWeaponData().curGunHeightWeapon?.level ?? 1
                this.playEffect(DataMgr.getInstance().gunCfgObj[id].sound)
                if (casterCharacter.spine.animation == casterCharacter.moveName) {
                    casterCharacter.spine.clearTracks()
                    casterCharacter.spine.setToSetupPose()
                }
                // if (casterCharacter.isDead) return
                this.launcher.caster = timelineObj.caster;
                this.launcher.fireWorldPosition = casterCharacter.fireNode.convertToWorldSpaceAR(cc.Vec3.ZERO)

                let eventCount = casterCharacter.spine.skeletonData.skeletonJson.animations[casterCharacter.attackSpineName].events.length
                let curEventIndex = 0
                let isCanHert = true
                casterCharacter.playAttack(false, (trackEntry: sp.spine.TrackEntry, event: sp.spine.Event) => {
                    if (event.data.name == 'attack' && FightCtrl.ins.isRun) {
                        let bulletCom = FightCtrl.ins.createBullet(this.launcher, timelineObj.target);
                        bulletCom.isCanHert = isCanHert
                        isCanHert = false
                        bulletCom.bullet.model.isRecycleWhenNoTarget = true
                        curEventIndex++
                        if (eventCount == curEventIndex) {
                            casterCharacter.playIdle(true)
                            //如果攻击间隔时间大于0.4结束，就回弹，然后角度变回去
                            if (casterCharacter.getSkillById(SkillNames.playerAttack).model.coldTime > 0.4) {
                                this.himPosTween = FightCtrl.ins.playHimBoneSpine(null, 0.35)
                            }
                        }
                    }
                })
            })
            .start()
    }

}
/**普通子弹攻击 */
export class PlayerBulletFire extends Skill {
    constructor(
        public prefabKey: string,
    ) {
        super(SkillNames.playerAttack)
        const launcher = new BulletLauncher();
        launcher.speed = DataMgr.getInstance().basicParaCfgObj['bulletSpeed']
        launcher.model = new BulletModel("bullet_0", this.prefabKey);
        launcher.model.tween = new BulletTweenForwardFireArrowPlayer();
        launcher.model.lifeDuration = 4
        launcher.model.onHit = new PlayerBulletHitCommon();
        this.skillMode = {
            name: this.name,
            effect: {
                id: UtilsExtends.generateUniqueId(),
                name: this.name,
                lifeDuration: 0.1,
                nodes: [
                    {
                        timeStartPoint: 0.01,
                        event: new FireBulletEvent(launcher)
                    } as TimelineNode,
                ]
            } as TimelineModel,
            buffs: [],
            coldTime: 0.6,
        }
    }
}


// class RotateNodeEvent extends TimelineEvent {
//     constructor(public duration: number) {
//         super()
//     }
//     doEvent(timelineObj: TimelineObj): void {
//         console.log("准备施法 ", timelineObj.caster.name);
//         const eulerAngles = timelineObj.caster.eulerAngles.clone();
//         const eulerAngles1 = eulerAngles.clone();
//         eulerAngles1.z += 180;

//         // 旋转一圈
//         cc.tween(timelineObj.caster)
//             .to(this.duration / 2, { eulerAngles: eulerAngles1 })
//             .to(this.duration / 2, { eulerAngles })
//             .start()
//     }
// }