
// import FightCtrl from '../../../../ctrl/FightCtrl';
// import { UtilsExtends } from '../../../../util/UtilsExtends';
// import { BulletLauncher, BulletObj, BulletTween } from '../../bullet/BulletObj';
// import { CharacterControl } from '../../CharacterControl';

// /** 追踪弹,
//  *  效果：时刻计算朝向目标的位置
//  */
// export class BulletTweenFollowTarget extends BulletTween {
//     /** 保持发射速度的时间，单位s */
//     public keepShootDirTime: number = 0.1;
//     /** 控制转向的速度 [0~1], 1可以实现瞬间转向， 可以做到弧形转向 */
//     public trunScale: number = 0.1;
//     /** auto find target */
//     public autoFindTarget: boolean = false;
//     /** flolow type, 始终第一个，最后一个，中间的，距离最近的。 或者选定了不变 */
//     private lastV: cc.Vec3 = undefined;

//     process(t: number, obj: BulletObj, lunch: BulletLauncher): cc.Vec3 {
//         if (t === 0) {
//             this.lastV = obj.caster.up;
//         }
//         // 前面保持发射角度
//         if (t < this.keepShootDirTime) {
//             return this.lastV;
//         }
//         let newDir: cc.Vec3 = null;
//         if (obj.selfNode) {
//             // 避免 node已经被移除了,所以检查parent
//             if (obj.target && obj.target.parent) {
//                 // 指定目标
//                 newDir = obj.target.convertToWorldSpaceAR(cc.Vec3.ZERO).subtract(obj.selfNode.convertToWorldSpaceAR(cc.Vec3.ZERO)).normalize();
//             } else if (this.autoFindTarget) {
//                 // 自动找目标
//                 const casterChar = obj.caster.getComponent(CharacterControl);
//                 if (casterChar) {
//                     const enemies = FightCtrl.ins.getCharacters(casterChar.role);
//                     if (enemies.length > 0 && enemies[0]?.node?.getParent()) {
//                         const enemy = enemies[0];
//                         newDir = enemy.node.convertToWorldSpaceAR(cc.Vec3.ZERO).subtract(obj.selfNode.convertToWorldSpaceAR(cc.Vec3.ZERO)).normalize()
//                     }
//                 }
//             }
//         }
//         if (this.lastV) {
//             if (newDir) {
//                 //调整角度 从上一个角度旋转到下一个角度的插值， this.trunScale控制插值时间
//                 this.lastV = UtilsExtends.slerp(this.lastV.clone(), newDir, this.lastV, this.trunScale);
//                 this.lastV = this.lastV.normalize()
//                 // const offset = newDir.subtract(this.lastV).multiplyScalar(this.trunScale);
//                 // this.lastV = this.lastV.add(offset);
//                 // console.log(offset, this.lastV)
//             } else {
//                 return this.lastV;
//             }
//             return this.lastV;
//         }
//         return cc.Vec3.UP;
//     }
// }
