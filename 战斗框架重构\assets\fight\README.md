# 战斗系统重构版本

这是一个完全重构的战斗系统，解决了原有系统的问题，提供了清晰的架构和强大的扩展性。

## 主要特性

### ✅ 已完成功能

1. **清晰的架构设计**
   - 基于接口的模块化设计
   - 职责分离，易于维护
   - TypeScript类型安全

2. **Timeline系统**
   - 基于时间轴的技能效果系统
   - 支持复杂的技能时序控制
   - 可扩展的事件类型

3. **属性系统**
   - 修改器模式，支持Buff叠加
   - 自动属性重新计算
   - 事件驱动的属性变化通知

4. **子弹系统**
   - 多种弹道类型（直线、抛物线、追踪）
   - 碰撞检测和命中处理
   - 可配置的视觉和音效

5. **技能系统**
   - 基于Timeline的技能实现
   - 资源消耗检查（MP、耐力）
   - 冷却时间管理

6. **Buff系统**
   - 属性修改器集成
   - 自动过期处理
   - 事件触发机制

## 快速开始

### 1. 创建角色

```typescript
import { Character } from "./characters/Character";
import { CharacterRole, CharacterCreateInfo } from "./core/types/CharacterTypes";

// 创建角色节点
const characterNode = new cc.Node("Player");
const character = characterNode.addComponent(Character);

// 设置角色数据
const characterData: CharacterCreateInfo = {
    prefabKey: "player_prefab",
    role: CharacterRole.HERO,
    name: "勇者",
    worldPosition: cc.v3(0, 0, 0),
    initialAttributes: {
        hp: 1000,
        maxHp: 1000,
        mp: 200,
        maxMp: 200,
        attack: 100,
        defense: 50,
        // ... 其他属性
    }
};

character.setCharacterData(characterData);
```

### 2. 学习和使用技能

```typescript
import { PlayerSkillFire1 } from "./skills/PlayerSkillFire1";

// 创建技能实例
const fireSkill = new PlayerSkillFire1();

// 学习技能
character.learnSkill(fireSkill);

// 释放技能
character.castSkill("player_skill_fire1", target);
```

### 3. 添加Buff

```typescript
import { BuffModelBeHurtFight } from "./buffs/BuffModelBeHurtFight";

// 创建Buff实例
const counterBuff = new BuffModelBeHurtFight(caster, target);

// 添加Buff
character.addBuff(counterBuff);
```

### 4. 初始化Timeline管理器

```typescript
import { TimelineManager } from "./systems/timeline/TimelineManager";

// 获取Timeline管理器实例
const timelineManager = TimelineManager.getInstance();

// 在游戏循环中更新
update(deltaTime: number) {
    timelineManager.update(deltaTime);
}
```

## 系统架构

```
fight/
├── core/                    # 核心架构层
│   ├── interfaces/         # 接口定义
│   ├── types/             # 类型定义
│   ├── base/              # 基础类
│   └── managers/          # 管理器
├── systems/               # 系统实现层
│   ├── timeline/          # Timeline系统
│   └── bullet/            # 子弹系统
├── characters/            # 角色实现
├── actions/               # 动作系统
├── skills/                # 技能实现
├── buffs/                 # Buff实现
└── examples/              # 使用示例
```

## 核心概念

### Timeline系统

Timeline系统是技能效果的核心，允许你精确控制技能的时序：

```typescript
// 0.0秒：播放施法动画
// 0.1秒：播放施法音效
// 0.5秒：发射火球
// 1.0秒：播放命中特效
```

### 属性修改器

Buff通过属性修改器影响角色属性，确保属性变化时自动更新：

```typescript
// Buff会自动：
// 1. 增加攻击力（每层10%）
// 2. 增加暴击率（每层5%）
// 3. 属性变化时自动通知系统更新
```

### 事件驱动

系统使用事件驱动设计，降低组件间的耦合：

```typescript
character.setEvents({
    onDeath: (character) => console.log(`${character.name} 死亡了！`),
    onTakeDamage: (character, damage, attacker) => {
        console.log(`${character.name} 受到了 ${damage} 点伤害`);
    }
});
```

## 测试和示例

### 运行测试场景

1. 将 `BattleTestScene.ts` 添加到场景中
2. 设置UI节点引用
3. 运行场景并点击按钮测试功能

### 完整示例

查看 `examples/UsageExample.ts` 了解完整的使用示例。

## 扩展指南

### 创建新技能

1. 继承 `ISkill` 接口
2. 实现技能逻辑
3. 创建Timeline配置
4. 添加技能效果

### 创建新Buff

1. 继承 `IBuff` 接口
2. 实现Buff效果
3. 配置属性修改器
4. 处理事件触发

### 添加新的Timeline事件

1. 继承 `TimelineEvent` 基类
2. 实现 `execute` 方法
3. 在技能中使用新事件

## 注意事项

1. **性能考虑**：Timeline管理器会在每帧更新，注意性能影响
2. **内存管理**：确保正确清理事件监听器和管理器
3. **类型安全**：充分利用TypeScript的类型检查
4. **调试支持**：所有管理器都提供了调试信息接口

## 后续计划

- [ ] 完善子弹系统的物理碰撞
- [ ] 添加更多技能和Buff示例
- [ ] 实现AI系统集成
- [ ] 添加单元测试
- [ ] 性能优化和内存管理优化
