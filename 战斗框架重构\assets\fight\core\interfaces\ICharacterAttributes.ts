/**
 * 角色属性接口定义
 */

/**
 * 角色基础属性接口
 */
export interface ICharacterAttributes {
    /** 当前生命值 */
    readonly currentHp: number;

    /** 最大生命值 */
    readonly maxHp: number;

    /** 攻击力 */
    readonly attack: number;

    /** 防御力 */
    readonly defense: number;

    /** 攻击速度 */
    readonly attackSpeed: number;

    /** 移动速度 */
    readonly moveSpeed: number;

    /** 攻击范围 */
    readonly attackRange: number;

    /** 等级 */
    readonly level: number;

    /**
     * 修改生命值
     * @param amount 修改量（正数为治疗，负数为伤害）
     */
    modifyHp(amount: number): void;

    /**
     * 获取生命值百分比
     * @returns 生命值百分比 (0-1)
     */
    getHpPercentage(): number;

    /**
     * 是否死亡
     * @returns 是否死亡
     */
    isDead(): boolean;

    /**
     * 重置到满血状态
     */
    resetToFull(): void;

    /**
     * 添加属性修改器
     * @param modifier 属性修改器
     */
    addModifier(modifier: IAttributeModifier): void;

    /**
     * 移除属性修改器
     * @param modifierId 修改器ID
     */
    removeModifier(modifierId: string): void;

    /**
     * 获取当前属性值
     * @param attributeName 属性名称
     * @returns 属性值
     */
    getCurrentAttributeValue(attributeName: string): number;

    /**
     * 设置属性值（用于修改器）
     * @param attributeName 属性名称
     * @param value 属性值
     */
    setAttributeValue(attributeName: string, value: number): void;
}

/**
 * 角色资源接口（MP、耐力等）
 */
export interface ICharacterResource {
    /** 当前魔法值 */
    readonly currentMp: number;

    /** 最大魔法值 */
    readonly maxMp: number;

    /** 当前耐力 */
    readonly currentStamina: number;

    /** 最大耐力 */
    readonly maxStamina: number;

    /**
     * 修改魔法值
     * @param amount 修改量
     */
    modifyMp(amount: number): void;

    /**
     * 修改耐力
     * @param amount 修改量
     */
    modifyStamina(amount: number): void;

    /**
     * 检查是否有足够的魔法值
     * @param amount 需要的魔法值
     * @returns 是否足够
     */
    hasEnoughMp(amount: number): boolean;

    /**
     * 检查是否有足够的耐力
     * @param amount 需要的耐力
     * @returns 是否足够
     */
    hasEnoughStamina(amount: number): boolean;

    /**
     * 获取魔法值百分比
     * @returns 魔法值百分比 (0-1)
     */
    getMpPercentage(): number;

    /**
     * 获取耐力百分比
     * @returns 耐力百分比 (0-1)
     */
    getStaminaPercentage(): number;

    /**
     * 重置资源到满值状态
     */
    resetResourcesToFull(): void;
}

/**
 * 角色属性修改器接口
 */
export interface IAttributeModifier {
    /** 修改器ID */
    readonly id: string;

    /** 修改器名称 */
    readonly name: string;

    /** 修改器类型 */
    readonly type: AttributeModifierType;

    /** 修改值 */
    readonly value: number;

    /** 持续时间（秒，-1表示永久） */
    readonly duration: number;

    /** 剩余时间 */
    remainingTime: number;

    /**
     * 应用修改器到属性
     * @param attributes 目标属性
     */
    apply(attributes: ICharacterAttributes): void;

    /**
     * 移除修改器效果
     * @param attributes 目标属性
     */
    remove(attributes: ICharacterAttributes): void;

    /**
     * 更新修改器
     * @param deltaTime 时间间隔
     * @returns 是否已过期
     */
    update(deltaTime: number): boolean;
}

/**
 * 属性修改器类型
 */
export enum AttributeModifierType {
    /** 加法修改 */
    ADD = "add",

    /** 乘法修改 */
    MULTIPLY = "multiply",

    /** 百分比修改 */
    PERCENTAGE = "percentage"
}

/**
 * 属性变化事件
 */
export interface IAttributeChangeEvent {
    /** 属性名称 */
    attributeName: string;

    /** 旧值 */
    oldValue: number;

    /** 新值 */
    newValue: number;

    /** 变化量 */
    delta: number;

    /** 变化原因 */
    reason?: string;
}
