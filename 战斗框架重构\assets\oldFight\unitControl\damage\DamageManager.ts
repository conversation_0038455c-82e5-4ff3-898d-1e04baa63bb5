import { Damage, DamageInfo, DamageInfoTag } from './DamageInfo';
import { CharacterControl } from '../CharacterControl';
import { CharacterResource } from '../CharacterAttributes';
const { ccclass, property } = cc._decorator;

/**
 * 处理所有damage信息
 */
export class DamageManager extends cc.Component {
    private damageInfos = new Array<DamageInfo>();

    protected update(dt: number): void {
        while (this.damageInfos.length > 0) {
            this.dealWithDamage(this.damageInfos[0]);
            this.damageInfos.shift();
        }
    }

    private dealWithDamage(dInfo: DamageInfo) {
        //如果目标已经挂了，就直接return了
        if (!dInfo.defender) return;

        const defenderChaState = dInfo.defender.getComponent(CharacterControl);
        if (!defenderChaState) return;
        if (defenderChaState.isDead) return;

        const attackerChaState = dInfo.attacker?.getComponent(CharacterControl);
        //先走一遍所有攻击者的onHit
        if (attackerChaState) {
            for (let i = 0; i < attackerChaState.buffs.length; i++) {
                dInfo = attackerChaState.buffs[i].model.onHit?.process(attackerChaState.buffs[i], dInfo, dInfo.defender);
            }
        }
        //然后走一遍挨打者的beHurt
        for (let i = 0; i < defenderChaState.buffs.length; i++) {
            if (defenderChaState.buffs[i].model.onBeHurt != null) {
                dInfo = defenderChaState.buffs[i].model.onBeHurt?.process(defenderChaState.buffs[i], dInfo, dInfo.attacker);
            }
        }
        // 获取最终伤害
        let dVal = dInfo.damageValue();
        defenderChaState.modResource(new CharacterResource(-dVal));
        //伤害流程走完，添加buff
        for (let i = 0; i < dInfo.addBuffs.length; i++) {
            const toCha = dInfo.addBuffs[i].target;
            const toChaState = toCha === (dInfo.attacker) ? attackerChaState : defenderChaState;
            if (toChaState != null && toChaState.isDead == false) {
                toChaState.addBuff(dInfo.addBuffs[i]);
            }
        }
    }

    addDamage(attacker: cc.Node, target: cc.Node, damage: Damage, tags: Array<DamageInfoTag>) {
        this.damageInfos.push(new DamageInfo(
            attacker, target, damage, tags
        ));
    }

}

