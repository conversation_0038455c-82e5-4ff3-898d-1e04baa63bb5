export class SkillNames {
    /**默认 */
    static default = 'default'
    /**敌人攻击技能 */
    static enemyAttack = 'enemyAttack'
    /**宠物攻击技能 */
    static petAttack = 'petAttack'
    /**追踪弹 */
    static skillTest = 'skillTest'
    // 普通攻击
    static playerAttack: string = "playerAttack"
    /**技能1 */
    static playerSKill1: string = "playerSKill1"
    static playerSKill2: string = "playerSKill2"
    static playerSKill3: string = "playerSKill3"
    static playerSKill4: string = "playerSKill4"
    static playerSKill5: string = "playerSKill5"
    static playerSKill6: string = "playerSKill6"
    static playerSKill7: string = "playerSKill7"
    static playerSKill8: string = "playerSKill8"
    static playerSKill9: string = "playerSKill9"
    static playerSKill10: string = "playerSKill10"
    static playerSKill11: string = "playerSKill11"
    static playerSKill12: string = "playerSKill12"
    static playerSKill13: string = "playerSKill13"
    static playerSKill14: string = "playerSKill14"
    static playerSKill15: string = "playerSKill15"
    static playerSKill16: string = "playerSKill16"
    static playerHpRecovery: string = "SKILL_HpRecovery"
}