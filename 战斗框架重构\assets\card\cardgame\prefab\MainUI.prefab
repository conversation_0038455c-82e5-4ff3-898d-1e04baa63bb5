[{"__type__": "cc.Prefab", "_name": "MainUI", "_objFlags": 0, "_native": "", "data": {"__id__": 1}, "optimizationPolicy": 0, "asyncLoadAssets": false, "readonly": false, "persistent": false}, {"__type__": "cc.Node", "_name": "MainUI", "_objFlags": 0, "_parent": null, "_children": [{"__id__": 2}, {"__id__": 72}, {"__id__": 160}, {"__id__": 288}, {"__id__": 368}], "_active": true, "_components": [{"__id__": 698}, {"__id__": 700}], "_prefab": {"__id__": 702}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 0.37, "y": 0.37, "z": 0.932}, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_id": "", "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "_bg", "_objFlags": 512, "_parent": {"__id__": 1}, "_children": [{"__id__": 3}, {"__id__": 11}, {"__id__": 19}, {"__id__": 27}, {"__id__": 35}, {"__id__": 43}, {"__id__": 51}, {"__id__": 59}], "_active": true, "_components": [{"__id__": 67}, {"__id__": 69}], "_prefab": {"__id__": 71}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_id": "", "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "cloud-bg", "_objFlags": 0, "_parent": {"__id__": 2}, "_children": [], "_active": true, "_components": [{"__id__": 4}, {"__id__": 6}, {"__id__": 8}], "_prefab": {"__id__": 10}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1.587, "y": 1.587, "z": 1.587}, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_id": "", "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 3}, "_enabled": true, "__prefab": {"__id__": 5}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8"}, "_visFlags": 0, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "ab4771b5-3f27-4260-9452-6ea9bee85cdd@f9941"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "c94v9kbc9LOLloxIrL7fjb"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "node": {"__id__": 3}, "_enabled": true, "__prefab": {"__id__": 7}, "_opacity": 255}, {"__type__": "cc.CompPrefabInfo", "fileId": "48HXnRpbFO3bSvnEqCHjKN"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 3}, "_enabled": true, "__prefab": {"__id__": 9}, "_priority": 0, "_contentSize": {"__type__": "cc.Size", "width": 2560, "height": 1440}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}}, {"__type__": "cc.CompPrefabInfo", "fileId": "53SLxRdttEI6YaMcOI4dyU"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "773Pa8GU1H9J8iSQvIkMTL"}, {"__type__": "cc.Node", "_name": "cloud", "_objFlags": 0, "_parent": {"__id__": 2}, "_children": [], "_active": true, "_components": [{"__id__": 12}, {"__id__": 14}, {"__id__": 16}], "_prefab": {"__id__": 18}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": -26.177, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_id": "", "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "sp.Skeleton", "_name": "", "_objFlags": 0, "node": {"__id__": 11}, "_enabled": true, "__prefab": {"__id__": 13}, "_visFlags": 0, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "loop": true, "timeScale": 0.1, "_useTint": false, "_preCacheMode": 0, "_cacheMode": 0, "_defaultCacheMode": 0, "_debugBones": false, "_debugSlots": false, "_skeletonData": {"__uuid__": "04336642-dd17-489a-b917-f6fbdc92bfba"}, "defaultSkin": "default", "defaultAnimation": "cloud", "_sockets": [], "_debugMesh": false, "_materials": [{"__uuid__": "7afd064b-113f-480e-b793-8817d19f63c3"}], "paused": false, "premultipliedAlpha": true, "_accTime": 0, "_playCount": 0, "_animationName": "cloud", "_animationQueue": [], "_playTimes": 0, "_isAniComplete": true, "__defaultCacheMode": 0, "_enableBatch": false, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "6eOG2JnOtHPJ+8jwInUdTH"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "node": {"__id__": 11}, "_enabled": true, "__prefab": {"__id__": 15}, "_opacity": 255}, {"__type__": "cc.CompPrefabInfo", "fileId": "42KOSzFqNLE6vtCRWKHdG5"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 11}, "_enabled": true, "__prefab": {"__id__": 17}, "_priority": 0, "_contentSize": {"__type__": "cc.Size", "width": 5105.25, "height": 1440}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}}, {"__type__": "cc.CompPrefabInfo", "fileId": "a0baky4RRGHab4H7tKbby/"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "70gfO0Xf5N4LuVG5wROn0A"}, {"__type__": "cc.Node", "_name": "mountain", "_objFlags": 0, "_parent": {"__id__": 2}, "_children": [], "_active": true, "_components": [{"__id__": 20}, {"__id__": 22}, {"__id__": 24}], "_prefab": {"__id__": 26}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 15.496, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_id": "", "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "sp.Skeleton", "_name": "", "_objFlags": 0, "node": {"__id__": 19}, "_enabled": true, "__prefab": {"__id__": 21}, "_visFlags": 0, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "loop": true, "timeScale": 0.05, "_useTint": false, "_preCacheMode": 0, "_cacheMode": 0, "_defaultCacheMode": 0, "_debugBones": false, "_debugSlots": false, "_skeletonData": {"__uuid__": "04336642-dd17-489a-b917-f6fbdc92bfba"}, "defaultSkin": "default", "defaultAnimation": "mountain", "_sockets": [], "_debugMesh": false, "_materials": [{"__uuid__": "7afd064b-113f-480e-b793-8817d19f63c3"}], "paused": false, "premultipliedAlpha": true, "_accTime": 0, "_playCount": 0, "_animationName": "mountain", "_animationQueue": [], "_playTimes": 0, "_isAniComplete": true, "__defaultCacheMode": 0, "_enableBatch": false, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "b4WjYqy+VH0LIYEZC8KcnH"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "node": {"__id__": 19}, "_enabled": true, "__prefab": {"__id__": 23}, "_opacity": 255}, {"__type__": "cc.CompPrefabInfo", "fileId": "8ez4DL37hEv4Df1FXwupjF"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 19}, "_enabled": true, "__prefab": {"__id__": 25}, "_priority": 0, "_contentSize": {"__type__": "cc.Size", "width": 5105.25, "height": 1440}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}}, {"__type__": "cc.CompPrefabInfo", "fileId": "01gIW9I+JEYqQgUhJdqSDJ"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "480r4rf7JI7qScPqWUlIrz"}, {"__type__": "cc.Node", "_name": "ground", "_objFlags": 0, "_parent": {"__id__": 2}, "_children": [], "_active": true, "_components": [{"__id__": 28}, {"__id__": 30}, {"__id__": 32}], "_prefab": {"__id__": 34}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_id": "", "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "sp.Skeleton", "_name": "", "_objFlags": 0, "node": {"__id__": 27}, "_enabled": true, "__prefab": {"__id__": 29}, "_visFlags": 0, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "loop": true, "timeScale": 1, "_useTint": false, "_preCacheMode": 0, "_cacheMode": 0, "_defaultCacheMode": 0, "_debugBones": false, "_debugSlots": false, "_skeletonData": {"__uuid__": "04336642-dd17-489a-b917-f6fbdc92bfba"}, "defaultSkin": "default", "defaultAnimation": "ground", "_sockets": [], "_debugMesh": false, "_materials": [{"__uuid__": "7afd064b-113f-480e-b793-8817d19f63c3"}], "paused": false, "premultipliedAlpha": true, "_accTime": 0, "_playCount": 0, "_animationName": "ground", "_animationQueue": [], "_playTimes": 0, "_isAniComplete": true, "__defaultCacheMode": 0, "_enableBatch": false, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "a8tGnbHSlJ7YGDOCHU7ULv"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "node": {"__id__": 27}, "_enabled": true, "__prefab": {"__id__": 31}, "_opacity": 255}, {"__type__": "cc.CompPrefabInfo", "fileId": "75nPfVoOBOdKxQd18YqRs7"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 27}, "_enabled": true, "__prefab": {"__id__": 33}, "_priority": 0, "_contentSize": {"__type__": "cc.Size", "width": 5105.25, "height": 1440}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}}, {"__type__": "cc.CompPrefabInfo", "fileId": "6bqQTJx1pFJpvdBpfKYOAu"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "6atfA9Hf1PyIaB6eI5Lpra"}, {"__type__": "cc.Node", "_name": "tree", "_objFlags": 0, "_parent": {"__id__": 2}, "_children": [], "_active": true, "_components": [{"__id__": 36}, {"__id__": 38}, {"__id__": 40}], "_prefab": {"__id__": 42}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": -7.828, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_id": "", "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "sp.Skeleton", "_name": "", "_objFlags": 0, "node": {"__id__": 35}, "_enabled": true, "__prefab": {"__id__": 37}, "_visFlags": 0, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "loop": true, "timeScale": 0.2, "_useTint": false, "_preCacheMode": 0, "_cacheMode": 0, "_defaultCacheMode": 0, "_debugBones": false, "_debugSlots": false, "_skeletonData": {"__uuid__": "04336642-dd17-489a-b917-f6fbdc92bfba"}, "defaultSkin": "default", "defaultAnimation": "tree", "_sockets": [], "_debugMesh": false, "_materials": [{"__uuid__": "7afd064b-113f-480e-b793-8817d19f63c3"}], "paused": false, "premultipliedAlpha": true, "_accTime": 0, "_playCount": 0, "_animationName": "tree", "_animationQueue": [], "_playTimes": 0, "_isAniComplete": true, "__defaultCacheMode": 0, "_enableBatch": false, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "9dpyf1RupBeLV5864JRZ6b"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "node": {"__id__": 35}, "_enabled": true, "__prefab": {"__id__": 39}, "_opacity": 255}, {"__type__": "cc.CompPrefabInfo", "fileId": "9bWseiWaRKz6AlfQGYFdu1"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 35}, "_enabled": true, "__prefab": {"__id__": 41}, "_priority": 0, "_contentSize": {"__type__": "cc.Size", "width": 5105.25, "height": 1440}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}}, {"__type__": "cc.CompPrefabInfo", "fileId": "f9zaJtWJtGmrkiG+g7zS0I"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "38EVqh1R9LJZXELqmtbXpE"}, {"__type__": "cc.Node", "_name": "ground2", "_objFlags": 0, "_parent": {"__id__": 2}, "_children": [], "_active": true, "_components": [{"__id__": 44}, {"__id__": 46}, {"__id__": 48}], "_prefab": {"__id__": 50}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": -811.47, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": -1, "z": 1}, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_id": "", "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "sp.Skeleton", "_name": "", "_objFlags": 0, "node": {"__id__": 43}, "_enabled": true, "__prefab": {"__id__": 45}, "_visFlags": 0, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "loop": true, "timeScale": 1, "_useTint": false, "_preCacheMode": 0, "_cacheMode": 0, "_defaultCacheMode": 0, "_debugBones": false, "_debugSlots": false, "_skeletonData": {"__uuid__": "04336642-dd17-489a-b917-f6fbdc92bfba"}, "defaultSkin": "default", "defaultAnimation": "ground", "_sockets": [], "_debugMesh": false, "_materials": [{"__uuid__": "7afd064b-113f-480e-b793-8817d19f63c3"}], "paused": false, "premultipliedAlpha": true, "_accTime": 0, "_playCount": 0, "_animationName": "ground", "_animationQueue": [], "_playTimes": 0, "_isAniComplete": true, "__defaultCacheMode": 0, "_enableBatch": false, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "868ebGVL1MKqnMtZYDtST+"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "node": {"__id__": 43}, "_enabled": true, "__prefab": {"__id__": 47}, "_opacity": 255}, {"__type__": "cc.CompPrefabInfo", "fileId": "cbsFXeD1pCdIMMknYOn/Xx"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 43}, "_enabled": true, "__prefab": {"__id__": 49}, "_priority": 0, "_contentSize": {"__type__": "cc.Size", "width": 5105.25, "height": 1440}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}}, {"__type__": "cc.CompPrefabInfo", "fileId": "faeHEid0ZNT50UJs/7Ssk3"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "321o/Yn0hApqJRVtEcJ8I2"}, {"__type__": "cc.Node", "_name": "gress", "_objFlags": 0, "_parent": {"__id__": 2}, "_children": [], "_active": true, "_components": [{"__id__": 52}, {"__id__": 54}, {"__id__": 56}], "_prefab": {"__id__": 58}, "_lpos": {"__type__": "cc.Vec3", "x": 563.927, "y": -33.913, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_id": "", "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "sp.Skeleton", "_name": "", "_objFlags": 0, "node": {"__id__": 51}, "_enabled": true, "__prefab": {"__id__": 53}, "_visFlags": 0, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "loop": true, "timeScale": 0.5, "_useTint": false, "_preCacheMode": 0, "_cacheMode": 0, "_defaultCacheMode": 0, "_debugBones": false, "_debugSlots": false, "_skeletonData": {"__uuid__": "04336642-dd17-489a-b917-f6fbdc92bfba"}, "defaultSkin": "default", "defaultAnimation": "grass", "_sockets": [], "_debugMesh": false, "_materials": [{"__uuid__": "7afd064b-113f-480e-b793-8817d19f63c3"}], "paused": false, "premultipliedAlpha": true, "_accTime": 0, "_playCount": 0, "_animationName": "grass", "_animationQueue": [], "_playTimes": 0, "_isAniComplete": true, "__defaultCacheMode": 0, "_enableBatch": false, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "b3MXrcSXlKq4Ra+sCdxMnm"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "node": {"__id__": 51}, "_enabled": true, "__prefab": {"__id__": 55}, "_opacity": 255}, {"__type__": "cc.CompPrefabInfo", "fileId": "45LMWdoEZLspWnsYYvSMMT"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 51}, "_enabled": true, "__prefab": {"__id__": 57}, "_priority": 0, "_contentSize": {"__type__": "cc.Size", "width": 5105.25, "height": 1440}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}}, {"__type__": "cc.CompPrefabInfo", "fileId": "eeWZ6638dN1YPia9/nS6Vq"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "9dmZhmiLNLNqgLlFvN1TzS"}, {"__type__": "cc.Node", "_name": "stone", "_objFlags": 0, "_parent": {"__id__": 2}, "_children": [], "_active": true, "_components": [{"__id__": 60}, {"__id__": 62}, {"__id__": 64}], "_prefab": {"__id__": 66}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": -133.284, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_id": "", "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "sp.Skeleton", "_name": "", "_objFlags": 0, "node": {"__id__": 59}, "_enabled": true, "__prefab": {"__id__": 61}, "_visFlags": 0, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "loop": true, "timeScale": 1, "_useTint": false, "_preCacheMode": 0, "_cacheMode": 0, "_defaultCacheMode": 0, "_debugBones": false, "_debugSlots": false, "_skeletonData": {"__uuid__": "04336642-dd17-489a-b917-f6fbdc92bfba"}, "defaultSkin": "default", "defaultAnimation": "stone", "_sockets": [], "_debugMesh": false, "_materials": [{"__uuid__": "7afd064b-113f-480e-b793-8817d19f63c3"}], "paused": false, "premultipliedAlpha": true, "_accTime": 0, "_playCount": 0, "_animationName": "stone", "_animationQueue": [], "_playTimes": 0, "_isAniComplete": true, "__defaultCacheMode": 0, "_enableBatch": false, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "965WT8Bl5D2L/COLMeFKyT"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "node": {"__id__": 59}, "_enabled": true, "__prefab": {"__id__": 63}, "_opacity": 255}, {"__type__": "cc.CompPrefabInfo", "fileId": "2bTcC1fgNAN4/3z/bAE5JV"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 59}, "_enabled": true, "__prefab": {"__id__": 65}, "_priority": 0, "_contentSize": {"__type__": "cc.Size", "width": 5105.25, "height": 1440}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}}, {"__type__": "cc.CompPrefabInfo", "fileId": "7dBQaqzuZEcKIMfTMpibZP"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "57H3WSvSFIjJ8maYcA4V5P"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "node": {"__id__": 2}, "_enabled": true, "__prefab": {"__id__": 68}, "_opacity": 255}, {"__type__": "cc.CompPrefabInfo", "fileId": "17wNNBYGxNeYfM1HZkGvTY"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 2}, "_enabled": true, "__prefab": {"__id__": 70}, "_priority": 0, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 0}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}}, {"__type__": "cc.CompPrefabInfo", "fileId": "1abSzCw+ZDiax6aNLQ7HId"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "e4iO9mrllI84usIgJGz1xi"}, {"__type__": "cc.Node", "_name": "scene", "_objFlags": 512, "_parent": {"__id__": 1}, "_children": [{"__id__": 73}, {"__id__": 97}, {"__id__": 113}, {"__id__": 121}, {"__id__": 139}, {"__id__": 149}], "_active": true, "_components": [{"__id__": 155}, {"__id__": 157}], "_prefab": {"__id__": 159}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_id": "", "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "_player", "_objFlags": 0, "_parent": {"__id__": 72}, "_children": [{"__id__": 74}, {"__id__": 82}], "_active": true, "_components": [{"__id__": 90}, {"__id__": 92}, {"__id__": 94}], "_prefab": {"__id__": 96}, "_lpos": {"__type__": "cc.Vec3", "x": -423.903, "y": -171.754, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_id": "", "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "_hpRecovery", "_objFlags": 0, "_parent": {"__id__": 73}, "_children": [], "_active": true, "_components": [{"__id__": 75}, {"__id__": 77}, {"__id__": 79}], "_prefab": {"__id__": 81}, "_lpos": {"__type__": "cc.Vec3", "x": 5.055, "y": -168.363, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_id": "", "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "sp.Skeleton", "_name": "", "_objFlags": 0, "node": {"__id__": 74}, "_enabled": true, "__prefab": {"__id__": 76}, "_visFlags": 0, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "loop": true, "timeScale": 1, "_useTint": false, "_preCacheMode": 0, "_cacheMode": 0, "_defaultCacheMode": 0, "_debugBones": false, "_debugSlots": false, "_skeletonData": {"__uuid__": "8c7decbc-7bd9-4944-9c2e-8d0e927c6818"}, "defaultSkin": "default", "defaultAnimation": "HP", "_sockets": [], "_debugMesh": false, "_materials": [{"__uuid__": "7afd064b-113f-480e-b793-8817d19f63c3"}], "paused": false, "premultipliedAlpha": true, "_accTime": 0, "_playCount": 0, "_animationName": "HP", "_animationQueue": [], "_playTimes": 0, "_isAniComplete": true, "__defaultCacheMode": 0, "_enableBatch": false, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "bcoiQ1iltC3J+e+fmVECL6"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "node": {"__id__": 74}, "_enabled": true, "__prefab": {"__id__": 78}, "_opacity": 255}, {"__type__": "cc.CompPrefabInfo", "fileId": "64CbrKOAhBCqCC5aMrJi/k"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 74}, "_enabled": true, "__prefab": {"__id__": 80}, "_priority": 0, "_contentSize": {"__type__": "cc.Size", "width": 308.15, "height": 227.98}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}}, {"__type__": "cc.CompPrefabInfo", "fileId": "49ubEmY+BISrZJj6eWUejx"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "c2GR9l1mBKkpqHJnjlfEXe"}, {"__type__": "cc.Node", "_name": "_playerHit", "_objFlags": 0, "_parent": {"__id__": 73}, "_children": [], "_active": true, "_components": [{"__id__": 83}, {"__id__": 85}, {"__id__": 87}], "_prefab": {"__id__": 89}, "_lpos": {"__type__": "cc.Vec3", "x": 27.651, "y": 288.568, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_id": "", "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "sp.Skeleton", "_name": "", "_objFlags": 0, "node": {"__id__": 82}, "_enabled": true, "__prefab": {"__id__": 84}, "_visFlags": 0, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "loop": true, "timeScale": 1, "_useTint": false, "_preCacheMode": 0, "_cacheMode": 0, "_defaultCacheMode": 0, "_debugBones": false, "_debugSlots": false, "_skeletonData": {"__uuid__": "46e34b3f-1f37-4a15-9fb5-715e10c93616"}, "defaultSkin": "default", "defaultAnimation": "hurt-effect", "_sockets": [], "_debugMesh": false, "_materials": [{"__uuid__": "7afd064b-113f-480e-b793-8817d19f63c3"}], "paused": false, "premultipliedAlpha": true, "_accTime": 0, "_playCount": 0, "_animationName": "hurt-effect", "_animationQueue": [], "_playTimes": 0, "_isAniComplete": true, "__defaultCacheMode": 0, "_enableBatch": false, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "a04mL0hX5OvIbxDiZ1C70W"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "node": {"__id__": 82}, "_enabled": true, "__prefab": {"__id__": 86}, "_opacity": 255}, {"__type__": "cc.CompPrefabInfo", "fileId": "c5QaCmivJE8Zyt43xL1Dg5"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 82}, "_enabled": true, "__prefab": {"__id__": 88}, "_priority": 0, "_contentSize": {"__type__": "cc.Size", "width": 300, "height": 285}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}}, {"__type__": "cc.CompPrefabInfo", "fileId": "995oeHMJ1Hz6r5BEnTgdW2"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "1bOax5w1dCdJ42weVNNxnS"}, {"__type__": "sp.Skeleton", "_name": "", "_objFlags": 0, "node": {"__id__": 73}, "_enabled": true, "__prefab": {"__id__": 91}, "_visFlags": 0, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "loop": true, "timeScale": 1.2, "_useTint": false, "_preCacheMode": 0, "_cacheMode": 0, "_defaultCacheMode": 0, "_debugBones": false, "_debugSlots": false, "_skeletonData": {"__uuid__": "d61ecbeb-475a-4b62-b787-8768d6a8f8c9"}, "defaultSkin": "default", "defaultAnimation": "idle", "_sockets": [], "_debugMesh": false, "_materials": [{"__uuid__": "28660804-065a-46bd-8f94-802685c93f95"}], "paused": false, "premultipliedAlpha": false, "_accTime": 0, "_playCount": 0, "_animationName": "idle", "_animationQueue": [], "_playTimes": 0, "_isAniComplete": true, "__defaultCacheMode": 0, "_enableBatch": false, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "4f+USrCrBGBZo1sJnvwxvm"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "node": {"__id__": 73}, "_enabled": true, "__prefab": {"__id__": 93}, "_opacity": 255}, {"__type__": "cc.CompPrefabInfo", "fileId": "a3CJ4J0TpLOpe1VBdsS6qI"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 73}, "_enabled": true, "__prefab": {"__id__": 95}, "_priority": 0, "_contentSize": {"__type__": "cc.Size", "width": 354.95, "height": 437.68}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}}, {"__type__": "cc.CompPrefabInfo", "fileId": "d4QkQSqrJOj5C+vWaTnF6E"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "3diObPRGpL+ZZIhr80vsUX"}, {"__type__": "cc.Node", "_name": "_monster", "_objFlags": 0, "_parent": {"__id__": 72}, "_children": [{"__id__": 98}], "_active": true, "_components": [{"__id__": 106}, {"__id__": 108}, {"__id__": 110}], "_prefab": {"__id__": 112}, "_lpos": {"__type__": "cc.Vec3", "x": 1758.997, "y": -150.205, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_id": "", "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "_batHit", "_objFlags": 0, "_parent": {"__id__": 97}, "_children": [], "_active": true, "_components": [{"__id__": 99}, {"__id__": 101}, {"__id__": 103}], "_prefab": {"__id__": 105}, "_lpos": {"__type__": "cc.Vec3", "x": -28.363, "y": 136.563, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_id": "", "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "sp.Skeleton", "_name": "", "_objFlags": 0, "node": {"__id__": 98}, "_enabled": true, "__prefab": {"__id__": 100}, "_visFlags": 0, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "loop": true, "timeScale": 1, "_useTint": false, "_preCacheMode": 0, "_cacheMode": 0, "_defaultCacheMode": 0, "_debugBones": false, "_debugSlots": false, "_skeletonData": {"__uuid__": "46e34b3f-1f37-4a15-9fb5-715e10c93616"}, "defaultSkin": "default", "defaultAnimation": "hurt-effect", "_sockets": [], "_debugMesh": false, "_materials": [{"__uuid__": "7afd064b-113f-480e-b793-8817d19f63c3"}], "paused": false, "premultipliedAlpha": true, "_accTime": 0, "_playCount": 0, "_animationName": "hurt-effect", "_animationQueue": [], "_playTimes": 0, "_isAniComplete": true, "__defaultCacheMode": 0, "_enableBatch": false, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "e7nkRA3U5ODbJScMckWpH9"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "node": {"__id__": 98}, "_enabled": true, "__prefab": {"__id__": 102}, "_opacity": 255}, {"__type__": "cc.CompPrefabInfo", "fileId": "6ax14+fxpJ5o85OVbYlpkt"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 98}, "_enabled": true, "__prefab": {"__id__": 104}, "_priority": 0, "_contentSize": {"__type__": "cc.Size", "width": 300, "height": 285}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}}, {"__type__": "cc.CompPrefabInfo", "fileId": "52N71pGEtGGKbNgRo5C3sR"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "9cT9xr3+hMsreCQ0dUv4o5"}, {"__type__": "sp.Skeleton", "_name": "", "_objFlags": 0, "node": {"__id__": 97}, "_enabled": true, "__prefab": {"__id__": 107}, "_visFlags": 0, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "loop": true, "timeScale": 1.2, "_useTint": false, "_preCacheMode": 0, "_cacheMode": 0, "_defaultCacheMode": 0, "_debugBones": false, "_debugSlots": false, "_skeletonData": {"__uuid__": "0d0d0877-ca5a-4fb4-a392-3c5a5315e1f9"}, "defaultSkin": "default", "defaultAnimation": "idle", "_sockets": [], "_debugMesh": false, "_materials": [{"__uuid__": "28660804-065a-46bd-8f94-802685c93f95"}], "paused": false, "premultipliedAlpha": false, "_accTime": 0, "_playCount": 0, "_animationName": "idle", "_animationQueue": [], "_playTimes": 0, "_isAniComplete": true, "__defaultCacheMode": 0, "_enableBatch": false, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "83rLrRKftF/I9M/Z2ijifx"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "node": {"__id__": 97}, "_enabled": true, "__prefab": {"__id__": 109}, "_opacity": 255}, {"__type__": "cc.CompPrefabInfo", "fileId": "19iTHw4IhEHonaThy+5rwq"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 97}, "_enabled": true, "__prefab": {"__id__": 111}, "_priority": 0, "_contentSize": {"__type__": "cc.Size", "width": 383.76, "height": 383.4}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}}, {"__type__": "cc.CompPrefabInfo", "fileId": "ea3+UxcSRMc7PRJrFBztsx"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "46VljOjbNEqpYjSSgk5TGk"}, {"__type__": "cc.Node", "_name": "_attactFx", "_objFlags": 0, "_parent": {"__id__": 72}, "_children": [], "_active": true, "_components": [{"__id__": 114}, {"__id__": 116}, {"__id__": 118}], "_prefab": {"__id__": 120}, "_lpos": {"__type__": "cc.Vec3", "x": 387.955, "y": -298.135, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_id": "", "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "sp.Skeleton", "_name": "", "_objFlags": 0, "node": {"__id__": 113}, "_enabled": true, "__prefab": {"__id__": 115}, "_visFlags": 0, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "loop": true, "timeScale": 1, "_useTint": false, "_preCacheMode": 0, "_cacheMode": 0, "_defaultCacheMode": 0, "_debugBones": false, "_debugSlots": false, "_skeletonData": {"__uuid__": "303efbbf-f237-40ae-a2c4-afd195c1b29e"}, "defaultSkin": "default", "defaultAnimation": "flame", "_sockets": [], "_debugMesh": false, "_materials": [{"__uuid__": "7afd064b-113f-480e-b793-8817d19f63c3"}], "paused": false, "premultipliedAlpha": true, "_accTime": 0, "_playCount": 0, "_animationName": "flame", "_animationQueue": [], "_playTimes": 0, "_isAniComplete": true, "__defaultCacheMode": 0, "_enableBatch": false, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "1b62nlMU1IAITCSW8nzQW1"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "node": {"__id__": 113}, "_enabled": true, "__prefab": {"__id__": 117}, "_opacity": 255}, {"__type__": "cc.CompPrefabInfo", "fileId": "d1jRpeZDRBJbejckymozDN"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 113}, "_enabled": true, "__prefab": {"__id__": 119}, "_priority": 0, "_contentSize": {"__type__": "cc.Size", "width": 366, "height": 495}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}}, {"__type__": "cc.CompPrefabInfo", "fileId": "b1wX30dJ9GrpT76c83D8oz"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "c8SidLnQJFRaHZ7KBvU8Uj"}, {"__type__": "cc.Node", "_name": "point", "_objFlags": 0, "_parent": {"__id__": 72}, "_children": [{"__id__": 122}, {"__id__": 128}], "_active": true, "_components": [{"__id__": 134}, {"__id__": 136}], "_prefab": {"__id__": 138}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_id": "", "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "_monsterStart", "_objFlags": 0, "_parent": {"__id__": 121}, "_children": [], "_active": true, "_components": [{"__id__": 123}, {"__id__": 125}], "_prefab": {"__id__": 127}, "_lpos": {"__type__": "cc.Vec3", "x": 1818.314, "y": 100.117, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_id": "", "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "node": {"__id__": 122}, "_enabled": true, "__prefab": {"__id__": 124}, "_opacity": 255}, {"__type__": "cc.CompPrefabInfo", "fileId": "d0rs5IrW5NiLoQLBlXAthW"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 122}, "_enabled": true, "__prefab": {"__id__": 126}, "_priority": 0, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 0}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}}, {"__type__": "cc.CompPrefabInfo", "fileId": "d8RBgd8TpP+4V7Y5q5Vnwx"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "07HJbzL4lPQYUi/dyevNYc"}, {"__type__": "cc.Node", "_name": "_monsterEnd", "_objFlags": 0, "_parent": {"__id__": 121}, "_children": [], "_active": true, "_components": [{"__id__": 129}, {"__id__": 131}], "_prefab": {"__id__": 133}, "_lpos": {"__type__": "cc.Vec3", "x": 324.602, "y": -150.205, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_id": "", "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "node": {"__id__": 128}, "_enabled": true, "__prefab": {"__id__": 130}, "_opacity": 255}, {"__type__": "cc.CompPrefabInfo", "fileId": "b36SchzdlDra/diG/nct3o"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 128}, "_enabled": true, "__prefab": {"__id__": 132}, "_priority": 0, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 0}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}}, {"__type__": "cc.CompPrefabInfo", "fileId": "09d326KsBLY5zWAF0gTHxk"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "cb8kO9C2tMmK/FgaSpbp2p"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "node": {"__id__": 121}, "_enabled": true, "__prefab": {"__id__": 135}, "_opacity": 255}, {"__type__": "cc.CompPrefabInfo", "fileId": "5cNL5gmp9NuJ5i3y6AO5II"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 121}, "_enabled": true, "__prefab": {"__id__": 137}, "_priority": 0, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 0}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}}, {"__type__": "cc.CompPrefabInfo", "fileId": "388Od7ykBMlbn9F1vhna8j"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "f4I5HXJVpFtId8lNHAxfGx"}, {"__type__": "cc.Node", "_name": "_hertText", "_objFlags": 0, "_parent": {"__id__": 72}, "_children": [], "_active": true, "_components": [{"__id__": 140}, {"__id__": 142}, {"__id__": 144}, {"__id__": 146}], "_prefab": {"__id__": 148}, "_lpos": {"__type__": "cc.Vec3", "x": -330.739, "y": 271.579, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_id": "", "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 139}, "_enabled": true, "__prefab": {"__id__": 141}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8"}, "_visFlags": 0, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_useOriginalSize": true, "_string": "-111", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 0, "_fontSize": 50, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 40, "_overflow": 0, "_enableWrapText": true, "_font": {"__uuid__": "6c499e91-b85d-4647-8f0e-0f22d10a798c"}, "_isSystemFontUsed": false, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 0, "_cacheMode": 0, "_spacingX": 0, "_batchAsBitmap": false, "_styleFlags": 1, "_id": "", "_enableOutline": false, "_outlineWidth": 3, "_outlineColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}}, {"__type__": "cc.CompPrefabInfo", "fileId": "4aiTNcTIpLPISJ8Ur5HcMP"}, {"__type__": "cc.LabelOutline", "_name": "", "_objFlags": 0, "node": {"__id__": 139}, "_enabled": false, "__prefab": {"__id__": 143}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "0eSMXfYGZNMJcxeDPSQKIT"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "node": {"__id__": 139}, "_enabled": true, "__prefab": {"__id__": 145}, "_opacity": 255}, {"__type__": "cc.CompPrefabInfo", "fileId": "4dkhf75ZJMXpdndcgnMvGp"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 139}, "_enabled": true, "__prefab": {"__id__": 147}, "_priority": 0, "_contentSize": {"__type__": "cc.Size", "width": 71.36, "height": 50.4}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}}, {"__type__": "cc.CompPrefabInfo", "fileId": "342b7EQVVLfa7Ot0anZtLR"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "14Pj6y1RdKebP/FO5Aihu9"}, {"__type__": "cc.Node", "_name": "_hertTextNode", "_objFlags": 0, "_parent": {"__id__": 72}, "_children": [], "_active": true, "_components": [{"__id__": 150}, {"__id__": 152}], "_prefab": {"__id__": 154}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_id": "", "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "node": {"__id__": 149}, "_enabled": true, "__prefab": {"__id__": 151}, "_opacity": 255}, {"__type__": "cc.CompPrefabInfo", "fileId": "ffGtsQhSRNCLDM9nxRYU/Q"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 149}, "_enabled": true, "__prefab": {"__id__": 153}, "_priority": 0, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 0}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}}, {"__type__": "cc.CompPrefabInfo", "fileId": "83A1zANeRIPoM4cqRFBqWd"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "2fYqNSv2pEsZBA5gqUbyQT"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "node": {"__id__": 72}, "_enabled": true, "__prefab": {"__id__": 156}, "_opacity": 255}, {"__type__": "cc.CompPrefabInfo", "fileId": "7c9OhXTHlA+LtRIJqqY0wM"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 72}, "_enabled": true, "__prefab": {"__id__": 158}, "_priority": 0, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 0}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}}, {"__type__": "cc.CompPrefabInfo", "fileId": "18/G9LFHRIX4tw+JFSzRIW"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "3aw6WslHdM/J8NY2X5rsNG"}, {"__type__": "cc.Node", "_name": "userBord", "_objFlags": 512, "_parent": {"__id__": 1}, "_children": [{"__id__": 161}], "_active": true, "_components": [{"__id__": 281}, {"__id__": 283}, {"__id__": 285}], "_prefab": {"__id__": 287}, "_lpos": {"__type__": "cc.Vec3", "x": -1212.868, "y": 843.746, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_id": "", "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "_userBord", "_objFlags": 0, "_parent": {"__id__": 160}, "_children": [{"__id__": 162}, {"__id__": 170}, {"__id__": 268}], "_active": true, "_components": [{"__id__": 276}, {"__id__": 278}], "_prefab": {"__id__": 280}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_id": "", "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "bg-2", "_objFlags": 0, "_parent": {"__id__": 161}, "_children": [], "_active": true, "_components": [{"__id__": 163}, {"__id__": 165}, {"__id__": 167}], "_prefab": {"__id__": 169}, "_lpos": {"__type__": "cc.Vec3", "x": 405, "y": -1.3, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_id": "", "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 162}, "_enabled": true, "__prefab": {"__id__": 164}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8"}, "_visFlags": 0, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "3483e6eb-85f8-4d73-9897-44cd9acf6b7e@f9941"}, "_type": 0, "_fillType": 0, "_sizeMode": 2, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "c7nMdaVMZOQrRg2iI8TXU5"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "node": {"__id__": 162}, "_enabled": true, "__prefab": {"__id__": 166}, "_opacity": 255}, {"__type__": "cc.CompPrefabInfo", "fileId": "d3NPrZ4FZPnL0c5JMW947z"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 162}, "_enabled": true, "__prefab": {"__id__": 168}, "_priority": 0, "_contentSize": {"__type__": "cc.Size", "width": 747, "height": 106}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}}, {"__type__": "cc.CompPrefabInfo", "fileId": "54738/+XtIZ7wv7Up2OGcG"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "75bM+pkLZBlomtLVBzBFYw"}, {"__type__": "cc.Node", "_name": "user", "_objFlags": 0, "_parent": {"__id__": 161}, "_children": [{"__id__": 171}, {"__id__": 195}, {"__id__": 219}, {"__id__": 243}, {"__id__": 251}], "_active": true, "_components": [{"__id__": 259}, {"__id__": 261}, {"__id__": 263}, {"__id__": 265}], "_prefab": {"__id__": 267}, "_lpos": {"__type__": "cc.Vec3", "x": 409.5, "y": -176.5, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_id": "", "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "_userLv", "_objFlags": 0, "_parent": {"__id__": 170}, "_children": [{"__id__": 172}, {"__id__": 180}], "_active": true, "_components": [{"__id__": 188}, {"__id__": 190}, {"__id__": 192}], "_prefab": {"__id__": 194}, "_lpos": {"__type__": "cc.Vec3", "x": -275.726, "y": -137.863, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_id": "", "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "_userLvLb", "_objFlags": 0, "_parent": {"__id__": 171}, "_children": [], "_active": true, "_components": [{"__id__": 173}, {"__id__": 175}, {"__id__": 177}], "_prefab": {"__id__": 179}, "_lpos": {"__type__": "cc.Vec3", "x": 31.819, "y": -2.571, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_id": "", "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 172}, "_enabled": true, "__prefab": {"__id__": 174}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8"}, "_visFlags": 0, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_useOriginalSize": true, "_string": "-1", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 0, "_fontSize": 40, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 40, "_overflow": 0, "_enableWrapText": true, "_font": {"__uuid__": "6c499e91-b85d-4647-8f0e-0f22d10a798c"}, "_isSystemFontUsed": false, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 0, "_cacheMode": 0, "_spacingX": 0, "_batchAsBitmap": false, "_styleFlags": 1, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "b2HwsoQIBIk6OAzSflm+KV"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "node": {"__id__": 172}, "_enabled": true, "__prefab": {"__id__": 176}, "_opacity": 255}, {"__type__": "cc.CompPrefabInfo", "fileId": "9bU5Ny/adFQbEBJV6vhNwd"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 172}, "_enabled": true, "__prefab": {"__id__": 178}, "_priority": 0, "_contentSize": {"__type__": "cc.Size", "width": 27.09, "height": 50.4}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}}, {"__type__": "cc.CompPrefabInfo", "fileId": "dbpbCwH/lMAJ9IvLv9ygn8"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "a8ECgHkg1GlJ4ZVtv16coL"}, {"__type__": "cc.Node", "_name": "lb", "_objFlags": 0, "_parent": {"__id__": 171}, "_children": [], "_active": true, "_components": [{"__id__": 181}, {"__id__": 183}, {"__id__": 185}], "_prefab": {"__id__": 187}, "_lpos": {"__type__": "cc.Vec3", "x": -48.849, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_id": "", "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 180}, "_enabled": true, "__prefab": {"__id__": 182}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8"}, "_visFlags": 0, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_useOriginalSize": true, "_string": "Lv", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 0, "_fontSize": 40, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 40, "_overflow": 0, "_enableWrapText": true, "_font": {"__uuid__": "6c499e91-b85d-4647-8f0e-0f22d10a798c"}, "_isSystemFontUsed": false, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 0, "_cacheMode": 0, "_spacingX": 0, "_batchAsBitmap": false, "_styleFlags": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "a0NXcdmftIA5pZxU2y7aYj"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "node": {"__id__": 180}, "_enabled": true, "__prefab": {"__id__": 184}, "_opacity": 255}, {"__type__": "cc.CompPrefabInfo", "fileId": "9enfV+Ov9MFIh0R1zWDVgP"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 180}, "_enabled": true, "__prefab": {"__id__": 186}, "_priority": 0, "_contentSize": {"__type__": "cc.Size", "width": 32.91, "height": 50.4}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}}, {"__type__": "cc.CompPrefabInfo", "fileId": "e1leVIrBZGz7KbLOrRI7oh"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "be9lcZZlREM6ZZI4ftGHc8"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 171}, "_enabled": true, "__prefab": {"__id__": 189}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8"}, "_visFlags": 0, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "c519f438-197c-4fc0-bf1d-c2e4a3cac8e4@f9941"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "42PQ9Nnz9OE67XWiQzBFBJ"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "node": {"__id__": 171}, "_enabled": true, "__prefab": {"__id__": 191}, "_opacity": 255}, {"__type__": "cc.CompPrefabInfo", "fileId": "7cD16wELtBYLb6cLJPkED3"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 171}, "_enabled": true, "__prefab": {"__id__": 193}, "_priority": 0, "_contentSize": {"__type__": "cc.Size", "width": 235, "height": 116}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}}, {"__type__": "cc.CompPrefabInfo", "fileId": "27MnabAPdD5KrNi9VWvMJH"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "75dsjOULNHOrMjStHd/KW0"}, {"__type__": "cc.Node", "_name": "_userExp", "_objFlags": 0, "_parent": {"__id__": 170}, "_children": [{"__id__": 196}, {"__id__": 204}], "_active": true, "_components": [{"__id__": 212}, {"__id__": 214}, {"__id__": 216}], "_prefab": {"__id__": 218}, "_lpos": {"__type__": "cc.Vec3", "x": 54.746, "y": -112.925, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_id": "", "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "_userExpProgress", "_objFlags": 0, "_parent": {"__id__": 195}, "_children": [], "_active": true, "_components": [{"__id__": 197}, {"__id__": 199}, {"__id__": 201}], "_prefab": {"__id__": 203}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_id": "", "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 196}, "_enabled": true, "__prefab": {"__id__": 198}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8"}, "_visFlags": 0, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "718b766d-eab7-44e2-9213-8db8d2dd2fbe@f9941"}, "_type": 3, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0.8, "_fillRange": -1, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "d3lET7lwlK7qavf8VHTY75"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "node": {"__id__": 196}, "_enabled": true, "__prefab": {"__id__": 200}, "_opacity": 255}, {"__type__": "cc.CompPrefabInfo", "fileId": "aaOnl6LQZN24RXlvA6msDJ"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 196}, "_enabled": true, "__prefab": {"__id__": 202}, "_priority": 0, "_contentSize": {"__type__": "cc.Size", "width": 414, "height": 91}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}}, {"__type__": "cc.CompPrefabInfo", "fileId": "faojPBOudOjLcorlRcgSUg"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "80D/lr++dLz6BOZfMJPnJ0"}, {"__type__": "cc.Node", "_name": "_userExpLb", "_objFlags": 0, "_parent": {"__id__": 195}, "_children": [], "_active": true, "_components": [{"__id__": 205}, {"__id__": 207}, {"__id__": 209}], "_prefab": {"__id__": 211}, "_lpos": {"__type__": "cc.Vec3", "x": -13.774, "y": -3.657, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_id": "", "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 204}, "_enabled": true, "__prefab": {"__id__": 206}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8"}, "_visFlags": 0, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_useOriginalSize": true, "_string": "00", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 0, "_fontSize": 40, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 40, "_overflow": 0, "_enableWrapText": true, "_font": {"__uuid__": "6c499e91-b85d-4647-8f0e-0f22d10a798c"}, "_isSystemFontUsed": false, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 0, "_cacheMode": 0, "_spacingX": 0, "_batchAsBitmap": false, "_styleFlags": 1, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "a8wuGjFPdOp6G6Oxw2FZIZ"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "node": {"__id__": 204}, "_enabled": true, "__prefab": {"__id__": 208}, "_opacity": 255}, {"__type__": "cc.CompPrefabInfo", "fileId": "90bmq6nb1Kvpxou6Luu657"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 204}, "_enabled": true, "__prefab": {"__id__": 210}, "_priority": 0, "_contentSize": {"__type__": "cc.Size", "width": 39.18, "height": 50.4}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}}, {"__type__": "cc.CompPrefabInfo", "fileId": "17C/zIFTZI+qVTrA8kENWW"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "4eTgLmljxPnpb67sZ/lb+V"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 195}, "_enabled": true, "__prefab": {"__id__": 213}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8"}, "_visFlags": 0, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "2198e76c-58b0-4c0c-89f8-a0b769f25d97@f9941"}, "_type": 0, "_fillType": 0, "_sizeMode": 2, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "0a8Ao4u2xIAp3+wja14517"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "node": {"__id__": 195}, "_enabled": true, "__prefab": {"__id__": 215}, "_opacity": 255}, {"__type__": "cc.CompPrefabInfo", "fileId": "9a76UxZ3ZE/5pI/7+7X/9j"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 195}, "_enabled": true, "__prefab": {"__id__": 217}, "_priority": 0, "_contentSize": {"__type__": "cc.Size", "width": 414, "height": 91}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}}, {"__type__": "cc.CompPrefabInfo", "fileId": "e73X1Z39NOvJhkX1GKShNV"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "78VhIF4gNDO6b0psQwPyVQ"}, {"__type__": "cc.Node", "_name": "_userHp", "_objFlags": 0, "_parent": {"__id__": 170}, "_children": [{"__id__": 220}, {"__id__": 228}], "_active": true, "_components": [{"__id__": 236}, {"__id__": 238}, {"__id__": 240}], "_prefab": {"__id__": 242}, "_lpos": {"__type__": "cc.Vec3", "x": 166.821, "y": 91.063, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_id": "", "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "_userHpProgress", "_objFlags": 0, "_parent": {"__id__": 219}, "_children": [], "_active": true, "_components": [{"__id__": 221}, {"__id__": 223}, {"__id__": 225}], "_prefab": {"__id__": 227}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_id": "", "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 220}, "_enabled": true, "__prefab": {"__id__": 222}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8"}, "_visFlags": 0, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "b4137058-885c-4abc-ab9e-24179fa3dbf8@f9941"}, "_type": 3, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0.7, "_fillRange": -1, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "61NHqyiIlNQ4DCOUONrkPH"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "node": {"__id__": 220}, "_enabled": true, "__prefab": {"__id__": 224}, "_opacity": 255}, {"__type__": "cc.CompPrefabInfo", "fileId": "fffK6Ks5NPlKVKuVjimUSZ"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 220}, "_enabled": true, "__prefab": {"__id__": 226}, "_priority": 0, "_contentSize": {"__type__": "cc.Size", "width": 414, "height": 91}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}}, {"__type__": "cc.CompPrefabInfo", "fileId": "50TPXRmD9G1pYJYNbf8gQq"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "e4LIyiJytEYrbdqSb71/5x"}, {"__type__": "cc.Node", "_name": "_userHpLb", "_objFlags": 0, "_parent": {"__id__": 219}, "_children": [], "_active": true, "_components": [{"__id__": 229}, {"__id__": 231}, {"__id__": 233}], "_prefab": {"__id__": 235}, "_lpos": {"__type__": "cc.Vec3", "x": -13.774, "y": -3.657, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_id": "", "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 228}, "_enabled": true, "__prefab": {"__id__": 230}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8"}, "_visFlags": 0, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_useOriginalSize": true, "_string": "00", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 0, "_fontSize": 40, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 40, "_overflow": 0, "_enableWrapText": true, "_font": {"__uuid__": "6c499e91-b85d-4647-8f0e-0f22d10a798c"}, "_isSystemFontUsed": false, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 0, "_cacheMode": 0, "_spacingX": 0, "_batchAsBitmap": false, "_styleFlags": 1, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "45FqhFbhpGqIkOPKCclUtk"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "node": {"__id__": 228}, "_enabled": true, "__prefab": {"__id__": 232}, "_opacity": 255}, {"__type__": "cc.CompPrefabInfo", "fileId": "18o5nGxchPIbXJCelRMQ56"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 228}, "_enabled": true, "__prefab": {"__id__": 234}, "_priority": 0, "_contentSize": {"__type__": "cc.Size", "width": 39.18, "height": 50.4}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}}, {"__type__": "cc.CompPrefabInfo", "fileId": "0biOjzQxBAl74V+tEu3M+Y"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "ac2Nh0FWpKZY4gbLf0Aa6/"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 219}, "_enabled": true, "__prefab": {"__id__": 237}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8"}, "_visFlags": 0, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "2198e76c-58b0-4c0c-89f8-a0b769f25d97@f9941"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "4buU7HIl5O6qe4mEBObYqA"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "node": {"__id__": 219}, "_enabled": true, "__prefab": {"__id__": 239}, "_opacity": 255}, {"__type__": "cc.CompPrefabInfo", "fileId": "basqGhns5Pb736wddDB5Eh"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 219}, "_enabled": true, "__prefab": {"__id__": 241}, "_priority": 0, "_contentSize": {"__type__": "cc.Size", "width": 414, "height": 91}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}}, {"__type__": "cc.CompPrefabInfo", "fileId": "96QCJnkZNDNbEqjc23zIsN"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "f9+Id2+oxA5qS/96bQ0X71"}, {"__type__": "cc.Node", "_name": "_userAttackLb", "_objFlags": 0, "_parent": {"__id__": 170}, "_children": [], "_active": true, "_components": [{"__id__": 244}, {"__id__": 246}, {"__id__": 248}], "_prefab": {"__id__": 250}, "_lpos": {"__type__": "cc.Vec3", "x": -13.774, "y": -3.657, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_id": "", "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 243}, "_enabled": true, "__prefab": {"__id__": 245}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8"}, "_visFlags": 0, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_useOriginalSize": true, "_string": "00", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 0, "_fontSize": 40, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 40, "_overflow": 0, "_enableWrapText": true, "_font": {"__uuid__": "6c499e91-b85d-4647-8f0e-0f22d10a798c"}, "_isSystemFontUsed": false, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 0, "_cacheMode": 0, "_spacingX": 0, "_batchAsBitmap": false, "_styleFlags": 1, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "f2E3kSmdpB4aIryL98g0BT"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "node": {"__id__": 243}, "_enabled": true, "__prefab": {"__id__": 247}, "_opacity": 255}, {"__type__": "cc.CompPrefabInfo", "fileId": "dbOwmfJ+dI6ZutyUxYjqub"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 243}, "_enabled": true, "__prefab": {"__id__": 249}, "_priority": 0, "_contentSize": {"__type__": "cc.Size", "width": 39.18, "height": 50.4}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}}, {"__type__": "cc.CompPrefabInfo", "fileId": "93qx2wCI9IeJ4RLSMW3wYj"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "37BwYHXPdFBbVzbgo0eOwX"}, {"__type__": "cc.Node", "_name": "_userDefenseLb", "_objFlags": 0, "_parent": {"__id__": 170}, "_children": [], "_active": true, "_components": [{"__id__": 252}, {"__id__": 254}, {"__id__": 256}], "_prefab": {"__id__": 258}, "_lpos": {"__type__": "cc.Vec3", "x": 291.498, "y": -3.657, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_id": "", "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 251}, "_enabled": true, "__prefab": {"__id__": 253}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8"}, "_visFlags": 0, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_useOriginalSize": true, "_string": "00", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 0, "_fontSize": 40, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 40, "_overflow": 0, "_enableWrapText": true, "_font": {"__uuid__": "6c499e91-b85d-4647-8f0e-0f22d10a798c"}, "_isSystemFontUsed": false, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 0, "_cacheMode": 0, "_spacingX": 0, "_batchAsBitmap": false, "_styleFlags": 1, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "48xxsCexJJOJD5PUhQUP0J"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "node": {"__id__": 251}, "_enabled": true, "__prefab": {"__id__": 255}, "_opacity": 255}, {"__type__": "cc.CompPrefabInfo", "fileId": "b1ojabEQtPeqC/vrZg6J9h"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 251}, "_enabled": true, "__prefab": {"__id__": 257}, "_priority": 0, "_contentSize": {"__type__": "cc.Size", "width": 39.18, "height": 50.4}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}}, {"__type__": "cc.CompPrefabInfo", "fileId": "cdOs/kIYlAGoLP6RcldgQ3"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "afBU8zlepPLLO03miaUNNg"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 170}, "_enabled": true, "__prefab": {"__id__": 260}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8"}, "_visFlags": 0, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "823865b9-2bb6-4178-bf9f-91a16075c93d@f9941"}, "_type": 0, "_fillType": 0, "_sizeMode": 2, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "4cdS3j8r9Czpx59f/7WEAi"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 170}, "_enabled": true, "__prefab": {"__id__": 262}, "_alignFlags": 9, "_target": null, "_left": 0, "_right": 0, "_top": 0, "_bottom": 0, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_alignMode": 2, "_lockFlags": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "98iqCiqRJCvpeNT6iyB5El"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "node": {"__id__": 170}, "_enabled": true, "__prefab": {"__id__": 264}, "_opacity": 255}, {"__type__": "cc.CompPrefabInfo", "fileId": "64ELKx/etHnLCFSqTCAtyY"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 170}, "_enabled": true, "__prefab": {"__id__": 266}, "_priority": 0, "_contentSize": {"__type__": "cc.Size", "width": 819, "height": 353}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}}, {"__type__": "cc.CompPrefabInfo", "fileId": "45SRXF2z1NarVyksANLFj9"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "b3uve5mENEgoUoTO941cVQ"}, {"__type__": "cc.Node", "_name": "_tempHp", "_objFlags": 0, "_parent": {"__id__": 161}, "_children": [], "_active": false, "_components": [{"__id__": 269}, {"__id__": 271}, {"__id__": 273}], "_prefab": {"__id__": 275}, "_lpos": {"__type__": "cc.Vec3", "x": 810.529, "y": -83.766, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_id": "", "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 268}, "_enabled": true, "__prefab": {"__id__": 270}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8"}, "_visFlags": 0, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_useOriginalSize": true, "_string": "+0", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 0, "_fontSize": 40, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 40, "_overflow": 0, "_enableWrapText": true, "_font": {"__uuid__": "6c499e91-b85d-4647-8f0e-0f22d10a798c"}, "_isSystemFontUsed": false, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 0, "_cacheMode": 0, "_spacingX": 0, "_batchAsBitmap": false, "_styleFlags": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "78BAYymRFBKpS19iLny3d7"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "node": {"__id__": 268}, "_enabled": true, "__prefab": {"__id__": 272}, "_opacity": 255}, {"__type__": "cc.CompPrefabInfo", "fileId": "0ajlRvUaBHFpj7h3h2vr0X"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 268}, "_enabled": true, "__prefab": {"__id__": 274}, "_priority": 0, "_contentSize": {"__type__": "cc.Size", "width": 45.61, "height": 50.4}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}}, {"__type__": "cc.CompPrefabInfo", "fileId": "9fwTLBBx9EzrxhiDzRSbau"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "62pGhAmqxAQZCEaf6oNDVt"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "node": {"__id__": 161}, "_enabled": true, "__prefab": {"__id__": 277}, "_opacity": 255}, {"__type__": "cc.CompPrefabInfo", "fileId": "5d4QNG4v9O4JRIFCTTG3ra"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 161}, "_enabled": true, "__prefab": {"__id__": 279}, "_priority": 0, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 0}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}}, {"__type__": "cc.CompPrefabInfo", "fileId": "4chiIUCoVBrKEEj8HJ3Hb/"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "e41LcosFtGUKwC5Pd3gxcb"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 160}, "_enabled": true, "__prefab": {"__id__": 282}, "_alignFlags": 9, "_target": null, "_left": 84.42929730000014, "_right": 0, "_top": 21.118864850000023, "_bottom": 0, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_alignMode": 2, "_lockFlags": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "21pJ8+CNZAYYj7lXqmRZWa"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "node": {"__id__": 160}, "_enabled": true, "__prefab": {"__id__": 284}, "_opacity": 255}, {"__type__": "cc.CompPrefabInfo", "fileId": "e8LkBmvfJN5Z6ra5Tit8h+"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 160}, "_enabled": true, "__prefab": {"__id__": 286}, "_priority": 0, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 0}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}}, {"__type__": "cc.CompPrefabInfo", "fileId": "0evdls3ExGYYVBj9yFttb/"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "96JeT61HlHYJ3ou1f/DLFm"}, {"__type__": "cc.Node", "_name": "monsterBord", "_objFlags": 512, "_parent": {"__id__": 1}, "_children": [{"__id__": 289}], "_active": true, "_components": [{"__id__": 361}, {"__id__": 363}, {"__id__": 365}], "_prefab": {"__id__": 367}, "_lpos": {"__type__": "cc.Vec3", "x": 564.441, "y": 838.126, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_id": "", "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "_<PERSON><PERSON><PERSON>", "_objFlags": 0, "_parent": {"__id__": 288}, "_children": [{"__id__": 290}, {"__id__": 298}], "_active": true, "_components": [{"__id__": 356}, {"__id__": 358}], "_prefab": {"__id__": 360}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_id": "", "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "bg-2", "_objFlags": 0, "_parent": {"__id__": 289}, "_children": [], "_active": true, "_components": [{"__id__": 291}, {"__id__": 293}, {"__id__": 295}], "_prefab": {"__id__": 297}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_id": "", "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 290}, "_enabled": true, "__prefab": {"__id__": 292}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8"}, "_visFlags": 0, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "3483e6eb-85f8-4d73-9897-44cd9acf6b7e@f9941"}, "_type": 0, "_fillType": 0, "_sizeMode": 2, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "3729+v/VtGM5aG/oyWqkGC"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "node": {"__id__": 290}, "_enabled": true, "__prefab": {"__id__": 294}, "_opacity": 255}, {"__type__": "cc.CompPrefabInfo", "fileId": "30prdOL0NK4ZhoXBr67rmV"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 290}, "_enabled": true, "__prefab": {"__id__": 296}, "_priority": 0, "_contentSize": {"__type__": "cc.Size", "width": 747, "height": 106}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}}, {"__type__": "cc.CompPrefabInfo", "fileId": "ea/rI9O8hNepVXOS7yeMrs"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "c9c16ffddHhodfbcPDhkua"}, {"__type__": "cc.Node", "_name": "monster", "_objFlags": 0, "_parent": {"__id__": 289}, "_children": [{"__id__": 299}, {"__id__": 307}, {"__id__": 315}, {"__id__": 323}], "_active": true, "_components": [{"__id__": 347}, {"__id__": 349}, {"__id__": 351}, {"__id__": 353}], "_prefab": {"__id__": 355}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": -100, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_id": "", "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "_monsNameLb", "_objFlags": 0, "_parent": {"__id__": 298}, "_children": [], "_active": true, "_components": [{"__id__": 300}, {"__id__": 302}, {"__id__": 304}], "_prefab": {"__id__": 306}, "_lpos": {"__type__": "cc.Vec3", "x": -289.855, "y": 11.293, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_id": "", "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 299}, "_enabled": true, "__prefab": {"__id__": 301}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8"}, "_visFlags": 0, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_useOriginalSize": true, "_string": "00", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 0, "_fontSize": 60, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 60, "_overflow": 0, "_enableWrapText": true, "_font": {"__uuid__": "6c499e91-b85d-4647-8f0e-0f22d10a798c"}, "_isSystemFontUsed": false, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 0, "_cacheMode": 0, "_spacingX": 0, "_batchAsBitmap": false, "_styleFlags": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "84tFigHnJIvaT2V7Kyhhl0"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "node": {"__id__": 299}, "_enabled": true, "__prefab": {"__id__": 303}, "_opacity": 255}, {"__type__": "cc.CompPrefabInfo", "fileId": "613byLtoxHSY51bAyxNYRK"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 299}, "_enabled": true, "__prefab": {"__id__": 305}, "_priority": 0, "_contentSize": {"__type__": "cc.Size", "width": 58.77, "height": 75.6}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}}, {"__type__": "cc.CompPrefabInfo", "fileId": "f008OTZNBLKYaPZ0irBVu2"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "c4MHIJOlRII4zzge3ny+yT"}, {"__type__": "cc.Node", "_name": "_monsAttackLb", "_objFlags": 0, "_parent": {"__id__": 298}, "_children": [], "_active": true, "_components": [{"__id__": 308}, {"__id__": 310}, {"__id__": 312}], "_prefab": {"__id__": 314}, "_lpos": {"__type__": "cc.Vec3", "x": 166.61, "y": -49.407, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_id": "", "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 307}, "_enabled": true, "__prefab": {"__id__": 309}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8"}, "_visFlags": 0, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_useOriginalSize": true, "_string": "00", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 0, "_fontSize": 40, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 40, "_overflow": 0, "_enableWrapText": true, "_font": {"__uuid__": "6c499e91-b85d-4647-8f0e-0f22d10a798c"}, "_isSystemFontUsed": false, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 0, "_cacheMode": 0, "_spacingX": 0, "_batchAsBitmap": false, "_styleFlags": 1, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "5alWJMgPNNpodtjb4N88tg"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "node": {"__id__": 307}, "_enabled": true, "__prefab": {"__id__": 311}, "_opacity": 255}, {"__type__": "cc.CompPrefabInfo", "fileId": "17ane8BLFAdp1vXceuVn3E"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 307}, "_enabled": true, "__prefab": {"__id__": 313}, "_priority": 0, "_contentSize": {"__type__": "cc.Size", "width": 39.18, "height": 50.4}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}}, {"__type__": "cc.CompPrefabInfo", "fileId": "9a+btTezdBx6lINtORxMpD"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "b5u3bW+H1EMYgyc87/1iHO"}, {"__type__": "cc.Node", "_name": "_monsDefenseLb", "_objFlags": 0, "_parent": {"__id__": 298}, "_children": [], "_active": true, "_components": [{"__id__": 316}, {"__id__": 318}, {"__id__": 320}], "_prefab": {"__id__": 322}, "_lpos": {"__type__": "cc.Vec3", "x": 422.211, "y": -53.328, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_id": "", "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 315}, "_enabled": true, "__prefab": {"__id__": 317}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8"}, "_visFlags": 0, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_useOriginalSize": true, "_string": "00", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 0, "_fontSize": 40, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 40, "_overflow": 0, "_enableWrapText": true, "_font": {"__uuid__": "6c499e91-b85d-4647-8f0e-0f22d10a798c"}, "_isSystemFontUsed": false, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 0, "_cacheMode": 0, "_spacingX": 0, "_batchAsBitmap": false, "_styleFlags": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "fdDaUIssRJsoOC/kE/2Gl8"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "node": {"__id__": 315}, "_enabled": true, "__prefab": {"__id__": 319}, "_opacity": 255}, {"__type__": "cc.CompPrefabInfo", "fileId": "24n8mCnZJLM7B0cXSwMRn5"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 315}, "_enabled": true, "__prefab": {"__id__": 321}, "_priority": 0, "_contentSize": {"__type__": "cc.Size", "width": 39.18, "height": 50.4}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}}, {"__type__": "cc.CompPrefabInfo", "fileId": "23o3Us4H1Bg4U7wgptzELM"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "8a3B4jKg1Keb2BY7amkfn4"}, {"__type__": "cc.Node", "_name": "_monsHp", "_objFlags": 0, "_parent": {"__id__": 298}, "_children": [{"__id__": 324}, {"__id__": 332}], "_active": true, "_components": [{"__id__": 340}, {"__id__": 342}, {"__id__": 344}], "_prefab": {"__id__": 346}, "_lpos": {"__type__": "cc.Vec3", "x": 304.049, "y": 40.582, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 0.8, "y": 0.8, "z": 0.777}, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_id": "", "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "_monsHpProgress", "_objFlags": 0, "_parent": {"__id__": 323}, "_children": [], "_active": true, "_components": [{"__id__": 325}, {"__id__": 327}, {"__id__": 329}], "_prefab": {"__id__": 331}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_id": "", "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 324}, "_enabled": true, "__prefab": {"__id__": 326}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8"}, "_visFlags": 0, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "b4137058-885c-4abc-ab9e-24179fa3dbf8@f9941"}, "_type": 3, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0.6, "_fillRange": -1, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "f5peeKvVRJH5kh66+O/Ylh"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "node": {"__id__": 324}, "_enabled": true, "__prefab": {"__id__": 328}, "_opacity": 255}, {"__type__": "cc.CompPrefabInfo", "fileId": "491/FQ2SFP6YsEGqSw5hTq"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 324}, "_enabled": true, "__prefab": {"__id__": 330}, "_priority": 0, "_contentSize": {"__type__": "cc.Size", "width": 414, "height": 91}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}}, {"__type__": "cc.CompPrefabInfo", "fileId": "08PwGZtz5KWawNu0O1AWP5"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "a8vJHflkpH64AEyFRtj9f5"}, {"__type__": "cc.Node", "_name": "_monsHpLb", "_objFlags": 0, "_parent": {"__id__": 323}, "_children": [], "_active": true, "_components": [{"__id__": 333}, {"__id__": 335}, {"__id__": 337}], "_prefab": {"__id__": 339}, "_lpos": {"__type__": "cc.Vec3", "x": -13.774, "y": -3.657, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_id": "", "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 332}, "_enabled": true, "__prefab": {"__id__": 334}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8"}, "_visFlags": 0, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_useOriginalSize": true, "_string": "00", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 0, "_fontSize": 40, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 40, "_overflow": 0, "_enableWrapText": true, "_font": {"__uuid__": "6c499e91-b85d-4647-8f0e-0f22d10a798c"}, "_isSystemFontUsed": false, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 0, "_cacheMode": 0, "_spacingX": 0, "_batchAsBitmap": false, "_styleFlags": 1, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "08+7Hj1jpLkYz3jOqZd03c"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "node": {"__id__": 332}, "_enabled": true, "__prefab": {"__id__": 336}, "_opacity": 255}, {"__type__": "cc.CompPrefabInfo", "fileId": "740HUVs4hFb4J0/NouaQ1r"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 332}, "_enabled": true, "__prefab": {"__id__": 338}, "_priority": 0, "_contentSize": {"__type__": "cc.Size", "width": 39.18, "height": 50.4}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}}, {"__type__": "cc.CompPrefabInfo", "fileId": "59Awp1tQtLPqEgQ/3D1ZLS"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "42aRTnm7lELJXEVIYx2D+q"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 323}, "_enabled": true, "__prefab": {"__id__": 341}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8"}, "_visFlags": 0, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "2198e76c-58b0-4c0c-89f8-a0b769f25d97@f9941"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "4e61DkGLlDYJ7ialMk53iU"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "node": {"__id__": 323}, "_enabled": true, "__prefab": {"__id__": 343}, "_opacity": 255}, {"__type__": "cc.CompPrefabInfo", "fileId": "a80ejrrgZC8ZMRc5eD3bVV"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 323}, "_enabled": true, "__prefab": {"__id__": 345}, "_priority": 0, "_contentSize": {"__type__": "cc.Size", "width": 414, "height": 91}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}}, {"__type__": "cc.CompPrefabInfo", "fileId": "75NXqV33NIuYQIN9IA7HXk"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "b4pnWdt3BBdYb/+Kypsux7"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 298}, "_enabled": true, "__prefab": {"__id__": 348}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8"}, "_visFlags": 0, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "d0f6b731-acd2-4a4f-a4d2-10663d0cfb7a@f9941"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "c5yhO7vKFMVJa8uMzgl88E"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 298}, "_enabled": true, "__prefab": {"__id__": 350}, "_alignFlags": 17, "_target": null, "_left": 0, "_right": 0, "_top": 0, "_bottom": 0, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_alignMode": 2, "_lockFlags": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "c492GzJpJKJ4MSQgPk9CxC"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "node": {"__id__": 298}, "_enabled": true, "__prefab": {"__id__": 352}, "_opacity": 255}, {"__type__": "cc.CompPrefabInfo", "fileId": "1dVRWuNLJMarpZoN47+f/C"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 298}, "_enabled": true, "__prefab": {"__id__": 354}, "_priority": 0, "_contentSize": {"__type__": "cc.Size", "width": 1051, "height": 200}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}}, {"__type__": "cc.CompPrefabInfo", "fileId": "b7EjXH3yVDNLBrfXOKtdYH"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "27al/3a9FO351Xj0MHnIwv"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "node": {"__id__": 289}, "_enabled": true, "__prefab": {"__id__": 357}, "_opacity": 255}, {"__type__": "cc.CompPrefabInfo", "fileId": "195fa4fWROMqu2D1x9t6P6"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 289}, "_enabled": true, "__prefab": {"__id__": 359}, "_priority": 0, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 0}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}}, {"__type__": "cc.CompPrefabInfo", "fileId": "e2QNI8bptFOLiEEGSRvVDa"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "0aPeb75ERFhbhBM+e36dw3"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 288}, "_enabled": true, "__prefab": {"__id__": 362}, "_alignFlags": 17, "_target": null, "_left": 0, "_right": 0, "_top": 26.738864850000027, "_bottom": 0, "_horizontalCenter": 564.441, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_alignMode": 2, "_lockFlags": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "32bZ5vzhZOxo3pHs/LsuHy"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "node": {"__id__": 288}, "_enabled": true, "__prefab": {"__id__": 364}, "_opacity": 255}, {"__type__": "cc.CompPrefabInfo", "fileId": "31sIKdkE9MuKXUUuw1ABTR"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 288}, "_enabled": true, "__prefab": {"__id__": 366}, "_priority": 0, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 0}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}}, {"__type__": "cc.CompPrefabInfo", "fileId": "68fuusvJ1FcoDptM0VW1OP"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "dcjKPgcuZNCYk92SNab4V1"}, {"__type__": "cc.Node", "_name": "bottomBored", "_objFlags": 0, "_parent": {"__id__": 1}, "_children": [{"__id__": 369}, {"__id__": 455}], "_active": true, "_components": [{"__id__": 689}, {"__id__": 691}, {"__id__": 693}, {"__id__": 695}], "_prefab": {"__id__": 697}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": -587.86486485, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_id": "", "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "_cardPage", "_objFlags": 0, "_parent": {"__id__": 368}, "_children": [{"__id__": 370}, {"__id__": 382}, {"__id__": 436}, {"__id__": 444}], "_active": true, "_components": [{"__id__": 450}, {"__id__": 452}], "_prefab": {"__id__": 454}, "_lpos": {"__type__": "cc.Vec3", "x": 246.494, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 2.7027}, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_id": "", "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "_btn", "_objFlags": 0, "_parent": {"__id__": 369}, "_children": [], "_active": true, "_components": [{"__id__": 371}, {"__id__": 373}, {"__id__": 375}, {"__id__": 377}, {"__id__": 379}], "_prefab": {"__id__": 381}, "_lpos": {"__type__": "cc.Vec3", "x": 966.199, "y": 303.105, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_id": "", "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 370}, "_enabled": true, "__prefab": {"__id__": 372}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8"}, "_visFlags": 0, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_useOriginalSize": true, "_string": "add_card", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 0, "_fontSize": 40, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 40, "_overflow": 0, "_enableWrapText": true, "_font": null, "_isSystemFontUsed": true, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 0, "_cacheMode": 0, "_spacingX": 0, "_batchAsBitmap": false, "_styleFlags": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "29mLaYCtFJeo19GRLfVg9w"}, {"__type__": "cc.<PERSON><PERSON>", "_name": "", "_objFlags": 0, "node": {"__id__": 370}, "_enabled": true, "__prefab": {"__id__": 374}, "clickEvents": [], "_interactable": true, "_transition": 3, "_normalColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_hoverColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_pressedColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "_disabledColor": {"__type__": "cc.Color", "r": 124, "g": 124, "b": 124, "a": 255}, "_normalSprite": null, "_hoverSprite": null, "_pressedSprite": null, "_disabledSprite": null, "_duration": 0.1, "_zoomScale": 1.2, "_target": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "f5pqvQ68pIwbqseVrf4Wo4"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 370}, "_enabled": false, "__prefab": {"__id__": 376}, "_alignFlags": 9, "_target": null, "_left": 1882.799, "_right": 0, "_top": -78.305, "_bottom": 0, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_alignMode": 2, "_lockFlags": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "d9luTcMRFESpPhSePcy/4z"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "node": {"__id__": 370}, "_enabled": true, "__prefab": {"__id__": 378}, "_opacity": 255}, {"__type__": "cc.CompPrefabInfo", "fileId": "5bUTxVLUROfY25uTTT/gq4"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 370}, "_enabled": true, "__prefab": {"__id__": 380}, "_priority": 0, "_contentSize": {"__type__": "cc.Size", "width": 166.8, "height": 50.4}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}}, {"__type__": "cc.CompPrefabInfo", "fileId": "5cy4McU+xMhodiGq3fArUu"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "04mA47oyNAw6ob6CyXPde4"}, {"__type__": "cc.Node", "_name": "_cardRoot", "_objFlags": 0, "_parent": {"__id__": 369}, "_children": [{"__id__": 383}, {"__id__": 391}, {"__id__": 399}, {"__id__": 407}, {"__id__": 415}, {"__id__": 423}], "_active": true, "_components": [{"__id__": 431}, {"__id__": 433}], "_prefab": {"__id__": 435}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_id": "", "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "card-clothing-1", "_objFlags": 0, "_parent": {"__id__": 382}, "_children": [], "_active": true, "_components": [{"__id__": 384}, {"__id__": 386}, {"__id__": 388}], "_prefab": {"__id__": 390}, "_lpos": {"__type__": "cc.Vec3", "x": -865, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_id": "", "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 383}, "_enabled": false, "__prefab": {"__id__": 385}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8"}, "_visFlags": 0, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "6a7f8d7e-ebda-4116-be83-b0804c517b33@f9941"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "ddzHsp4e1GTrsTXldYOuu0"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "node": {"__id__": 383}, "_enabled": true, "__prefab": {"__id__": 387}, "_opacity": 255}, {"__type__": "cc.CompPrefabInfo", "fileId": "d9xbFEkRdENZnvRNNsehwk"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 383}, "_enabled": true, "__prefab": {"__id__": 389}, "_priority": 0, "_contentSize": {"__type__": "cc.Size", "width": 366, "height": 501}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}}, {"__type__": "cc.CompPrefabInfo", "fileId": "12lUIRDn5HbJ0dnbWYBMwT"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "96o/u067hAF7hKU041SRUv"}, {"__type__": "cc.Node", "_name": "card-clothing-1", "_objFlags": 0, "_parent": {"__id__": 382}, "_children": [], "_active": true, "_components": [{"__id__": 392}, {"__id__": 394}, {"__id__": 396}], "_prefab": {"__id__": 398}, "_lpos": {"__type__": "cc.Vec3", "x": -519, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_id": "", "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 391}, "_enabled": false, "__prefab": {"__id__": 393}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8"}, "_visFlags": 0, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "6a7f8d7e-ebda-4116-be83-b0804c517b33@f9941"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "76JQB/kCBNZpvvanLJuOTO"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "node": {"__id__": 391}, "_enabled": true, "__prefab": {"__id__": 395}, "_opacity": 255}, {"__type__": "cc.CompPrefabInfo", "fileId": "fdv3lpX6VNxYjFAAlzlukP"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 391}, "_enabled": true, "__prefab": {"__id__": 397}, "_priority": 0, "_contentSize": {"__type__": "cc.Size", "width": 366, "height": 501}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}}, {"__type__": "cc.CompPrefabInfo", "fileId": "a6wNEoosBGrr3IpBcqJZj1"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "c57Z4b4d5DNqoPbZ9DDVMM"}, {"__type__": "cc.Node", "_name": "card-clothing-1", "_objFlags": 0, "_parent": {"__id__": 382}, "_children": [], "_active": true, "_components": [{"__id__": 400}, {"__id__": 402}, {"__id__": 404}], "_prefab": {"__id__": 406}, "_lpos": {"__type__": "cc.Vec3", "x": -173, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_id": "", "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 399}, "_enabled": false, "__prefab": {"__id__": 401}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8"}, "_visFlags": 0, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "6a7f8d7e-ebda-4116-be83-b0804c517b33@f9941"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "acin9RRcRNvq6aYKdCbu6l"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "node": {"__id__": 399}, "_enabled": true, "__prefab": {"__id__": 403}, "_opacity": 255}, {"__type__": "cc.CompPrefabInfo", "fileId": "2ajJfQb2BDyr65uQFY0sU3"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 399}, "_enabled": true, "__prefab": {"__id__": 405}, "_priority": 0, "_contentSize": {"__type__": "cc.Size", "width": 366, "height": 501}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}}, {"__type__": "cc.CompPrefabInfo", "fileId": "e14DYyF39ARqfy5pUZATQe"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "8dSZU4CAdNP75C11WwA0TZ"}, {"__type__": "cc.Node", "_name": "card-clothing-1", "_objFlags": 0, "_parent": {"__id__": 382}, "_children": [], "_active": true, "_components": [{"__id__": 408}, {"__id__": 410}, {"__id__": 412}], "_prefab": {"__id__": 414}, "_lpos": {"__type__": "cc.Vec3", "x": 173, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_id": "", "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 407}, "_enabled": false, "__prefab": {"__id__": 409}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8"}, "_visFlags": 0, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "6a7f8d7e-ebda-4116-be83-b0804c517b33@f9941"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "6bxKQsteZMXaX3bYiB0BpC"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "node": {"__id__": 407}, "_enabled": true, "__prefab": {"__id__": 411}, "_opacity": 255}, {"__type__": "cc.CompPrefabInfo", "fileId": "db7tjYvcdBPYdFhf75qBwx"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 407}, "_enabled": true, "__prefab": {"__id__": 413}, "_priority": 0, "_contentSize": {"__type__": "cc.Size", "width": 366, "height": 501}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}}, {"__type__": "cc.CompPrefabInfo", "fileId": "91N3rVmR1IoIcF6tBjrdhE"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "dec4fBJMVESr74b6McbRDt"}, {"__type__": "cc.Node", "_name": "card-clothing-1", "_objFlags": 0, "_parent": {"__id__": 382}, "_children": [], "_active": true, "_components": [{"__id__": 416}, {"__id__": 418}, {"__id__": 420}], "_prefab": {"__id__": 422}, "_lpos": {"__type__": "cc.Vec3", "x": 519, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_id": "", "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 415}, "_enabled": false, "__prefab": {"__id__": 417}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8"}, "_visFlags": 0, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "6a7f8d7e-ebda-4116-be83-b0804c517b33@f9941"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "6atHwKZpJAtrxvQ+T6q2Tk"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "node": {"__id__": 415}, "_enabled": true, "__prefab": {"__id__": 419}, "_opacity": 255}, {"__type__": "cc.CompPrefabInfo", "fileId": "bddS90JKtDk76wGFHeHfR/"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 415}, "_enabled": true, "__prefab": {"__id__": 421}, "_priority": 0, "_contentSize": {"__type__": "cc.Size", "width": 366, "height": 501}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}}, {"__type__": "cc.CompPrefabInfo", "fileId": "c5d9IAbXRDh4ztV/7vM4v1"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "1bsB629NVIQ6ov6ACcaELQ"}, {"__type__": "cc.Node", "_name": "card-clothing-1", "_objFlags": 0, "_parent": {"__id__": 382}, "_children": [], "_active": true, "_components": [{"__id__": 424}, {"__id__": 426}, {"__id__": 428}], "_prefab": {"__id__": 430}, "_lpos": {"__type__": "cc.Vec3", "x": 865, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_id": "", "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 423}, "_enabled": false, "__prefab": {"__id__": 425}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8"}, "_visFlags": 0, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "6a7f8d7e-ebda-4116-be83-b0804c517b33@f9941"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "39JJDHecZMS4JINxS9EZme"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "node": {"__id__": 423}, "_enabled": true, "__prefab": {"__id__": 427}, "_opacity": 255}, {"__type__": "cc.CompPrefabInfo", "fileId": "78x9dVYmJPrZgqlymBJCpt"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 423}, "_enabled": true, "__prefab": {"__id__": 429}, "_priority": 0, "_contentSize": {"__type__": "cc.Size", "width": 366, "height": 501}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}}, {"__type__": "cc.CompPrefabInfo", "fileId": "6aZrQtGWpOcadRe4O1wGot"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "fcichAfHVMyZoRQTbIBcDa"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "node": {"__id__": 382}, "_enabled": true, "__prefab": {"__id__": 432}, "_opacity": 255}, {"__type__": "cc.CompPrefabInfo", "fileId": "20qjlGcdFNvquk4m4BCQj2"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 382}, "_enabled": true, "__prefab": {"__id__": 434}, "_priority": 0, "_contentSize": {"__type__": "cc.Size", "width": 2096, "height": 400}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}}, {"__type__": "cc.CompPrefabInfo", "fileId": "b62HXtEwdPSKKHt14h4BAE"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "68GbjIEkZAIJEqKTcO/skR"}, {"__type__": "cc.Node", "_name": "_discard", "_objFlags": 0, "_parent": {"__id__": 369}, "_children": [], "_active": true, "_components": [{"__id__": 437}, {"__id__": 439}, {"__id__": 441}], "_prefab": {"__id__": 443}, "_lpos": {"__type__": "cc.Vec3", "x": -246.494, "y": -200, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_id": "", "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 436}, "_enabled": true, "__prefab": {"__id__": 438}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8"}, "_visFlags": 0, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "957c2eb6-37dd-4497-aa2e-67361ecaeb9a@f9941"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "28iUmmUOdOjL9MXdzs2+jE"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "node": {"__id__": 436}, "_enabled": true, "__prefab": {"__id__": 440}, "_opacity": 255}, {"__type__": "cc.CompPrefabInfo", "fileId": "73nBK7zlBJtrORCWeSYi8d"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 436}, "_enabled": true, "__prefab": {"__id__": 442}, "_priority": 0, "_contentSize": {"__type__": "cc.Size", "width": 2547, "height": 168}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}}, {"__type__": "cc.CompPrefabInfo", "fileId": "dbb9zbtNxFT4hrpJWbSont"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "2d2EV6cb5FJJiyL290/w7B"}, {"__type__": "cc.Node", "_name": "_topCard", "_objFlags": 0, "_parent": {"__id__": 369}, "_children": [], "_active": true, "_components": [{"__id__": 445}, {"__id__": 447}], "_prefab": {"__id__": 449}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_id": "", "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "node": {"__id__": 444}, "_enabled": true, "__prefab": {"__id__": 446}, "_opacity": 255}, {"__type__": "cc.CompPrefabInfo", "fileId": "87aoBzQchGPZZ5yn/4HXJK"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 444}, "_enabled": true, "__prefab": {"__id__": 448}, "_priority": 0, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 0}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}}, {"__type__": "cc.CompPrefabInfo", "fileId": "8arDxjZVhAZpVtoxZgLgAG"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "36J4e9v/dH3qBiG2Ls34KV"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "node": {"__id__": 369}, "_enabled": true, "__prefab": {"__id__": 451}, "_opacity": 255}, {"__type__": "cc.CompPrefabInfo", "fileId": "aaLgRxlOVHgoiXFk0np4Ju"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 369}, "_enabled": true, "__prefab": {"__id__": 453}, "_priority": 0, "_contentSize": {"__type__": "cc.Size", "width": 2000, "height": 500}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}}, {"__type__": "cc.CompPrefabInfo", "fileId": "96YVu30d5DsbvHV6d1oGVT"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "62lmUooPlF7av8p4qii7BT"}, {"__type__": "cc.Node", "_name": "equipment", "_objFlags": 0, "_parent": {"__id__": 368}, "_children": [{"__id__": 456}, {"__id__": 494}, {"__id__": 532}, {"__id__": 570}, {"__id__": 608}, {"__id__": 646}], "_active": true, "_components": [{"__id__": 684}, {"__id__": 686}], "_prefab": {"__id__": 688}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_id": "", "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "_hat", "_objFlags": 0, "_parent": {"__id__": 455}, "_children": [{"__id__": 457}, {"__id__": 465}, {"__id__": 473}], "_active": true, "_components": [{"__id__": 489}, {"__id__": 491}], "_prefab": {"__id__": 493}, "_lpos": {"__type__": "cc.Vec3", "x": -1075.747, "y": 186.598, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_id": "", "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "_eqBord", "_objFlags": 0, "_parent": {"__id__": 456}, "_children": [], "_active": true, "_components": [{"__id__": 458}, {"__id__": 460}, {"__id__": 462}], "_prefab": {"__id__": 464}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_id": "", "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 457}, "_enabled": true, "__prefab": {"__id__": 459}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8"}, "_visFlags": 0, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "681c29f0-ecb8-474a-a6cb-294bfdfe01c4@f9941"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "a1amBss8xBB5IlfCyHm+3R"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "node": {"__id__": 457}, "_enabled": true, "__prefab": {"__id__": 461}, "_opacity": 255}, {"__type__": "cc.CompPrefabInfo", "fileId": "14Wd0WQ7hISoicoJiugQve"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 457}, "_enabled": true, "__prefab": {"__id__": 463}, "_priority": 0, "_contentSize": {"__type__": "cc.Size", "width": 134, "height": 138}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}}, {"__type__": "cc.CompPrefabInfo", "fileId": "58IvSR5rVBwbc46cro0Q6Z"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "153zQHITtO4akDscOq0ghn"}, {"__type__": "cc.Node", "_name": "_eqIcon", "_objFlags": 0, "_parent": {"__id__": 456}, "_children": [], "_active": true, "_components": [{"__id__": 466}, {"__id__": 468}, {"__id__": 470}], "_prefab": {"__id__": 472}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 0.75, "y": 0.75, "z": 0.808}, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_id": "", "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 465}, "_enabled": true, "__prefab": {"__id__": 467}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8"}, "_visFlags": 0, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "d0b56a79-62d9-43fa-ae64-5ae522befbb6@f9941"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "47bNOCM+9J96c1QIZiP8lU"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "node": {"__id__": 465}, "_enabled": true, "__prefab": {"__id__": 469}, "_opacity": 255}, {"__type__": "cc.CompPrefabInfo", "fileId": "00hghnxXtNsodEUd76m2qH"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 465}, "_enabled": true, "__prefab": {"__id__": 471}, "_priority": 0, "_contentSize": {"__type__": "cc.Size", "width": 81, "height": 96}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}}, {"__type__": "cc.CompPrefabInfo", "fileId": "b46yTBwTZBpLmT4LbGss9U"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "1aNy4IzzlBObnszISLCM6o"}, {"__type__": "cc.Node", "_name": "_eqProgressGroup", "_objFlags": 0, "_parent": {"__id__": 456}, "_children": [{"__id__": 474}], "_active": true, "_components": [{"__id__": 482}, {"__id__": 484}, {"__id__": 486}], "_prefab": {"__id__": 488}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": -88.997, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_id": "", "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "_eqProgress", "_objFlags": 0, "_parent": {"__id__": 473}, "_children": [], "_active": true, "_components": [{"__id__": 475}, {"__id__": 477}, {"__id__": 479}], "_prefab": {"__id__": 481}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_id": "", "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 474}, "_enabled": true, "__prefab": {"__id__": 476}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8"}, "_visFlags": 0, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "34655e80-df82-469d-be7b-8807ebcbe816@f9941"}, "_type": 3, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0.8, "_fillRange": -1, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "cf7v/DQPFFXqa1CZX0SX9c"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "node": {"__id__": 474}, "_enabled": true, "__prefab": {"__id__": 478}, "_opacity": 255}, {"__type__": "cc.CompPrefabInfo", "fileId": "39IMqA62FPJrt+QzD4tYDe"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 474}, "_enabled": true, "__prefab": {"__id__": 480}, "_priority": 0, "_contentSize": {"__type__": "cc.Size", "width": 123, "height": 27}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}}, {"__type__": "cc.CompPrefabInfo", "fileId": "4ahQ4RZfJH7I3C318i6HK7"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "e5NSwJW5hAv5Tc9Av1incz"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 473}, "_enabled": true, "__prefab": {"__id__": 483}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8"}, "_visFlags": 0, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "fb147611-5dcc-4003-8e72-ecb5b95c4be2@f9941"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "38rIY/rkNNUoxMJkv9ryj+"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "node": {"__id__": 473}, "_enabled": true, "__prefab": {"__id__": 485}, "_opacity": 255}, {"__type__": "cc.CompPrefabInfo", "fileId": "f6EFO3/6xHbZKyY+jnuuCW"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 473}, "_enabled": true, "__prefab": {"__id__": 487}, "_priority": 0, "_contentSize": {"__type__": "cc.Size", "width": 123, "height": 27}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}}, {"__type__": "cc.CompPrefabInfo", "fileId": "e2KhPOdTFOko7MWkgNdJMX"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "6aB+JDvllLx7fxpUhFiS5S"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "node": {"__id__": 456}, "_enabled": true, "__prefab": {"__id__": 490}, "_opacity": 255}, {"__type__": "cc.CompPrefabInfo", "fileId": "c65VQK36pJAL6vaNnv3BFT"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 456}, "_enabled": true, "__prefab": {"__id__": 492}, "_priority": 0, "_contentSize": {"__type__": "cc.Size", "width": 81, "height": 96}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}}, {"__type__": "cc.CompPrefabInfo", "fileId": "7ab737Ue1KCrIhfydfzyUc"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "633vYFU9lK3Ic7uiQ+WkjP"}, {"__type__": "cc.Node", "_name": "_cloth", "_objFlags": 0, "_parent": {"__id__": 455}, "_children": [{"__id__": 495}, {"__id__": 503}, {"__id__": 511}], "_active": true, "_components": [{"__id__": 527}, {"__id__": 529}], "_prefab": {"__id__": 531}, "_lpos": {"__type__": "cc.Vec3", "x": -1077.824, "y": -0.185, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_id": "", "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "_eqBord", "_objFlags": 0, "_parent": {"__id__": 494}, "_children": [], "_active": true, "_components": [{"__id__": 496}, {"__id__": 498}, {"__id__": 500}], "_prefab": {"__id__": 502}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_id": "", "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 495}, "_enabled": true, "__prefab": {"__id__": 497}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8"}, "_visFlags": 0, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "681c29f0-ecb8-474a-a6cb-294bfdfe01c4@f9941"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "f8rRQorL9GlZIr6RAMGJ+6"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "node": {"__id__": 495}, "_enabled": true, "__prefab": {"__id__": 499}, "_opacity": 255}, {"__type__": "cc.CompPrefabInfo", "fileId": "0eTpLWJ4RN4bWcPvrfvqJo"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 495}, "_enabled": true, "__prefab": {"__id__": 501}, "_priority": 0, "_contentSize": {"__type__": "cc.Size", "width": 134, "height": 138}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}}, {"__type__": "cc.CompPrefabInfo", "fileId": "55hTkSuAZGCIGZlHVrbM5q"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "09B74+uytDxrwng5Ade/5L"}, {"__type__": "cc.Node", "_name": "_eqIcon", "_objFlags": 0, "_parent": {"__id__": 494}, "_children": [], "_active": true, "_components": [{"__id__": 504}, {"__id__": 506}, {"__id__": 508}], "_prefab": {"__id__": 510}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 0.75, "y": 0.75, "z": 0.808}, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_id": "", "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 503}, "_enabled": true, "__prefab": {"__id__": 505}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8"}, "_visFlags": 0, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "bfb55a56-3edf-4e7c-843d-cb33920aa327@f9941"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "67oLTe6HFLkrMUjGbTCqla"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "node": {"__id__": 503}, "_enabled": true, "__prefab": {"__id__": 507}, "_opacity": 255}, {"__type__": "cc.CompPrefabInfo", "fileId": "dbu3pU9ARBt4m8+u2l6odF"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 503}, "_enabled": true, "__prefab": {"__id__": 509}, "_priority": 0, "_contentSize": {"__type__": "cc.Size", "width": 101, "height": 84}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}}, {"__type__": "cc.CompPrefabInfo", "fileId": "3dzBjAGVtIYKMp4M5zJTti"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "36QEnKttBG6ZXX9/7m72tU"}, {"__type__": "cc.Node", "_name": "_eqProgressGroup", "_objFlags": 0, "_parent": {"__id__": 494}, "_children": [{"__id__": 512}], "_active": true, "_components": [{"__id__": 520}, {"__id__": 522}, {"__id__": 524}], "_prefab": {"__id__": 526}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": -88.997, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_id": "", "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "_eqProgress", "_objFlags": 0, "_parent": {"__id__": 511}, "_children": [], "_active": true, "_components": [{"__id__": 513}, {"__id__": 515}, {"__id__": 517}], "_prefab": {"__id__": 519}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_id": "", "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 512}, "_enabled": true, "__prefab": {"__id__": 514}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8"}, "_visFlags": 0, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "34655e80-df82-469d-be7b-8807ebcbe816@f9941"}, "_type": 3, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 1, "_fillRange": -1, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "a38XTO9ZpO9phlAy4h7H38"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "node": {"__id__": 512}, "_enabled": true, "__prefab": {"__id__": 516}, "_opacity": 255}, {"__type__": "cc.CompPrefabInfo", "fileId": "0ezHX1cWBCTL41sQT5lB3g"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 512}, "_enabled": true, "__prefab": {"__id__": 518}, "_priority": 0, "_contentSize": {"__type__": "cc.Size", "width": 123, "height": 27}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}}, {"__type__": "cc.CompPrefabInfo", "fileId": "c4BryfO4ZN/o9haqpO3s9H"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "5bFUcJQ7hITrQkhC56rDvz"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 511}, "_enabled": true, "__prefab": {"__id__": 521}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8"}, "_visFlags": 0, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "fb147611-5dcc-4003-8e72-ecb5b95c4be2@f9941"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "30mF+voFFPk7N4c45zAayB"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "node": {"__id__": 511}, "_enabled": true, "__prefab": {"__id__": 523}, "_opacity": 255}, {"__type__": "cc.CompPrefabInfo", "fileId": "d1RKfcceBMUIXkRN2OJdsa"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 511}, "_enabled": true, "__prefab": {"__id__": 525}, "_priority": 0, "_contentSize": {"__type__": "cc.Size", "width": 123, "height": 27}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}}, {"__type__": "cc.CompPrefabInfo", "fileId": "7eD7FDV7pALYMPUMGjk5zO"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "9eQYd/kLVB3pm1pTyJV1O6"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "node": {"__id__": 494}, "_enabled": true, "__prefab": {"__id__": 528}, "_opacity": 255}, {"__type__": "cc.CompPrefabInfo", "fileId": "c3RcLY5bZIAKw9DQhcE6QN"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 494}, "_enabled": true, "__prefab": {"__id__": 530}, "_priority": 0, "_contentSize": {"__type__": "cc.Size", "width": 101, "height": 84}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}}, {"__type__": "cc.CompPrefabInfo", "fileId": "9bx7uEV4xM+68w99Kpe82q"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "28pYzCM/dCKLA5gvzgN1ww"}, {"__type__": "cc.Node", "_name": "_pants", "_objFlags": 0, "_parent": {"__id__": 455}, "_children": [{"__id__": 533}, {"__id__": 541}, {"__id__": 549}], "_active": true, "_components": [{"__id__": 565}, {"__id__": 567}], "_prefab": {"__id__": 569}, "_lpos": {"__type__": "cc.Vec3", "x": -1074.957, "y": -176.275, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_id": "", "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "_eqIcon", "_objFlags": 0, "_parent": {"__id__": 532}, "_children": [], "_active": true, "_components": [{"__id__": 534}, {"__id__": 536}, {"__id__": 538}], "_prefab": {"__id__": 540}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 0.75, "y": 0.75, "z": 0.808}, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_id": "", "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 533}, "_enabled": true, "__prefab": {"__id__": 535}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8"}, "_visFlags": 0, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "2b02b5ec-7ada-4da8-b119-703902ac6f9c@f9941"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "13gF5b0e1BLb/vqV5glnX0"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "node": {"__id__": 533}, "_enabled": true, "__prefab": {"__id__": 537}, "_opacity": 255}, {"__type__": "cc.CompPrefabInfo", "fileId": "38iEWZ5qpNl7FFxHqA3Zqo"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 533}, "_enabled": true, "__prefab": {"__id__": 539}, "_priority": 0, "_contentSize": {"__type__": "cc.Size", "width": 67, "height": 107}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}}, {"__type__": "cc.CompPrefabInfo", "fileId": "b45wXVnndCr6ZJTIDtPjx8"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "b2l1Y3FxpKaazk+BNKUqcK"}, {"__type__": "cc.Node", "_name": "_eqBord", "_objFlags": 0, "_parent": {"__id__": 532}, "_children": [], "_active": true, "_components": [{"__id__": 542}, {"__id__": 544}, {"__id__": 546}], "_prefab": {"__id__": 548}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_id": "", "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 541}, "_enabled": true, "__prefab": {"__id__": 543}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8"}, "_visFlags": 0, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "681c29f0-ecb8-474a-a6cb-294bfdfe01c4@f9941"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "d5Hn93OUZMC7BablJkS1TI"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "node": {"__id__": 541}, "_enabled": true, "__prefab": {"__id__": 545}, "_opacity": 255}, {"__type__": "cc.CompPrefabInfo", "fileId": "acLoilqA9Df5vU6YSIWCob"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 541}, "_enabled": true, "__prefab": {"__id__": 547}, "_priority": 0, "_contentSize": {"__type__": "cc.Size", "width": 134, "height": 138}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}}, {"__type__": "cc.CompPrefabInfo", "fileId": "b3Joo8Fo1EQr8FJIPRXCb4"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "64j4Uok+1Iyait9Kn/57pm"}, {"__type__": "cc.Node", "_name": "_eqProgressGroup", "_objFlags": 0, "_parent": {"__id__": 532}, "_children": [{"__id__": 550}], "_active": true, "_components": [{"__id__": 558}, {"__id__": 560}, {"__id__": 562}], "_prefab": {"__id__": 564}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": -88.997, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_id": "", "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "_eqProgress", "_objFlags": 0, "_parent": {"__id__": 549}, "_children": [], "_active": true, "_components": [{"__id__": 551}, {"__id__": 553}, {"__id__": 555}], "_prefab": {"__id__": 557}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_id": "", "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 550}, "_enabled": true, "__prefab": {"__id__": 552}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8"}, "_visFlags": 0, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "34655e80-df82-469d-be7b-8807ebcbe816@f9941"}, "_type": 3, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0.2, "_fillRange": -1, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "f6nojLkBpLOL0gnqDGVwV+"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "node": {"__id__": 550}, "_enabled": true, "__prefab": {"__id__": 554}, "_opacity": 255}, {"__type__": "cc.CompPrefabInfo", "fileId": "39/RAReaFOeojIwjFW1Jrl"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 550}, "_enabled": true, "__prefab": {"__id__": 556}, "_priority": 0, "_contentSize": {"__type__": "cc.Size", "width": 123, "height": 27}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}}, {"__type__": "cc.CompPrefabInfo", "fileId": "21fVDo3d5IxqZQMfdYJyEv"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "39dpYuvY1ITYrSfMDbRRU/"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 549}, "_enabled": true, "__prefab": {"__id__": 559}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8"}, "_visFlags": 0, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "fb147611-5dcc-4003-8e72-ecb5b95c4be2@f9941"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "a8gCA/oLBEILgDf1vvanL+"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "node": {"__id__": 549}, "_enabled": true, "__prefab": {"__id__": 561}, "_opacity": 255}, {"__type__": "cc.CompPrefabInfo", "fileId": "f46HX3hI1PsaucEtbnuKMA"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 549}, "_enabled": true, "__prefab": {"__id__": 563}, "_priority": 0, "_contentSize": {"__type__": "cc.Size", "width": 123, "height": 27}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}}, {"__type__": "cc.CompPrefabInfo", "fileId": "75VnpSWLpLqqHioGHcsT4k"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "09/lUC/2FPPLCfYZkA/GDZ"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "node": {"__id__": 532}, "_enabled": true, "__prefab": {"__id__": 566}, "_opacity": 255}, {"__type__": "cc.CompPrefabInfo", "fileId": "5aR00alRRInbN8BzwvggM9"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 532}, "_enabled": true, "__prefab": {"__id__": 568}, "_priority": 0, "_contentSize": {"__type__": "cc.Size", "width": 67, "height": 107}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}}, {"__type__": "cc.CompPrefabInfo", "fileId": "daM3eyaBZF1aPlFuwk2inr"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "0f163BAjxJQZ1KsrC4pqwh"}, {"__type__": "cc.Node", "_name": "_shoes", "_objFlags": 0, "_parent": {"__id__": 455}, "_children": [{"__id__": 571}, {"__id__": 579}, {"__id__": 587}], "_active": true, "_components": [{"__id__": 603}, {"__id__": 605}], "_prefab": {"__id__": 607}, "_lpos": {"__type__": "cc.Vec3", "x": -915.576, "y": -175.772, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_id": "", "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "_eqIcon", "_objFlags": 0, "_parent": {"__id__": 570}, "_children": [], "_active": true, "_components": [{"__id__": 572}, {"__id__": 574}, {"__id__": 576}], "_prefab": {"__id__": 578}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 0.75, "y": 0.75, "z": 0.808}, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_id": "", "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 571}, "_enabled": true, "__prefab": {"__id__": 573}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8"}, "_visFlags": 0, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "df02b447-56d9-4a20-85a5-0725bf670f8e@f9941"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "6cHdolZFBL86PBIjwwh0Od"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "node": {"__id__": 571}, "_enabled": true, "__prefab": {"__id__": 575}, "_opacity": 255}, {"__type__": "cc.CompPrefabInfo", "fileId": "fcIpN3x41GyYSXvDDcs5uI"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 571}, "_enabled": true, "__prefab": {"__id__": 577}, "_priority": 0, "_contentSize": {"__type__": "cc.Size", "width": 87, "height": 96}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}}, {"__type__": "cc.CompPrefabInfo", "fileId": "a0xjvBj2JJkZg4y9WnnTCe"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "5bcAVd6JJHr5NTF84Zgmrj"}, {"__type__": "cc.Node", "_name": "_eqBord", "_objFlags": 0, "_parent": {"__id__": 570}, "_children": [], "_active": true, "_components": [{"__id__": 580}, {"__id__": 582}, {"__id__": 584}], "_prefab": {"__id__": 586}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_id": "", "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 579}, "_enabled": true, "__prefab": {"__id__": 581}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8"}, "_visFlags": 0, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "681c29f0-ecb8-474a-a6cb-294bfdfe01c4@f9941"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "c0EIutothNqLNz8ohsKryK"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "node": {"__id__": 579}, "_enabled": true, "__prefab": {"__id__": 583}, "_opacity": 255}, {"__type__": "cc.CompPrefabInfo", "fileId": "78co/13AdHdaZpRHvzza+S"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 579}, "_enabled": true, "__prefab": {"__id__": 585}, "_priority": 0, "_contentSize": {"__type__": "cc.Size", "width": 134, "height": 138}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}}, {"__type__": "cc.CompPrefabInfo", "fileId": "ea5zfGuB9IiqQDnY7Bsi5b"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "2fclGZsGdOU6o44IuTjVlD"}, {"__type__": "cc.Node", "_name": "_eqProgressGroup", "_objFlags": 0, "_parent": {"__id__": 570}, "_children": [{"__id__": 588}], "_active": true, "_components": [{"__id__": 596}, {"__id__": 598}, {"__id__": 600}], "_prefab": {"__id__": 602}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": -88.997, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_id": "", "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "_eqProgress", "_objFlags": 0, "_parent": {"__id__": 587}, "_children": [], "_active": true, "_components": [{"__id__": 589}, {"__id__": 591}, {"__id__": 593}], "_prefab": {"__id__": 595}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_id": "", "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 588}, "_enabled": true, "__prefab": {"__id__": 590}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8"}, "_visFlags": 0, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "34655e80-df82-469d-be7b-8807ebcbe816@f9941"}, "_type": 3, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0.2, "_fillRange": -1, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "2bCafJiDRLIKih1H/st1r+"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "node": {"__id__": 588}, "_enabled": true, "__prefab": {"__id__": 592}, "_opacity": 255}, {"__type__": "cc.CompPrefabInfo", "fileId": "9eENHL94dHN6odjT6Yp/V1"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 588}, "_enabled": true, "__prefab": {"__id__": 594}, "_priority": 0, "_contentSize": {"__type__": "cc.Size", "width": 123, "height": 27}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}}, {"__type__": "cc.CompPrefabInfo", "fileId": "54ED8QGelDdYU0moABp367"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "4fjT+4ODNKU4lWAKPT1y48"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 587}, "_enabled": true, "__prefab": {"__id__": 597}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8"}, "_visFlags": 0, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "fb147611-5dcc-4003-8e72-ecb5b95c4be2@f9941"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "acqNdmd+FCKZ7Gw714JBVL"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "node": {"__id__": 587}, "_enabled": true, "__prefab": {"__id__": 599}, "_opacity": 255}, {"__type__": "cc.CompPrefabInfo", "fileId": "74zsecpb5BMK07m7Xqrff5"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 587}, "_enabled": true, "__prefab": {"__id__": 601}, "_priority": 0, "_contentSize": {"__type__": "cc.Size", "width": 123, "height": 27}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}}, {"__type__": "cc.CompPrefabInfo", "fileId": "34tpRteuBJZK7YwVw2To4l"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "7cNAXvaFRF8Lf6M4tRxjrG"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "node": {"__id__": 570}, "_enabled": true, "__prefab": {"__id__": 604}, "_opacity": 255}, {"__type__": "cc.CompPrefabInfo", "fileId": "94xyLQX9RDxb+2aPxv7QNA"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 570}, "_enabled": true, "__prefab": {"__id__": 606}, "_priority": 0, "_contentSize": {"__type__": "cc.Size", "width": 87, "height": 96}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}}, {"__type__": "cc.CompPrefabInfo", "fileId": "1bmTPXjpBFKY1/xwpJSg/u"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "2cbelVkBJB46bM5HBxrWUo"}, {"__type__": "cc.Node", "_name": "_shield", "_objFlags": 0, "_parent": {"__id__": 455}, "_children": [{"__id__": 609}, {"__id__": 617}, {"__id__": 625}], "_active": true, "_components": [{"__id__": 641}, {"__id__": 643}], "_prefab": {"__id__": 645}, "_lpos": {"__type__": "cc.Vec3", "x": -913.75, "y": 89.958, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_id": "", "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "_eqIcon", "_objFlags": 0, "_parent": {"__id__": 608}, "_children": [], "_active": true, "_components": [{"__id__": 610}, {"__id__": 612}, {"__id__": 614}], "_prefab": {"__id__": 616}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 0.75, "y": 0.75, "z": 0.808}, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_id": "", "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 609}, "_enabled": true, "__prefab": {"__id__": 611}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8"}, "_visFlags": 0, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "82ebadbf-c442-431e-a6ec-fd97eba7a0a8@f9941"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "04GDD+yH9D/7vzi+LWVBO3"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "node": {"__id__": 609}, "_enabled": true, "__prefab": {"__id__": 613}, "_opacity": 255}, {"__type__": "cc.CompPrefabInfo", "fileId": "7e5TYTxbRJ1JP3AvU7X9pu"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 609}, "_enabled": true, "__prefab": {"__id__": 615}, "_priority": 0, "_contentSize": {"__type__": "cc.Size", "width": 96, "height": 164}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}}, {"__type__": "cc.CompPrefabInfo", "fileId": "429RW8JoJESIraMdHjD8Om"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "45iYBFvRlA5IMsZEs2TNFd"}, {"__type__": "cc.Node", "_name": "_eqBord", "_objFlags": 0, "_parent": {"__id__": 608}, "_children": [], "_active": true, "_components": [{"__id__": 618}, {"__id__": 620}, {"__id__": 622}], "_prefab": {"__id__": 624}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_id": "", "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 617}, "_enabled": true, "__prefab": {"__id__": 619}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8"}, "_visFlags": 0, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "681c29f0-ecb8-474a-a6cb-294bfdfe01c4@f9941"}, "_type": 1, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "cbmzWUyeFK9IVzIyennaoI"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "node": {"__id__": 617}, "_enabled": true, "__prefab": {"__id__": 621}, "_opacity": 255}, {"__type__": "cc.CompPrefabInfo", "fileId": "52xrZ3UHJFMqkDdScbEHVH"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 617}, "_enabled": true, "__prefab": {"__id__": 623}, "_priority": 0, "_contentSize": {"__type__": "cc.Size", "width": 165.6, "height": 265.2}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}}, {"__type__": "cc.CompPrefabInfo", "fileId": "54Vzq880RNMJJDf9e0LVRe"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "77AG2Pu5VH3I/ofzSE3+7l"}, {"__type__": "cc.Node", "_name": "_eqProgressGroup", "_objFlags": 0, "_parent": {"__id__": 608}, "_children": [{"__id__": 626}], "_active": true, "_components": [{"__id__": 634}, {"__id__": 636}, {"__id__": 638}], "_prefab": {"__id__": 640}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": -153.881, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_id": "", "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "_eqProgress", "_objFlags": 0, "_parent": {"__id__": 625}, "_children": [], "_active": true, "_components": [{"__id__": 627}, {"__id__": 629}, {"__id__": 631}], "_prefab": {"__id__": 633}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_id": "", "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 626}, "_enabled": true, "__prefab": {"__id__": 628}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8"}, "_visFlags": 0, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "34655e80-df82-469d-be7b-8807ebcbe816@f9941"}, "_type": 3, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0.2, "_fillRange": -1, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "e2Surml11F95kGHaDtvHNO"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "node": {"__id__": 626}, "_enabled": true, "__prefab": {"__id__": 630}, "_opacity": 255}, {"__type__": "cc.CompPrefabInfo", "fileId": "2aNpxB2iFOVoSlMrBgEDYR"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 626}, "_enabled": true, "__prefab": {"__id__": 632}, "_priority": 0, "_contentSize": {"__type__": "cc.Size", "width": 123, "height": 27}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}}, {"__type__": "cc.CompPrefabInfo", "fileId": "3aXco8YRBEDYnaY1V7QMlI"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "68s/j2cdhAyqFdKu7ubYi5"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 625}, "_enabled": true, "__prefab": {"__id__": 635}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8"}, "_visFlags": 0, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "fb147611-5dcc-4003-8e72-ecb5b95c4be2@f9941"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "b1XqSu5mBO4LlpM9Eh0sU3"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "node": {"__id__": 625}, "_enabled": true, "__prefab": {"__id__": 637}, "_opacity": 255}, {"__type__": "cc.CompPrefabInfo", "fileId": "20x5jd+I1K465QhciGcFgq"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 625}, "_enabled": true, "__prefab": {"__id__": 639}, "_priority": 0, "_contentSize": {"__type__": "cc.Size", "width": 123, "height": 27}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}}, {"__type__": "cc.CompPrefabInfo", "fileId": "abvM5TaYhLFKIJRL6jz7TN"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "713szADMlEq4USNuJdqwox"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "node": {"__id__": 608}, "_enabled": true, "__prefab": {"__id__": 642}, "_opacity": 255}, {"__type__": "cc.CompPrefabInfo", "fileId": "92VVtNsJlJUqC0KuJMUOhc"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 608}, "_enabled": true, "__prefab": {"__id__": 644}, "_priority": 0, "_contentSize": {"__type__": "cc.Size", "width": 96, "height": 164}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}}, {"__type__": "cc.CompPrefabInfo", "fileId": "82qQZSoelHpp8eTrvopcjR"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "fdXfQFkRNAEpN4RvgrU4qf"}, {"__type__": "cc.Node", "_name": "_sword", "_objFlags": 0, "_parent": {"__id__": 455}, "_children": [{"__id__": 647}, {"__id__": 655}, {"__id__": 663}], "_active": true, "_components": [{"__id__": 679}, {"__id__": 681}], "_prefab": {"__id__": 683}, "_lpos": {"__type__": "cc.Vec3", "x": -1219.147, "y": -58.503, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_id": "", "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "_eqIcon", "_objFlags": 0, "_parent": {"__id__": 646}, "_children": [], "_active": true, "_components": [{"__id__": 648}, {"__id__": 650}, {"__id__": 652}], "_prefab": {"__id__": 654}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 0.75, "y": 0.75, "z": 0.808}, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_id": "", "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 647}, "_enabled": true, "__prefab": {"__id__": 649}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8"}, "_visFlags": 0, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "9d1f8369-25ea-42d0-99b4-9e06f5520bc6@f9941"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "f3vU7INJxIoqQaELuFQ5Kf"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "node": {"__id__": 647}, "_enabled": true, "__prefab": {"__id__": 651}, "_opacity": 255}, {"__type__": "cc.CompPrefabInfo", "fileId": "43tsB3MSdOqoNezyq7p25h"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 647}, "_enabled": true, "__prefab": {"__id__": 653}, "_priority": 0, "_contentSize": {"__type__": "cc.Size", "width": 77, "height": 221}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}}, {"__type__": "cc.CompPrefabInfo", "fileId": "02adOhF8tPCLl8r1IE4KpK"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "959eAS65JDWZ8e9OXISx8I"}, {"__type__": "cc.Node", "_name": "_eqBord", "_objFlags": 0, "_parent": {"__id__": 646}, "_children": [], "_active": true, "_components": [{"__id__": 656}, {"__id__": 658}, {"__id__": 660}], "_prefab": {"__id__": 662}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_id": "", "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 655}, "_enabled": true, "__prefab": {"__id__": 657}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8"}, "_visFlags": 0, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "681c29f0-ecb8-474a-a6cb-294bfdfe01c4@f9941"}, "_type": 1, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "74ktNNARBNl5eDtrrcPkdv"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "node": {"__id__": 655}, "_enabled": true, "__prefab": {"__id__": 659}, "_opacity": 255}, {"__type__": "cc.CompPrefabInfo", "fileId": "bb0B8Os0RDG7tmoUjjBpte"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 655}, "_enabled": true, "__prefab": {"__id__": 661}, "_priority": 0, "_contentSize": {"__type__": "cc.Size", "width": 142.3, "height": 244.7}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}}, {"__type__": "cc.CompPrefabInfo", "fileId": "2dlX4MgDdL74W2QMla87Ba"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "8bhG+TBhVPdaYoQGUxmT8i"}, {"__type__": "cc.Node", "_name": "_eqProgressGroup", "_objFlags": 0, "_parent": {"__id__": 646}, "_children": [{"__id__": 664}], "_active": true, "_components": [{"__id__": 672}, {"__id__": 674}, {"__id__": 676}], "_prefab": {"__id__": 678}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": -149.535, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_id": "", "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "_eqProgress", "_objFlags": 0, "_parent": {"__id__": 663}, "_children": [], "_active": true, "_components": [{"__id__": 665}, {"__id__": 667}, {"__id__": 669}], "_prefab": {"__id__": 671}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_id": "", "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 664}, "_enabled": true, "__prefab": {"__id__": 666}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8"}, "_visFlags": 0, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "34655e80-df82-469d-be7b-8807ebcbe816@f9941"}, "_type": 3, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0.6, "_fillRange": -1, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "ceYLBWryNFd7jmmg4BXaXi"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "node": {"__id__": 664}, "_enabled": true, "__prefab": {"__id__": 668}, "_opacity": 255}, {"__type__": "cc.CompPrefabInfo", "fileId": "92XE8LbwpBwYjDJjQWoMeH"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 664}, "_enabled": true, "__prefab": {"__id__": 670}, "_priority": 0, "_contentSize": {"__type__": "cc.Size", "width": 123, "height": 27}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}}, {"__type__": "cc.CompPrefabInfo", "fileId": "a2LmCJ36BDUII2aTYBfTvM"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "39N/38oZBH4L4v3xr8+XWb"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 663}, "_enabled": true, "__prefab": {"__id__": 673}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8"}, "_visFlags": 0, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "fb147611-5dcc-4003-8e72-ecb5b95c4be2@f9941"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "c6IPf6OaNI2It3QuxrWaLM"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "node": {"__id__": 663}, "_enabled": true, "__prefab": {"__id__": 675}, "_opacity": 255}, {"__type__": "cc.CompPrefabInfo", "fileId": "f7vj5a9UFLrJtgfzI8rnUM"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 663}, "_enabled": true, "__prefab": {"__id__": 677}, "_priority": 0, "_contentSize": {"__type__": "cc.Size", "width": 123, "height": 27}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}}, {"__type__": "cc.CompPrefabInfo", "fileId": "daNmDAV5lB5753c/2CJHtE"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "06j2ph67tBMIgKu7CiSw/c"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "node": {"__id__": 646}, "_enabled": true, "__prefab": {"__id__": 680}, "_opacity": 255}, {"__type__": "cc.CompPrefabInfo", "fileId": "79rPlNCmpOv5L8wyJQtr8Z"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 646}, "_enabled": true, "__prefab": {"__id__": 682}, "_priority": 0, "_contentSize": {"__type__": "cc.Size", "width": 77, "height": 221}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}}, {"__type__": "cc.CompPrefabInfo", "fileId": "a7c0sD3B5CmZJ11mtYGa9j"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "e28lCTyCBC04tLyes/L4GV"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "node": {"__id__": 455}, "_enabled": true, "__prefab": {"__id__": 685}, "_opacity": 255}, {"__type__": "cc.CompPrefabInfo", "fileId": "ffIkygyylOYrS/mR0uPvY/"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 455}, "_enabled": true, "__prefab": {"__id__": 687}, "_priority": 0, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 0}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}}, {"__type__": "cc.CompPrefabInfo", "fileId": "953r02gjpCVpoQ7mA+nL/0"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "afuoqcsHNJTILk7aoFtzOT"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 368}, "_enabled": true, "__prefab": {"__id__": 690}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8"}, "_visFlags": 0, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "9bd696fd-a748-45e4-8959-0aa4c049789f@f9941"}, "_type": 0, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "2fjGrEBnpOdZlD6HZkLVOj"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 368}, "_enabled": true, "__prefab": {"__id__": 692}, "_alignFlags": 20, "_target": null, "_left": 0, "_right": 0, "_top": 0, "_bottom": 0, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_alignMode": 2, "_lockFlags": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "9ezsfD9SVIhascHQbGJBwI"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "node": {"__id__": 368}, "_enabled": true, "__prefab": {"__id__": 694}, "_opacity": 255}, {"__type__": "cc.CompPrefabInfo", "fileId": "2aiNbz9U5IbqRhCQ3ldVfn"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 368}, "_enabled": true, "__prefab": {"__id__": 696}, "_priority": 0, "_contentSize": {"__type__": "cc.Size", "width": 2600, "height": 554}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}}, {"__type__": "cc.CompPrefabInfo", "fileId": "2dJ2yzmthCn7+zhH50GeLq"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "c09+sju79BU4YlcmLa5ydW"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "node": {"__id__": 1}, "_enabled": true, "__prefab": {"__id__": 699}, "_opacity": 255}, {"__type__": "cc.CompPrefabInfo", "fileId": "cdkp6dIRBByro8Lgv4MR3W"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 1}, "_enabled": true, "__prefab": {"__id__": 701}, "_priority": 0, "_contentSize": {"__type__": "cc.Size", "width": 2594.5945946, "height": 1729.7297297}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}}, {"__type__": "cc.CompPrefabInfo", "fileId": "a6yeGtvTROK5VilmsFiCpV"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "afW8qhZb9C9bQSs9FtuM77"}]