/**
 * 重构后的攻击动作类
 * 提供更清晰的攻击行为管理
 */

const { ccclass, property } = cc._decorator;

/**
 * 攻击动作属性接口
 */
export interface IAttackActionProps {
    /** 前摇时间，攻击开始到造成伤害的时间（毫秒） */
    hurtStartTimeMs: number;
    
    /** 攻击持续时间，从开始到结束的总时间（毫秒） */
    hurtEndTimeMs: number;
    
    /** 开始造成伤害时的回调 */
    onHurtStart?: () => void;
    
    /** 攻击结束时的回调 */
    onHurtEnd?: () => void;
    
    /** 攻击过程中的回调（可选） */
    onAttackProgress?: (progress: number) => void;
}

/**
 * 攻击状态枚举
 */
export enum AttackState {
    /** 空闲状态 */
    IDLE = "idle",
    
    /** 前摇阶段 */
    WINDUP = "windup",
    
    /** 伤害阶段 */
    DAMAGE = "damage",
    
    /** 后摇阶段 */
    RECOVERY = "recovery"
}

/**
 * 攻击动作类
 */
@ccclass
export class AttackAction extends cc.Component {
    // 当前攻击状态
    private _currentState: AttackState = AttackState.IDLE;
    
    // 攻击计时器
    private _attackTimer: number = 0;
    
    // 当前攻击属性
    private _currentProps: IAttackActionProps | null = null;
    
    // 回调标记
    private _hurtStartTriggered: boolean = false;
    private _hurtEndTriggered: boolean = false;
    
    // 攻击队列
    private _attackQueue: IAttackActionProps[] = [];
    
    // 是否允许攻击队列
    @property(cc.Boolean)
    public allowQueue: boolean = false;
    
    // 最大队列长度
    @property(cc.Integer)
    public maxQueueLength: number = 3;
    
    /**
     * 获取当前攻击状态
     */
    get currentState(): AttackState {
        return this._currentState;
    }
    
    /**
     * 是否正在攻击
     */
    get isAttacking(): boolean {
        return this._currentState !== AttackState.IDLE;
    }
    
    /**
     * 是否可以开始新的攻击
     */
    get canStartNewAttack(): boolean {
        return this._currentState === AttackState.IDLE || 
               (this.allowQueue && this._attackQueue.length < this.maxQueueLength);
    }
    
    /**
     * 获取攻击进度 (0-1)
     */
    get attackProgress(): number {
        if (!this._currentProps || this._currentState === AttackState.IDLE) {
            return 0;
        }
        
        return Math.min(1, this._attackTimer / this._currentProps.hurtEndTimeMs);
    }
    
    /**
     * 执行一次攻击
     * @param props 攻击属性
     * @returns 是否成功开始攻击
     */
    doAttackOnce(props: IAttackActionProps): boolean {
        // 验证攻击属性
        if (!this.validateAttackProps(props)) {
            console.error("Invalid attack properties");
            return false;
        }
        
        if (this._currentState === AttackState.IDLE) {
            // 直接开始攻击
            this.startAttack(props);
            return true;
        } else if (this.allowQueue && this._attackQueue.length < this.maxQueueLength) {
            // 添加到队列
            this._attackQueue.push(props);
            return true;
        }
        
        return false;
    }
    
    /**
     * 中断当前攻击
     */
    interruptAttack(): void {
        if (this._currentState !== AttackState.IDLE) {
            this.resetAttack();
            this.setState(AttackState.IDLE);
        }
    }
    
    /**
     * 清空攻击队列
     */
    clearQueue(): void {
        this._attackQueue.length = 0;
    }
    
    /**
     * 获取队列中的攻击数量
     */
    getQueueLength(): number {
        return this._attackQueue.length;
    }
    
    /**
     * 更新攻击状态
     */
    protected update(dt: number): void {
        if (this._currentState === AttackState.IDLE) {
            return;
        }
        
        this.updateAttackTimer(dt);
        this.updateAttackState();
        this.triggerCallbacks();
    }
    
    /**
     * 组件销毁时清理
     */
    protected onDestroy(): void {
        this.interruptAttack();
        this.clearQueue();
    }
    
    /**
     * 开始攻击
     */
    private startAttack(props: IAttackActionProps): void {
        this._currentProps = props;
        this._attackTimer = 0;
        this._hurtStartTriggered = false;
        this._hurtEndTriggered = false;
        
        this.setState(AttackState.WINDUP);
    }
    
    /**
     * 更新攻击计时器
     */
    private updateAttackTimer(dt: number): void {
        this._attackTimer += dt * 1000; // 转换为毫秒
    }
    
    /**
     * 更新攻击状态
     */
    private updateAttackState(): void {
        if (!this._currentProps) {
            return;
        }
        
        const { hurtStartTimeMs, hurtEndTimeMs } = this._currentProps;
        
        if (this._attackTimer >= hurtEndTimeMs) {
            // 攻击结束
            this.setState(AttackState.IDLE);
            this.processNextAttack();
        } else if (this._attackTimer >= hurtStartTimeMs && this._currentState === AttackState.WINDUP) {
            // 进入伤害阶段
            this.setState(AttackState.DAMAGE);
        }
    }
    
    /**
     * 触发回调
     */
    private triggerCallbacks(): void {
        if (!this._currentProps) {
            return;
        }
        
        const { hurtStartTimeMs, hurtEndTimeMs, onHurtStart, onHurtEnd, onAttackProgress } = this._currentProps;
        
        // 触发伤害开始回调
        if (!this._hurtStartTriggered && this._attackTimer >= hurtStartTimeMs) {
            this._hurtStartTriggered = true;
            onHurtStart?.();
        }
        
        // 触发攻击进度回调
        if (onAttackProgress) {
            onAttackProgress(this.attackProgress);
        }
        
        // 触发攻击结束回调
        if (!this._hurtEndTriggered && this._attackTimer >= hurtEndTimeMs) {
            this._hurtEndTriggered = true;
            onHurtEnd?.();
        }
    }
    
    /**
     * 设置攻击状态
     */
    private setState(newState: AttackState): void {
        if (this._currentState !== newState) {
            const oldState = this._currentState;
            this._currentState = newState;
            
            this.onStateChanged(oldState, newState);
        }
    }
    
    /**
     * 状态改变时的处理
     */
    private onStateChanged(oldState: AttackState, newState: AttackState): void {
        // 发送状态改变事件
        this.node.emit("attackStateChanged", {
            oldState,
            newState,
            component: this
        });
        
        // 状态特定的处理
        switch (newState) {
            case AttackState.IDLE:
                this.onEnterIdle();
                break;
            case AttackState.WINDUP:
                this.onEnterWindup();
                break;
            case AttackState.DAMAGE:
                this.onEnterDamage();
                break;
            case AttackState.RECOVERY:
                this.onEnterRecovery();
                break;
        }
    }
    
    /**
     * 进入空闲状态
     */
    private onEnterIdle(): void {
        this.resetAttack();
    }
    
    /**
     * 进入前摇状态
     */
    private onEnterWindup(): void {
        // 可以在这里添加前摇特效
    }
    
    /**
     * 进入伤害状态
     */
    private onEnterDamage(): void {
        // 可以在这里添加伤害特效
    }
    
    /**
     * 进入后摇状态
     */
    private onEnterRecovery(): void {
        // 可以在这里添加后摇特效
    }
    
    /**
     * 重置攻击状态
     */
    private resetAttack(): void {
        this._currentProps = null;
        this._attackTimer = 0;
        this._hurtStartTriggered = false;
        this._hurtEndTriggered = false;
    }
    
    /**
     * 处理下一个攻击
     */
    private processNextAttack(): void {
        if (this._attackQueue.length > 0) {
            const nextAttack = this._attackQueue.shift()!;
            this.startAttack(nextAttack);
        }
    }
    
    /**
     * 验证攻击属性
     */
    private validateAttackProps(props: IAttackActionProps): boolean {
        if (!props) {
            return false;
        }
        
        if (props.hurtStartTimeMs < 0 || props.hurtEndTimeMs < 0) {
            return false;
        }
        
        if (props.hurtStartTimeMs > props.hurtEndTimeMs) {
            return false;
        }
        
        return true;
    }
    
    /**
     * 获取攻击信息
     */
    public getAttackInfo(): any {
        return {
            currentState: this._currentState,
            isAttacking: this.isAttacking,
            attackProgress: this.attackProgress,
            queueLength: this._attackQueue.length,
            canStartNewAttack: this.canStartNewAttack,
            currentProps: this._currentProps ? {
                hurtStartTimeMs: this._currentProps.hurtStartTimeMs,
                hurtEndTimeMs: this._currentProps.hurtEndTimeMs
            } : null
        };
    }
}
