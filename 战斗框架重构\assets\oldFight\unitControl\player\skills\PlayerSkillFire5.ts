import FightCtrl from "../../../../ctrl/FightCtrl";
import { UtilsExtends } from "../../../../util/UtilsExtends";
import { TimelineEvent, TimelineObj, TimelineNode, TimelineModel } from "../../../timeline/TimelineObj";
import { BulletLauncher, BulletModel } from "../../bullet/BulletObj";
import { Skill, SkillModel } from "../../CharacterSkillObj";
import { CharacterControl } from "../../CharacterControl";
import { PlayerBulletHitCommon } from "../bulletOnHit/PlayerBulletHitCommon";
import { SkillNames } from "./SkillNames";
import { BulletTweenForwardFireArrowPlayer } from "../bulletTween/BulletTweenForwardFireArrowPlayer";
import { MaxHpHit, PlayerSkill1Hit } from "../bulletOnHit/PlayerSkillNormalHit";
import { AudioMgr } from "../../../../../../scripts/framework/manager/AudioMgr";
import { AudioId } from "../../../../../../scripts/game/config/Config";
import { DataMgr } from "../../../../../../scripts/game/manager/DataMgr";

class FireBulletEvent extends TimelineEvent {
    constructor(
        public launcher: BulletLauncher,
    ) { super() }
    doEvent(timelineObj: TimelineObj, index: number): void {
        let target = timelineObj.targets[index]
        if (!timelineObj.targets[index]) return

        this.launcher.fireWorldPosition = FightCtrl.ins.skillStartPoint.convertToWorldSpaceAR(cc.Vec3.ZERO)
        this.launcher.caster = timelineObj.caster;

        this.playEffect(DataMgr.getInstance().skillCfg[Number(this.launcher.model.id) - 1].sound)
        let bulletCom = FightCtrl.ins.createBullet(this.launcher, target, `partner/buddy_5_vfx/buddy_5_vfx`, 'skill_bullet', true);
    }
}
/**熔岩迸发出火球，对随机1个目标造成攻击力{0}伤害，并造成灼烧伤害（每秒扣除最大生命值{1}），持续{2}秒 */
export class PlayerSkillFire5 extends Skill {
    constructor(
        public prefabKey: string,
    ) {
        super(SkillNames.playerSKill5);
        const launcher = new BulletLauncher();
        launcher.speed = 15
        launcher.model = new BulletModel("5", this.prefabKey);
        launcher.model.lifeDuration = 3
        launcher.model.tween = new BulletTweenForwardFireArrowPlayer();
        launcher.model.onHit = new MaxHpHit();
        this.skillMode = {
            name: this.name,
            effect: {
                id: UtilsExtends.generateUniqueId(),
                name: this.name,
                lifeDuration: 0.1,
                nodes: [
                    {
                        timeStartPoint: 0.01,
                        event: new FireBulletEvent(launcher)
                    } as TimelineNode,
                ]
            } as TimelineModel,
            buffs: [],
            coldTime: 0.5,
        }
    }
}
