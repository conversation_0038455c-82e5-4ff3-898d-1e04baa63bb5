import FightCtrl from "../../../ctrl/FightCtrl";
import { Damage, DamageInfo } from "../damage/DamageInfo";
import { BuffModel, BuffOnBeHurt, BuffOnCast, BuffOnHit, BuffOnOccur, BuffOnRemoved, BuffOnTick, CharacteBuffObj } from "../CharacteBuffObj";
import { CharacterControl } from "../CharacterControl";

/**
 *  目前用不到
 */


/**  收到伤害反击  */
class BuffOnBeHurtDefance extends BuffOnBeHurt {
    /**  反击伤害  */
    defanceAttack: number = 1000000;
    process(buff: CharacteBuffObj, damageInfo: DamageInfo, attacker: cc.Node): DamageInfo {
        console.log("DefanceBuffOnBeHurt, attacker", attacker.getComponent(CharacterControl).roleName)
        // 直接反击,造成伤害
        FightCtrl.ins.createDamage(buff.caster, attacker, new Damage(this.defanceAttack, 0, 0), []);
        return damageInfo;
    }
}

/**  收到伤害反击buff  */
export class BuffModelBeHurtFight implements BuffModel {
    name: string = "BuffModelBeHurtFight";
    lifeDuration: number = Number.MAX_VALUE;
    onOccur?: BuffOnOccur;
    onTick?: BuffOnTick;
    onRemoved?: BuffOnRemoved;
    onCast?: BuffOnCast;
    onHit?: BuffOnHit;
    onBeHurt: BuffOnBeHurt = new BuffOnBeHurtDefance();
}


// import BaseCtrl from "../../../scripts/framework/base/BaseCtrl";

// /**buff类型 */
// export enum EBuffType {
//     None = `None`,
// }

// declare global {
//     /**buff叠加类型 */
//     export interface IBuffTimeType {
//         type: EBuffType;
//         value: number;
//     }
//     /**buff数据接口 */
//     export interface IBuffDataType {
//         id
//         /**buff 的类名 */
//         type: string;
//         /**Buff等级 */
//         level: number;
//         /**buff生效时间类型 */
//         buffTime: IBuffTimeType;
//         /**是否在生效中 */
//         isTakeEffect: boolean;
//         /**是否可以叠加 */
//         isCanOverlay: boolean;
//         /**最大叠加几层 */
//         maxFloor: number;
//         /**当前叠加层数*/
//         nowFloor: number;
//     }
// }

// export default class BuffBase extends BaseCtrl {
//     /**技能数据 */
//     data: IBuffDataType;
//     /**BUFF作用的目标 */
//     target: any;
//     /**buff的Id */
//     id: string;
//     /**buff的类型 */
//     type: string;

//     /**使用buff ` 子类负责实现具体逻辑*/
//     useBuff() {}
//     /**移除Buff 子类负责实现具体逻辑*/
//     removeBuff() {}
//     /**叠加一层buff 子类负责实现具体逻辑*/
//     overlayBuff() {}
//     /**减少一层buff */
//     reduceBuff() {}
// }
