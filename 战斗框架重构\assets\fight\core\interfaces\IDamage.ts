/**
 * 伤害系统接口定义
 */

import { ICharacter } from "./ICharacter";
import { IBuff } from "./IBuff";

/**
 * 伤害信息接口
 */
export interface IDamageInfo {
    /** 伤害ID */
    readonly id: string;
    
    /** 攻击者 */
    readonly attacker: ICharacter | null;
    
    /** 受害者 */
    readonly target: ICharacter;
    
    /** 基础伤害 */
    baseDamage: number;
    
    /** 最终伤害 */
    readonly finalDamage: number;
    
    /** 伤害类型 */
    readonly damageType: DamageType;
    
    /** 伤害标签 */
    readonly tags: DamageTag[];
    
    /** 是否暴击 */
    isCritical: boolean;
    
    /** 暴击倍率 */
    criticalMultiplier: number;
    
    /** 伤害减免 */
    damageReduction: number;
    
    /** 伤害增幅 */
    damageAmplification: number;
    
    /** 附加的Buff */
    attachedBuffs: IBuff[];
    
    /** 伤害来源（技能、普攻等） */
    source?: string;
    
    /**
     * 计算最终伤害
     * @returns 最终伤害值
     */
    calculateFinalDamage(): number;
    
    /**
     * 添加伤害标签
     * @param tag 伤害标签
     */
    addTag(tag: DamageTag): void;
    
    /**
     * 检查是否包含标签
     * @param tag 伤害标签
     * @returns 是否包含
     */
    hasTag(tag: DamageTag): boolean;
    
    /**
     * 添加附加Buff
     * @param buff Buff
     */
    addAttachedBuff(buff: IBuff): void;
}

/**
 * 伤害类型枚举
 */
export enum DamageType {
    /** 物理伤害 */
    PHYSICAL = "physical",
    
    /** 魔法伤害 */
    MAGIC = "magic",
    
    /** 真实伤害 */
    TRUE = "true",
    
    /** 治疗 */
    HEAL = "heal"
}

/**
 * 伤害标签枚举
 */
export enum DamageTag {
    /** 直接伤害 */
    DIRECT = "direct",
    
    /** 持续伤害 */
    OVER_TIME = "over_time",
    
    /** 反射伤害 */
    REFLECT = "reflect",
    
    /** 溅射伤害 */
    SPLASH = "splash",
    
    /** 穿透伤害 */
    PENETRATING = "penetrating",
    
    /** 暴击伤害 */
    CRITICAL = "critical",
    
    /** 技能伤害 */
    SKILL = "skill",
    
    /** 普攻伤害 */
    BASIC_ATTACK = "basic_attack"
}

/**
 * 伤害计算器接口
 */
export interface IDamageCalculator {
    /**
     * 计算伤害
     * @param attacker 攻击者
     * @param target 目标
     * @param baseDamage 基础伤害
     * @param damageType 伤害类型
     * @param tags 伤害标签
     * @returns 伤害信息
     */
    calculateDamage(
        attacker: ICharacter | null,
        target: ICharacter,
        baseDamage: number,
        damageType: DamageType,
        tags?: DamageTag[]
    ): IDamageInfo;
    
    /**
     * 计算暴击
     * @param attacker 攻击者
     * @param target 目标
     * @returns 是否暴击和暴击倍率
     */
    calculateCritical(attacker: ICharacter, target: ICharacter): { isCritical: boolean; multiplier: number };
    
    /**
     * 计算伤害减免
     * @param target 目标
     * @param damageType 伤害类型
     * @param baseDamage 基础伤害
     * @returns 伤害减免值
     */
    calculateDamageReduction(target: ICharacter, damageType: DamageType, baseDamage: number): number;
}

/**
 * 伤害处理器接口
 */
export interface IDamageProcessor {
    /**
     * 处理伤害
     * @param damageInfo 伤害信息
     */
    processDamage(damageInfo: IDamageInfo): void;
    
    /**
     * 应用伤害前的处理
     * @param damageInfo 伤害信息
     * @returns 修改后的伤害信息
     */
    beforeDamage(damageInfo: IDamageInfo): IDamageInfo;
    
    /**
     * 应用伤害后的处理
     * @param damageInfo 伤害信息
     */
    afterDamage(damageInfo: IDamageInfo): void;
}

/**
 * 伤害事件接口
 */
export interface IDamageEvents {
    /** 伤害计算前事件 */
    onBeforeDamageCalculation?: (damageInfo: IDamageInfo) => IDamageInfo;
    
    /** 伤害应用前事件 */
    onBeforeDamageApplication?: (damageInfo: IDamageInfo) => IDamageInfo;
    
    /** 伤害应用后事件 */
    onAfterDamageApplication?: (damageInfo: IDamageInfo) => void;
    
    /** 暴击事件 */
    onCriticalHit?: (damageInfo: IDamageInfo) => void;
    
    /** 死亡事件 */
    onDeath?: (damageInfo: IDamageInfo) => void;
}

/**
 * 伤害管理器接口
 */
export interface IDamageManager {
    /** 伤害计算器 */
    readonly calculator: IDamageCalculator;
    
    /** 伤害处理器 */
    readonly processor: IDamageProcessor;
    
    /**
     * 造成伤害
     * @param attacker 攻击者
     * @param target 目标
     * @param baseDamage 基础伤害
     * @param damageType 伤害类型
     * @param tags 伤害标签
     * @param source 伤害来源
     */
    dealDamage(
        attacker: ICharacter | null,
        target: ICharacter,
        baseDamage: number,
        damageType: DamageType,
        tags?: DamageTag[],
        source?: string
    ): void;
    
    /**
     * 治疗
     * @param healer 治疗者
     * @param target 目标
     * @param healAmount 治疗量
     * @param source 治疗来源
     */
    heal(
        healer: ICharacter | null,
        target: ICharacter,
        healAmount: number,
        source?: string
    ): void;
    
    /**
     * 注册伤害事件监听器
     * @param events 事件监听器
     */
    registerEvents(events: IDamageEvents): void;
}
