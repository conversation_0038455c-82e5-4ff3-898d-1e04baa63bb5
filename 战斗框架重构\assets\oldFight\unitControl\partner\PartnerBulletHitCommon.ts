import FightCtrl from "../../../ctrl/FightCtrl";
import { CharacterControl } from "../CharacterControl";
import { Damage } from "../damage/DamageInfo";
import { BulletOnHit, BulletObj } from "../bullet/BulletObj";
import { UtilsExtends } from "../../../util/UtilsExtends";

/**
 * 直接生成一个伤害，没有其他效果
 */
export class PartnerBulletHitCommon extends BulletOnHit {
    process(bullet: BulletObj, target: cc.Node) {
        if (!bullet) return;
        // 触发伤害
        const attack = Math.ceil(bullet.caster.getComponent(CharacterControl).characterAttr.attack)
        FightCtrl.ins.createDamage(bullet.caster, target, new Damage(attack + 10, 0, 0), []);
        FightCtrl.ins.createHertLb(attack, 1, target)
        bullet.target.getComponent(CharacterControl).onHitEvent?.()
        FightCtrl.ins.shakeCamera()
        //播放子弹的动画
        // bullet.selfNode.getComponent(CharacterControl).spine?.playAsync('hit').then(() => {
        //     bullet.model.hitTimes -= 1;
        // })
        return true
    }
}