// import { <PERSON>etLauncher, BulletObj, BulletTween } from '../../bullet/BulletObj';

// /**
//  * 直线弹
//  * 效果： 沿着发射时候的方向  注意如果是 球体等不需要方向的不用设置这个
//  */
// export class BulletTweenForwardFireArrow extends BulletTween {
//     private v = cc.Vec3.RIGHT
//     process(t: number, obj: BulletObj, lunch: BulletLauncher): cc.Vec3 {
//         if (t === 0) {
//             this.v = lunch.fireNormalizedVector ?? cc.Vec3.RIGHT
//             if (obj.selfNode && obj.caster && obj.caster.parent) {
//                 obj.selfNode.angle = Math.atan2(lunch.fireNormalizedVector.y, lunch.fireNormalizedVector.x) * (180 / Math.PI)
//             }
//         }
//         if (obj.model.isCollision) {
//             this.v = cc.Vec3.ZERO
//         }
//         return this.v;
//     }
// }
