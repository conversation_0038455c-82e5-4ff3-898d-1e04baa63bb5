import FightCtrl from "../../../../ctrl/FightCtrl";
import { UtilsExtends } from "../../../../util/UtilsExtends";
import { Damage } from "../../damage/DamageInfo";
import { TimelineEvent, TimelineObj, TimelineNode, TimelineModel } from "../../../timeline/TimelineObj";
import { CharacterControl } from "../../CharacterControl";
import { SkillModel } from "../../CharacterSkillObj";
import { SkillNames } from "./SkillNames";


/** 普通攻击，计算伤害  */
class NormalAttackTimelineEvent extends TimelineEvent {
    doEvent(timelineObj: TimelineObj): void {
        // 触发伤害
        const attack = timelineObj.caster.getComponent(CharacterControl).characterAttr.attack
        console.log(`[timeline-event] normal attack ${timelineObj.caster?.getComponent(CharacterControl)?.roleName} => ${timelineObj.target?.getComponent(CharacterControl)?.roleName}`);
        FightCtrl.ins.createDamage(timelineObj.caster, timelineObj.target, new Damage(attack, 0, 0), []);
    }
}

/** 普通攻击(隔空攻击？), 间隔s, 没有子弹  */
export class SkillModelNormalAttack extends SkillModel {
    constructor() {
        super()
        this.name = SkillNames.playerAttack;
        this.effect = this.getTimelineModel()
        this.coldTime = 0.3;
    }

    getTimelineModel() {
        return {
            id: UtilsExtends.generateUniqueId(),
            name: this.name,
            lifeDuration: 1,
            nodes: [
                {
                    timeStartPoint: 0,
                    event: new NormalAttackTimelineEvent()
                } as TimelineNode
            ]
        } as TimelineModel;
    }
}