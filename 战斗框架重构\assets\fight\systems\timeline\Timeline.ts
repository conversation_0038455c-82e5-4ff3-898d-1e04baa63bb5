/**
 * Timeline系统实现
 * 基于时间轴的技能效果系统
 */

import { ITimeline, ITimelineNode, ITimelineEvent, TimelineEventType } from "../../core/interfaces/ITimeline";
import { ICharacter } from "../../core/interfaces/ICharacter";
import { EventManager } from "../../core/managers/EventManager";

/**
 * Timeline实现类
 */
export class Timeline implements ITimeline {
    private _id: string;
    private _name: string;
    private _duration: number;
    private _timeElapsed: number = 0;
    private _nodes: TimelineNode[] = [];
    private _caster: ICharacter;
    private _target?: ICharacter;
    private _targets?: ICharacter[];
    private _targetPosition?: cc.Vec3;
    private _isCompleted: boolean = false;
    private _isPaused: boolean = false;
    private _eventManager: EventManager;
    
    constructor(
        id: string,
        name: string,
        duration: number,
        caster: ICharacter,
        target?: ICharacter,
        targets?: ICharacter[],
        targetPosition?: cc.Vec3
    ) {
        this._id = id;
        this._name = name;
        this._duration = duration;
        this._caster = caster;
        this._target = target;
        this._targets = targets;
        this._targetPosition = targetPosition;
        this._eventManager = new EventManager();
    }
    
    // 实现ITimeline接口
    get id(): string { return this._id; }
    get name(): string { return this._name; }
    get duration(): number { return this._duration; }
    get timeElapsed(): number { return this._timeElapsed; }
    set timeElapsed(value: number) { this._timeElapsed = value; }
    get nodes(): ReadonlyArray<ITimelineNode> { return this._nodes; }
    get caster(): ICharacter { return this._caster; }
    get target(): ICharacter | undefined { return this._target; }
    get targets(): ICharacter[] | undefined { return this._targets; }
    get targetPosition(): cc.Vec3 | undefined { return this._targetPosition; }
    get isCompleted(): boolean { return this._isCompleted; }
    get isPaused(): boolean { return this._isPaused; }
    set isPaused(value: boolean) { this._isPaused = value; }
    
    /**
     * 添加Timeline节点
     */
    addNode(node: ITimelineNode): void {
        this._nodes.push(node as TimelineNode);
        // 按触发时间排序
        this._nodes.sort((a, b) => a.triggerTime - b.triggerTime);
    }
    
    /**
     * 更新Timeline
     */
    update(deltaTime: number): boolean {
        if (this._isPaused || this._isCompleted) {
            return this._isCompleted;
        }
        
        const previousTime = this._timeElapsed;
        this._timeElapsed += deltaTime;
        
        // 检查并触发节点事件
        for (const node of this._nodes) {
            if (node.shouldTrigger(this._timeElapsed, deltaTime)) {
                node.trigger(this, this._nodes.indexOf(node));
            }
        }
        
        // 检查是否完成
        if (this._timeElapsed >= this._duration) {
            this._isCompleted = true;
            this._eventManager.emit("completed", { timeline: this });
        }
        
        return this._isCompleted;
    }
    
    /**
     * 暂停Timeline
     */
    pause(): void {
        this._isPaused = true;
        this._eventManager.emit("paused", { timeline: this });
    }
    
    /**
     * 恢复Timeline
     */
    resume(): void {
        this._isPaused = false;
        this._eventManager.emit("resumed", { timeline: this });
    }
    
    /**
     * 停止Timeline
     */
    stop(): void {
        this._isCompleted = true;
        this._eventManager.emit("stopped", { timeline: this });
    }
    
    /**
     * 重置Timeline
     */
    reset(): void {
        this._timeElapsed = 0;
        this._isCompleted = false;
        this._isPaused = false;
        
        // 重置所有节点
        for (const node of this._nodes) {
            node.reset();
        }
        
        this._eventManager.emit("reset", { timeline: this });
    }
    
    /**
     * 跳转到指定时间点
     */
    seekTo(time: number): void {
        const oldTime = this._timeElapsed;
        this._timeElapsed = Math.max(0, Math.min(time, this._duration));
        
        // 如果是向前跳转，需要触发中间的事件
        if (time > oldTime) {
            for (const node of this._nodes) {
                if (node.triggerTime > oldTime && node.triggerTime <= time) {
                    if (!node.isTriggered || node.repeatable) {
                        node.trigger(this, this._nodes.indexOf(node));
                    }
                }
            }
        }
        
        this._eventManager.emit("seeked", { timeline: this, oldTime, newTime: time });
    }
    
    /**
     * 设置事件监听器
     */
    on(event: string, callback: Function): void {
        this._eventManager.on(event, callback);
    }
    
    /**
     * 移除事件监听器
     */
    off(event: string, callback: Function): void {
        this._eventManager.off(event, callback);
    }
    
    /**
     * 清理资源
     */
    cleanup(): void {
        this._eventManager.cleanup();
        this._nodes.length = 0;
    }
}

/**
 * Timeline节点实现类
 */
export class TimelineNode implements ITimelineNode {
    private _id: string;
    private _triggerTime: number;
    private _event: ITimelineEvent;
    private _isTriggered: boolean = false;
    private _repeatable: boolean;
    private _repeatInterval?: number;
    private _maxRepeats?: number;
    private _currentRepeats: number = 0;
    private _lastTriggerTime: number = -1;
    
    constructor(
        id: string,
        triggerTime: number,
        event: ITimelineEvent,
        repeatable: boolean = false,
        repeatInterval?: number,
        maxRepeats?: number
    ) {
        this._id = id;
        this._triggerTime = triggerTime;
        this._event = event;
        this._repeatable = repeatable;
        this._repeatInterval = repeatInterval;
        this._maxRepeats = maxRepeats;
    }
    
    // 实现ITimelineNode接口
    get id(): string { return this._id; }
    get triggerTime(): number { return this._triggerTime; }
    get event(): ITimelineEvent { return this._event; }
    get isTriggered(): boolean { return this._isTriggered; }
    set isTriggered(value: boolean) { this._isTriggered = value; }
    get repeatable(): boolean { return this._repeatable; }
    get repeatInterval(): number | undefined { return this._repeatInterval; }
    get maxRepeats(): number | undefined { return this._maxRepeats; }
    get currentRepeats(): number { return this._currentRepeats; }
    set currentRepeats(value: number) { this._currentRepeats = value; }
    
    /**
     * 检查是否应该触发
     */
    shouldTrigger(currentTime: number, deltaTime: number): boolean {
        // 首次触发检查
        if (!this._isTriggered && currentTime >= this._triggerTime) {
            return true;
        }
        
        // 重复触发检查
        if (this._repeatable && this._isTriggered && this._repeatInterval) {
            // 检查是否达到最大重复次数
            if (this._maxRepeats !== undefined && this._currentRepeats >= this._maxRepeats) {
                return false;
            }
            
            // 检查是否到了下一次触发时间
            const nextTriggerTime = this._lastTriggerTime + this._repeatInterval;
            if (currentTime >= nextTriggerTime) {
                return true;
            }
        }
        
        return false;
    }
    
    /**
     * 触发节点事件
     */
    trigger(timeline: ITimeline, nodeIndex: number): void {
        try {
            this._event.execute(timeline, nodeIndex);
            
            if (!this._isTriggered) {
                this._isTriggered = true;
            }
            
            if (this._repeatable) {
                this._currentRepeats++;
                this._lastTriggerTime = timeline.timeElapsed;
            }
        } catch (error) {
            console.error(`Error executing timeline event ${this._event.id}:`, error);
        }
    }
    
    /**
     * 重置节点状态
     */
    reset(): void {
        this._isTriggered = false;
        this._currentRepeats = 0;
        this._lastTriggerTime = -1;
    }
}

/**
 * Timeline事件基类
 */
export abstract class TimelineEvent implements ITimelineEvent {
    protected _id: string;
    protected _type: TimelineEventType;
    
    constructor(id: string, type: TimelineEventType) {
        this._id = id;
        this._type = type;
    }
    
    get id(): string { return this._id; }
    get type(): TimelineEventType { return this._type; }
    
    /**
     * 执行事件（抽象方法，子类实现）
     */
    abstract execute(timeline: ITimeline, nodeIndex: number, context?: any): void;
    
    /**
     * 播放音效
     */
    playSound(soundId: string): void {
        // 这里应该调用音效管理器
        // AudioManager.getInstance().playEffect(soundId);
        console.log(`Playing sound: ${soundId}`);
    }
    
    /**
     * 播放特效
     */
    playEffect(effectId: string, position?: cc.Vec3): void {
        // 这里应该调用特效管理器
        // EffectManager.getInstance().playEffect(effectId, position);
        console.log(`Playing effect: ${effectId} at position:`, position);
    }
    
    /**
     * 获取目标列表
     */
    protected getTargets(timeline: ITimeline, nodeIndex?: number): ICharacter[] {
        const targets: ICharacter[] = [];
        
        if (timeline.target) {
            targets.push(timeline.target);
        }
        
        if (timeline.targets) {
            targets.push(...timeline.targets);
        }
        
        return targets.filter(target => target && !target.isDead);
    }
    
    /**
     * 获取有效的目标（排除已死亡的）
     */
    protected getValidTarget(timeline: ITimeline, nodeIndex?: number): ICharacter | null {
        // 如果指定了节点索引，尝试获取对应的目标
        if (nodeIndex !== undefined && timeline.targets && timeline.targets[nodeIndex]) {
            const target = timeline.targets[nodeIndex];
            return target && !target.isDead ? target : null;
        }
        
        // 否则返回单个目标
        if (timeline.target && !timeline.target.isDead) {
            return timeline.target;
        }
        
        // 或者返回第一个有效的多目标
        if (timeline.targets) {
            for (const target of timeline.targets) {
                if (target && !target.isDead) {
                    return target;
                }
            }
        }
        
        return null;
    }
}
