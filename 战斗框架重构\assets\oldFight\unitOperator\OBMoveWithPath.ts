

import { CharacterControl } from "../unitControl/CharacterControl";
import { OperatorBehavior } from "./BaseOperator";

/**
 * 移动
 */
export class OBMoveWithPath extends OperatorBehavior {
    /**是否重复 */
    public repeat: boolean = true;
    curWordPos: cc.Vec3;
    endWordPos: cc.Vec3;
    _lineVelocity = new cc.Vec2(0, 0);
    isClear = true
    onCleraEvent: () => void
    onFinishMoveEvent: () => void

    constructor() {
        super();
    }
    onClear(roleControl: CharacterControl): void {
        if (this.isClear) {
            this.onCleraEvent?.()
            this._lineVelocity = cc.v2(0, 0)
            this.curWordPos = null
            this.endWordPos = null
        }
    }
    addWorldPath(pathList: cc.Vec3[]) {
        this.enable = true
        this.curWordPos = pathList[0];
        this.endWordPos = pathList[1];
    }
    override tickOperator(roleControl: CharacterControl, dt: number): void {
        if (roleControl.isDead) {
            //如果角色已经死亡了就停止移动        
            this.onFinishMoveEvent?.()
            return
        }
        if (this.endWordPos == null) return
        let moveToEnd = false;
        let dis = roleControl.node.convertToWorldSpaceAR(cc.Vec3.ZERO).subtract(this.endWordPos).len()
        if (dis <= 20 || dis > cc.view.getVisibleSize().height + 200) {
            // 已经到达
            moveToEnd = true
            this.endWordPos = null
        } else {
            // 未到达，继续调整方向
            const dir = this.endWordPos.clone().subtract(this.curWordPos).normalize();
            this._lineVelocity.x = dir.x * roleControl.characterAttr.moveSpeed
            this._lineVelocity.y = dir.y * roleControl.characterAttr.moveSpeed
            roleControl.moveWithDir(dir)
        }
        if (moveToEnd) {
            // 停止移动           
            this.onFinishMoveEvent?.()
        }
    }
}
