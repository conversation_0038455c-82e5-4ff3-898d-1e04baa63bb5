import FightCtrl from "../../../../ctrl/FightCtrl";
import { UtilsExtends } from "../../../../util/UtilsExtends";
import { TimelineEvent, TimelineObj, TimelineNode, TimelineModel } from "../../../timeline/TimelineObj";
import { BulletLauncher, BulletModel } from "../../bullet/BulletObj";
import { Skill, SkillModel } from "../../CharacterSkillObj";
import { CharacterControl } from "../../CharacterControl";
import { PlayerBulletHitCommon } from "../bulletOnHit/PlayerBulletHitCommon";
import { SkillNames } from "./SkillNames";
import { PlayerSkill1Hit } from "../bulletOnHit/PlayerSkillNormalHit";
import { AudioMgr } from "../../../../../../scripts/framework/manager/AudioMgr";
import { AudioId } from "../../../../../../scripts/game/config/Config";
import { DataMgr } from "../../../../../../scripts/game/manager/DataMgr";

class FireBulletEvent extends TimelineEvent {
    constructor(
        public launcher: BulletLauncher,
    ) { super() }
    doEvent(timelineObj: TimelineObj, index: number): void {
        let target = timelineObj.targets[index]
        if (!timelineObj.targets[index]) return

        this.launcher.fireWorldPosition = target.convertToWorldSpaceAR(cc.Vec3.ZERO)
        this.launcher.caster = timelineObj.caster;

        this.playEffect(DataMgr.getInstance().skillCfg[Number(this.launcher.model.id) - 1].sound)
        let bulletCom = FightCtrl.ins.createBullet(this.launcher, target, `partner/buddy_13_vfx/buddy_13_vfx`, 'skill_hit', false, (trackEntry: sp.spine.TrackEntry, event: sp.spine.Event) => {
            if (event.data.name == 'attack') {
                bulletCom.playHit()
                bulletCom.isCanHert = false
            }
        });
        bulletCom.checkDistance = -1
    }
}
/**电光精准劈向敌人，对随机1个目标造成攻击力{0}伤害*/
export class PlayerSkillFire13 extends Skill {
    constructor(
        public prefabKey: string,
    ) {
        super(SkillNames.playerSKill13);
        const launcher = new BulletLauncher();
        launcher.speed = 0
        launcher.model = new BulletModel("13", this.prefabKey);
        launcher.model.lifeDuration = 5;
        launcher.model.onHit = new PlayerSkill1Hit();
        this.skillMode = {
            name: this.name,
            effect: {
                id: UtilsExtends.generateUniqueId(),
                name: this.name,
                lifeDuration: 0.1,
                nodes: [
                    {
                        timeStartPoint: 0.01,
                        event: new FireBulletEvent(launcher)
                    } as TimelineNode,
                ]
            } as TimelineModel,
            buffs: [],
            coldTime: 0.5,
        }
    }
}
