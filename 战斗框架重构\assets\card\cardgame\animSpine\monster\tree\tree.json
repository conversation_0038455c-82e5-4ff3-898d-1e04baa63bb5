{"skeleton": {"hash": "66jTcbOC0mI", "spine": "3.8-from-4.0-from-4.1.24", "x": -144.88, "y": -31.89, "width": 267.9, "height": 315.26, "images": "./images/", "audio": "H:/fan/tree"}, "bones": [{"name": "root", "x": -4.43, "y": -19.2}, {"name": "bone", "parent": "root", "length": 137.41, "rotation": 86.54, "x": 15.67, "y": 128.63}, {"name": "bone8", "parent": "root", "x": 10.98, "y": 118.1}, {"name": "bone2", "parent": "bone8", "length": 48.1, "rotation": -98.6, "x": -50.61, "y": -4.41}, {"name": "bone3", "parent": "bone2", "length": 37.32, "rotation": 1.79, "x": 48.1}, {"name": "bone4", "parent": "bone", "x": 89.09, "y": 75.2}, {"name": "bone5", "parent": "bone", "x": 22.96, "y": 60.12}, {"name": "bone6", "parent": "bone8", "length": 47.2, "rotation": -90, "x": 28.05, "y": -5.89}, {"name": "bone7", "parent": "bone6", "length": 37.65, "rotation": 0.81, "x": 47.2}, {"name": "bone9", "parent": "root", "x": -18.53, "y": 22.75}], "slots": [{"name": "shadow", "bone": "bone9", "attachment": "shadow"}, {"name": "feet", "bone": "root", "attachment": "feet"}, {"name": "feet2", "bone": "root", "attachment": "feet"}, {"name": "body", "bone": "bone", "attachment": "body"}, {"name": "eye", "bone": "bone4", "attachment": "eye"}, {"name": "eye-attack", "bone": "bone4"}, {"name": "mouse", "bone": "bone5", "attachment": "mouse"}, {"name": "mouse-attack", "bone": "bone5"}, {"name": "eye-hurt", "bone": "bone4"}], "skins": [{"name": "default", "attachments": {"body": {"body": {"x": 68.31, "y": 4.35, "rotation": -86.54, "width": 224, "height": 211}}, "eye": {"eye": {"x": 0.71, "y": -28.47, "rotation": -86.54, "width": 104, "height": 117}}, "eye-attack": {"eye-attack": {"x": 11.67, "y": -35.82, "rotation": -86.54, "width": 124, "height": 60}}, "eye-hurt": {"eye-hurt": {"x": 19.08, "y": -25.86, "rotation": -86.54, "width": 117, "height": 34}}, "feet": {"feet": {"type": "mesh", "uvs": [0.82997, 0, 0.98093, 0.10977, 0.94279, 0.49084, 0.94035, 0.52058, 0.93825, 0.54603, 0.9351, 0.5843, 0.93189, 0.62334, 0.90829, 0.99223, 0, 0.99165, 0, 0.81455, 0.16907, 0.67718, 0.38788, 0.62726, 0.40391, 0.55928, 0.40645, 0.52633, 0.41044, 0.48937, 0.47618, 0.0538, 0.64719, 0], "triangles": [5, 12, 4, 6, 12, 5, 11, 12, 6, 8, 9, 10, 7, 11, 6, 8, 10, 11, 7, 8, 11, 16, 0, 1, 15, 16, 1, 14, 15, 1, 2, 14, 1, 3, 14, 2, 13, 14, 3, 4, 13, 3, 12, 13, 4], "vertices": [1, 3, -16.3, 1.59, 1, 1, 3, -5.42, 14.08, 1, 2, 3, 38.31, 17.95, 0.88493, 4, -9.23, 18.25, 0.11507, 2, 3, 41.72, 18.29, 0.77691, 4, -5.81, 18.48, 0.22309, 2, 3, 44.63, 18.58, 0.65455, 4, -2.89, 18.68, 0.34545, 2, 3, 49.02, 19.02, 0.44315, 4, 1.51, 18.98, 0.55685, 2, 3, 53.49, 19.46, 0.25276, 4, 5.99, 19.28, 0.74724, 1, 4, 48.32, 22.65, 1, 1, 4, 55.9, -41.39, 1, 1, 4, 35.67, -43.81, 1, 2, 3, 67.71, -33.16, 6e-05, 4, 18.56, -33.76, 0.99994, 2, 3, 59.71, -18.66, 0.09253, 4, 11.02, -19.02, 0.90747, 2, 3, 51.81, -18.7, 0.40107, 4, 3.12, -18.81, 0.59893, 2, 3, 48.04, -19.09, 0.57846, 4, -0.66, -19.08, 0.42154, 2, 3, 43.79, -19.45, 0.75236, 4, -4.91, -19.3, 0.24764, 1, 3, -6.43, -22.32, 1, 1, 3, -14.36, -11.24, 1], "hull": 17, "edges": [0, 32, 0, 2, 14, 16, 16, 18, 30, 32, 18, 20, 20, 22, 28, 30, 22, 24, 24, 26, 26, 28, 6, 8, 8, 10, 2, 4, 4, 6, 10, 12, 12, 14], "width": 71, "height": 115}}, "feet2": {"feet": {"type": "mesh", "uvs": [0.86295, 0, 0.97929, 0.14638, 0.95134, 0.47169, 0.94792, 0.51154, 0.94559, 0.53866, 0.94276, 0.57154, 0.93971, 0.60711, 0.93733, 0.63478, 0.90661, 0.99237, 0, 0.99134, 0, 0.80845, 0.4134, 0.55345, 0.41421, 0.52391, 0.42284, 0.50407, 0.47907, 0.04257, 0.68886, 0], "triangles": [6, 11, 5, 7, 11, 6, 8, 9, 10, 8, 11, 7, 8, 10, 11, 1, 15, 0, 1, 2, 15, 2, 14, 15, 2, 13, 14, 3, 13, 2, 4, 13, 3, 5, 13, 4, 13, 11, 12, 13, 5, 11], "vertices": [1, 7, -16.3, 8.72, 1, 1, 7, -0.36, 18.6, 1, 2, 7, 37.06, 20.31, 0.90168, 8, -9.85, 20.45, 0.09832, 2, 7, 41.65, 20.52, 0.783, 8, -5.26, 20.6, 0.217, 2, 7, 44.77, 20.67, 0.67257, 8, -2.14, 20.7, 0.32743, 2, 7, 48.55, 20.84, 0.51835, 8, 1.64, 20.82, 0.48165, 2, 7, 52.64, 21.03, 0.35587, 8, 5.74, 20.95, 0.64413, 2, 7, 55.82, 21.17, 0.24371, 8, 8.92, 21.05, 0.75629, 1, 8, 50.08, 22.35, 1, 1, 8, 55.4, -41.8, 1, 1, 8, 34.45, -43.58, 1, 2, 7, 50.18, -16.77, 0.35838, 8, 2.75, -16.81, 0.64162, 2, 7, 46.79, -17.05, 0.55986, 8, -0.64, -17.04, 0.44014, 2, 7, 44.46, -16.66, 0.68192, 8, -2.97, -16.62, 0.31808, 1, 7, -8.74, -17.92, 1, 1, 7, -15.08, -3.58, 1], "hull": 16, "edges": [0, 30, 0, 2, 16, 18, 18, 20, 20, 22, 28, 30, 22, 24, 24, 26, 26, 28, 14, 16, 2, 4, 4, 6, 10, 12, 12, 14, 6, 8, 8, 10], "width": 71, "height": 115}}, "mouse": {"mouse": {"x": -4, "y": -18.18, "rotation": -86.54, "width": 89, "height": 65}}, "mouse-attack": {"mouse-attack": {"x": 5.64, "y": -12.08, "rotation": -86.54, "width": 100, "height": 93}}, "shadow": {"shadow": {"x": 6.08, "y": -6.94, "width": 256, "height": 57}}}}], "events": {"attack": {}}, "animations": {"attack": {"slots": {"eye": {"attachment": [{"name": null}]}, "eye-attack": {"attachment": [{"name": "eye-attack"}]}, "mouse": {"attachment": [{"name": null}]}, "mouse-attack": {"attachment": [{"name": "mouse-attack"}]}}, "bones": {"bone": {"rotate": [{}, {"time": 0.1333, "angle": 11.06}, {"time": 0.2333, "angle": -11.84}, {"time": 0.4, "angle": -16.64}, {"time": 0.5, "angle": 37.06}, {"time": 0.6, "angle": 34.79}, {"time": 0.7667, "angle": 24.92}, {"time": 1}], "translate": [{}, {"time": 0.1333, "x": -16.86, "y": -38.41}, {"time": 0.2333, "x": -15.79, "y": 83.85, "curve": "stepped"}, {"time": 0.4, "x": -15.79, "y": 83.85}, {"time": 0.6, "x": -3.33, "y": -2.24}, {"time": 0.7667, "x": 10.96, "y": -5.6}, {"time": 1}], "scale": [{}, {"time": 0.1333, "x": 0.887}, {"time": 0.2333, "x": 1.048, "y": 0.943, "curve": "stepped"}, {"time": 0.4, "x": 1.048, "y": 0.943}, {"time": 0.5, "x": 0.901, "y": 1.142, "curve": "stepped"}, {"time": 0.6, "x": 0.901, "y": 1.142}, {"time": 0.7667}]}, "bone2": {"rotate": [{}, {"time": 0.1333, "angle": -31.53}, {"time": 0.2333, "angle": 12.97, "curve": "stepped"}, {"time": 0.4, "angle": 12.97}, {"time": 0.5, "angle": 5.35}, {"time": 0.6, "angle": -23.63, "curve": "stepped"}, {"time": 0.7667, "angle": -23.63}, {"time": 1}], "translate": [{}, {"time": 0.1333, "x": -4.86, "y": -11.98}, {"time": 0.2333, "x": -14.93, "y": -13.42}, {"time": 0.4, "x": -16.11, "y": -7.48}, {"time": 0.5, "x": -7.28, "y": -52.29}, {"time": 0.6, "x": 16.79, "y": -9.69, "curve": "stepped"}, {"time": 0.7667, "x": 16.79, "y": -9.69}, {"time": 1}]}, "bone3": {"rotate": [{}, {"time": 0.1333, "angle": 31.99}, {"time": 0.2333, "angle": 17.29, "curve": "stepped"}, {"time": 0.4, "angle": 17.29}, {"time": 0.5, "angle": 1.37}, {"time": 0.6, "angle": 26.95, "curve": "stepped"}, {"time": 0.7667, "angle": 26.95}, {"time": 1}]}, "bone4": {"rotate": [{}, {"time": 0.5, "angle": 0.13, "curve": "stepped"}, {"time": 0.7667, "angle": 0.13}, {"time": 1}], "translate": [{}, {"time": 0.1333, "x": -6.22, "y": 3.89}, {"time": 0.4, "x": 1.87, "y": 3.16}, {"time": 1}], "scale": [{}, {"time": 0.1333, "x": 0.723}, {"time": 0.2333, "x": 1.058, "y": 0.955, "curve": "stepped"}, {"time": 0.4, "x": 1.058, "y": 0.955}, {"time": 0.5}]}, "bone5": {"translate": [{}, {"time": 0.1333, "x": -13.96, "y": 5.54}, {"time": 0.2333, "x": -12.43, "y": 10.66}, {"time": 0.4, "x": -7.15, "y": 7.45}, {"time": 1}], "scale": [{}, {"time": 0.1333, "x": 0.701}, {"time": 0.2333, "x": 1.057, "y": 0.902, "curve": "stepped"}, {"time": 0.4, "x": 1.057, "y": 0.902}, {"time": 0.5}]}, "bone6": {"rotate": [{}, {"time": 0.1333, "angle": -18.14}, {"time": 0.2333, "angle": 7.91, "curve": "stepped"}, {"time": 0.4, "angle": 7.91}, {"time": 0.5, "angle": 3.43}, {"time": 0.6, "angle": -4.89, "curve": "stepped"}, {"time": 0.7667, "angle": -4.89}, {"time": 1}], "translate": [{}, {"time": 0.1333, "x": -0.33, "y": -3.11}, {"time": 0.2333, "x": -16.62, "y": -22.77}, {"time": 0.4, "x": -17.81, "y": -16.83}, {"time": 0.5, "x": -0.14, "y": 11.57}, {"time": 0.6, "x": 12.28, "y": 3.19, "curve": "stepped"}, {"time": 0.7667, "x": 12.28, "y": 3.19}, {"time": 1}]}, "bone7": {"rotate": [{}, {"time": 0.1333, "angle": 12.8}, {"time": 0.2333, "angle": 22.89, "curve": "stepped"}, {"time": 0.4, "angle": 22.89}, {"time": 0.5, "angle": 15.15}, {"time": 0.6, "angle": 3.81, "curve": "stepped"}, {"time": 0.7667, "angle": 3.81}, {"time": 1}]}, "bone8": {"rotate": [{"time": 0.6, "angle": -2.27}], "translate": [{"time": 0.1333}, {"time": 0.2333, "x": -3.44, "y": 96.71, "curve": "stepped"}, {"time": 0.4, "x": -3.44, "y": 96.71}, {"time": 0.6, "x": -0.48, "y": 0.73, "curve": "stepped"}, {"time": 0.7667, "x": -0.48, "y": 0.73}, {"time": 1}]}, "bone9": {"translate": [{}, {"time": 0.1333, "x": -22.88}, {"time": 0.4}], "scale": [{}, {"time": 0.1333, "x": 0.834, "curve": "stepped"}, {"time": 0.4, "x": 0.834}, {"time": 0.6}]}}, "events": [{"time": 0.5, "name": "attack"}]}, "hurt": {"slots": {"eye": {"attachment": [{"name": null}]}, "eye-hurt": {"attachment": [{"name": "eye-hurt"}]}, "mouse": {"attachment": [{"name": null}]}, "mouse-attack": {"attachment": [{"name": "mouse-attack"}]}}, "bones": {"bone": {"rotate": [{}, {"time": 0.1667, "angle": -21.5}, {"time": 0.4, "angle": 14.71}, {"time": 0.6333}, {"time": 0.7667, "angle": -4.38}, {"time": 1}], "translate": [{}, {"time": 0.1667, "x": 0.73, "y": 2.19}, {"time": 0.4}], "scale": [{}, {"time": 0.1667, "x": 1.079, "y": 0.951}, {"time": 0.4, "x": 0.958, "y": 1.066}, {"time": 0.6333}]}, "bone2": {"rotate": [{}, {"time": 0.1667, "angle": -33.96}, {"time": 0.4, "angle": -19.46}, {"time": 0.6333}, {"time": 0.7667, "angle": -2.56}, {"time": 1}], "translate": [{}, {"time": 0.1667, "x": -11.7, "y": 32.92}, {"time": 0.4, "x": -1.46, "y": -6.58}, {"time": 0.6333}]}, "bone3": {"rotate": [{}, {"time": 0.1667, "angle": 20.21}, {"time": 0.4, "angle": 19.7}, {"time": 0.6333}]}, "bone6": {"rotate": [{}, {"time": 0.1667, "angle": -16.37}, {"time": 0.4, "angle": -7.68}, {"time": 0.6333}, {"time": 0.7667, "angle": -4.99}, {"time": 1}], "translate": [{}, {"time": 0.1667, "x": 3.66}, {"time": 0.4, "x": 5.12, "y": 0.73}, {"time": 0.6333}]}, "bone7": {"rotate": [{}, {"time": 0.1667, "angle": 9.58}, {"time": 0.4, "angle": 2.38}, {"time": 0.6333}, {"time": 0.7667, "angle": 1.64}, {"time": 1}]}}}, "idle": {"bones": {"bone": {"rotate": [{}, {"time": 0.5, "angle": -4.37}, {"time": 1}], "translate": [{}, {"time": 0.5, "x": 2.3, "y": 3.83}, {"time": 1}], "scale": [{}, {"time": 0.5, "x": 1.05, "y": 0.969}, {"time": 1}]}, "bone2": {"rotate": [{}, {"time": 0.5, "angle": -3.25}, {"time": 1}]}, "bone3": {"rotate": [{}, {"time": 0.5, "angle": 4.89}, {"time": 1}]}, "bone4": {"translate": [{}, {"time": 0.5, "x": 6.14, "y": -0.58}, {"time": 1}], "scale": [{}, {"time": 0.5, "x": 1.027, "y": 0.97}, {"time": 1}]}, "bone5": {"translate": [{}, {"time": 0.5, "x": -0.33, "y": 2.59}, {"time": 1}], "scale": [{}, {"time": 0.5, "x": 1.053, "y": 0.981}, {"time": 1}]}, "bone6": {"rotate": [{}, {"time": 0.5, "angle": -5.91}, {"time": 1}]}, "bone7": {"rotate": [{}, {"time": 0.5, "angle": 8.51}, {"time": 1}]}}}}}