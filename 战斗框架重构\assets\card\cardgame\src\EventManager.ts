
// import { Broadcast } from "@judu233/cc-broadcast";

// /**消息名字 */
// interface ITestKey extends broadcast.IMsgKey {
//     /**流程部分 */
//     /**战斗开始 */
//     FightStart: `FightStart`,
//     /**战斗结束 */
//     FightOver: `FightOver`,
//     /**回合开始 */
//     RoundStart: `RoundStart`,
//     /**回合结束 */
//     RoundOver: `RoundOver`,
    
//     /**使用卡牌 */
//     UseCard: `UseCard`,
//     /**丢弃卡牌 */
//     DiscardCard: `DiscardCard`,
//     /**显示使用卡片 */
//     ShowUseCard: `ShowUseCard`,
//     /**显示丢弃卡片 */
//     ShowDiscardCard: `ShowDiscardCard`,
//     /**取消显示所有 */
//     CancelShowAll: `CancelShowAll`,
//     /**污染卡片 */
//     PolluteCard: `PolluteCard`,

//     /**战斗控制部分 */
//     /**触发玩家攻击角色 */
//     PlayerAttack: `PlayerAttack`,
//     /**触发玩家技能 */
//     PlayerSkill: `PlayerSkill`,

//     /**属性监听 */
//     /**刷新UI界面 */
//     RefreshUI: `RefreshUI`,
//     /**触发玩家卡牌血量增减 */
//     PlayerHpChange: `PlayerHpChange`,
//     /**玩家卡牌死亡 */
//     PlayerCardDeath: `PlayerCardDeath`,
//     /**玩家卡牌复活 */
//     PlayerCardResurrection: `PlayerCardResurrection`,

//     /**触发电脑敌人卡牌血量增减 */
//     ComputerHpChange: `ComputerHpChange`,
//     /**敌方卡牌死亡 */
//     ComputerCardDeath: `ComputerCardDeath`,
//     /**敌方卡牌复活 */
//     ComputerCardResurrection: `ComputerCardResurrection`,

//     /**技能·buff */
// }
// /**消息广播传参值*/
// interface ITestValueType extends broadcast.IMsgValueType {
//     /**流程部分 */
//     /**战斗开始 */
//     FightStart: any,
//     /**战斗结束 */
//     FightOver: any,
//     /**回合开始 */
//     RoundStart: any,
//     /**回合结束 */
//     RoundOver: any,

//     /**战斗控制部分 */
//     /**触发玩家战斗 */
//     PlayerAttack: any,
//     /**触发玩家技能 */
//     PlayerSkill: any,

//     /**属性监听 */
//     /**触发玩家卡牌血量增减 */
//     PlayerHpChange: any,
//     /**玩家卡牌死亡 */
//     PlayerCardDeath: any,
//     /**玩家卡牌复活 */
//     PlayerCardResurrection: any,

//     /**触发电脑敌人卡牌血量增减 */
//     ComputerHpChange: any,
//     /**敌方卡牌死亡 */
//     ComputerCardDeath: any,
//     /**敌方卡牌复活 */
//     ComputerCardResurrection: any,

//     /**技能·buff */
// }
// /**消息回传传参值 */
// interface ITestResultType extends broadcast.IMsgValueType {
// }

// /**事件管理 */
// export const BRO = new Broadcast<ITestKey, ITestValueType, ITestResultType>();