import { TimelineModel, TimelineObj } from './TimelineObj';
const { ccclass, property } = cc._decorator;

/**
 * 处理所有Timeline信息
 * 根据时间去执行一系列动作
 */
export class TimelineManager extends cc.Component {
    private timelines: Array<TimelineObj> = new Array<TimelineObj>();
    protected update(dt: number): void {
        if (this.timelines.length <= 0) return;
        let idx = 0;
        while (idx < this.timelines.length) {
            const timeline = this.timelines[idx];
            const wasTimeElapsed = timeline.timeElapsed;
            timeline.timeElapsed += dt;
            //执行时间点内的事情
            for (let i = 0; i < timeline.model?.nodes?.length; i++) {
                const timelineNode = timeline.model.nodes[i];
                // < timeline.timeElapsed是为了避免重复执行
                if (
                    timelineNode.timeStartPoint < timeline.timeElapsed &&
                    timelineNode.timeStartPoint >= wasTimeElapsed
                ) {
                    timelineNode?.event?.doEvent(timeline, i);
                }
            }
            //判断timeline是否终结
            if (timeline.model.lifeDuration <= timeline.timeElapsed) {
                // 执行完成进行移除
                this.timelines.splice(idx, 1);
            } else {
                idx++;
            }
        }
    }
    addTimelineWithModel(timelineModel: TimelineModel, caster: cc.Node) {
        const item = new TimelineObj(timelineModel, caster);
        this.addTimeline(item);
    }
    addTimeline(timeline: TimelineObj) {
        this.timelines.push(timeline);
    }
}

