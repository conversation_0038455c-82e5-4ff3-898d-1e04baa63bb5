// import { ECamp } from "../CampManager";
// import { BRO } from "../EventManager";
// import FightManager from "../FightManager";
// import SkillManager from "../SkillManager";
// import Base from "./Base";
// import CampBase from "./CampBase";
// import RoleBase from "./RoleBase";


// const { ccclass, property } = cc._decorator;

// export enum ECardType {
//     /**装备卡牌 */
//     equipment = 'equipment',
//     /**攻击卡牌 */
//     attact = 'attact',
//     /**血量操作相关的卡牌 */
//     hp = 'hp',
//     /**垃圾牌 */
//     garbage = 'garbage',
//     /**污染卡 */
//     pollution = 'pollution',
//     /**礼物卡 */
//     gift = 'gift',
//     /**修补装备 */
//     repair = 'repair',
// }

// /**卡片的状态 */
// enum ECardStatus {
//     /**卡牌未使用 */
//     None = `None`,
//     /**卡牌已使用 */
//     Used = `Used`,
//     /**卡牌已死亡 */
//     Death = `Death`,
//     /**卡牌已冻结 */
//     Frozen = `Frozen`,
// }
// export enum EHpType {
//     /**固定恢复 */
//     fixedRecovery = 'fixedRecovery',
//     /**百分比恢复 */
//     percentRecovery = 'percentRecovery',
//     /**扣除固定 */
//     fixedDeduct = 'fixedDeduct',
//     /**扣除百分比 */
//     percentDeduct = 'percentDeduct',
//     /**破碎的血瓶，使用后恢复剩余的血量 */
//     breakRecovery = 'breakRecovery',
// }

// export enum EPollutionType {
//     /**污染其他卡牌 */
//     pollutionOtherCard = 'pollutionOtherCard',
//     /**自爆卡 */
//     selfBomb = 'selfBomb',
// }

// export enum EGiftType {
//     /**装备礼物卡 */
//     equipmentGift = 'equipmentGift',
//     /**卷轴礼物卡 */
//     scrollGift = 'scrollGift',
// }


// declare global {
//     type ICardBaseName = 'CardBase'
//     /**卡牌或角色的基础数据 */
//     export interface ICardDataType extends IBaseDataType {
//         /**卡牌hp值（耐久值） */
//         hp: number;
//         /**卡牌血量的上限值 */
//         hpUp: number;
//         /**卡牌所属阵营 */
//         camp: ECamp;
//         /**攻击值 */
//         attack: number;
//         /**卡牌的状态 */
//         status?: ECardStatus;

//         /**卡片类型 */
//         type: ECardType
//         /**卡片图片名字 */
//         cardImageName: string
//         /**卡牌值 */
//         value: number
//         /**卡牌原始值 */
//         originValue?: number
//         /**装备类型 （当卡牌为装备卡时）*/
//         equipType?: number
//         /**装备在场景的图片名字 */
//         equipImgName?: string
//         /**加血回复类型（当卡牌和血量有关时）*/
//         hpType?: EHpType
//         /**污染类型（当卡牌和污染卡片有关时 */
//         pollutionType?: EPollutionType
//         /**是否可以使用 */
//         isCanUse?: boolean
//         /**礼物卡牌类型 */
//         giftType?: EGiftType
//         /**是否持续计时卡片 */
//         isDuration?: boolean
//     }
// }

// /**
//  * @features : 卡牌或角色的基类
//  * @description: 说明
//  * @Date : 2020-08-12 23:29:02
//  * <AUTHOR> judu233(<EMAIL>)
//  * @LastEditTime : 2023-11-14 14:07:04
//  * @LastEditors : judu233
//  */
// // @ccclass
// export default class CardBase extends Base implements ICardDataType {
//     /**存储卡牌的数据 */
//     data: ICardDataType
//     /**卡牌技能 */
//     skillMgr = new SkillManager

//     /**卡牌id */
//     @Base.ViewLinked
//     id: string

//     /**卡牌血量*/
//     @Base.ViewLinked
//     hp: number

//     /**卡牌血量的上限值 */
//     hpUp: number

//     /**卡牌的状态 */
//     @Base.ViewLinked
//     status: ECardStatus

//     /**卡牌名字 */
//     @Base.ViewLinked
//     cardName: string

//     /**卡牌的阵营 */
//     camp: ECamp

//     /**卡牌攻击力 */
//     @Base.ViewLinked
//     attack: number

//     type: ECardType

//     @Base.ViewLinked
//     value: number

//     cardImageName: string
//     originValue: number

//     get isDeath() { return this.status == ECardStatus.Death; }

//     initCard() {
//         this.status = ECardStatus.None
//         this.data.originValue = this.value
//         if (this.data.isDuration) {
//             this.schedule(this.onScheduleTimer, 1)
//         }
//     }
//     onScheduleTimer() {
//         this.value--;
//         if (this.value < 0) {
//             this.data.value = 0
//             if (this.data.pollutionType == EPollutionType.selfBomb) {
//                 BRO.broadcast(BRO.keys.DiscardCard, this.id)
//                 FightManager.playerCamp.getRole().hp -= 20
//             } else if (this.data.pollutionType == EPollutionType.pollutionOtherCard) {
//                 BRO.broadcast(BRO.keys.PolluteCard, this.id)
//             }
//             this.unschedule(this.onScheduleTimer)
//         }
//     }

//     hpAfterChangeView(value: number) {
//         if (value < 0) {
//             this.hp = 0;
//             this.status = ECardStatus.Death;
//         } else {
//             if (value >= this.hpUp) value = this.hpUp;
//             this.data.hp = value;
//         }
//     }

//     broadcast(target, key, newValue) {
//         BRO.broadcast(BRO.keys.RefreshUI, { [key]: newValue }, target)
//     }


//     /********************** 关于卡牌的操作 ********************** */
//     /**
//      * 攻击指定阵营
//      * @param camp 要攻击的阵营
//      */
//     async attackCamp(camp: CampBase) {
//         if (FightManager.isAuto || this.camp == ECamp.Computer) {
//             await Promise.all(this.attackCards(camp, 1));
//         } else {
//             await new Promise<void>(waitResolve => {
//                 BRO.once(BRO.keys.PlayerAttack, async () => {
//                     await this.attackCards(camp, 1)[0];
//                     waitResolve();
//                 }, this);
//             });
//         }
//     }

//     /**
//      * 攻击指定的卡牌
//      * @param card 要攻击的卡牌
//      */
//     async attackCard(card: CardBase) {
//         cc.log(`[${this.cardName}]卡牌开始攻击[${card.cardName}]`);
//         await Promise.wait(1000)
//         card.hp -= this.attack;
//         cc.log(`[${this.cardName}]卡牌攻击完成[${card.cardName}]`);
//     }

//     async attackRole(role: RoleBase) {
//         cc.log(`[${this.cardName}]卡牌开始攻击[${role.roleName}]`);
//         await Promise.wait(1000)
//         role.hp -= this.attack;
//         cc.log(`[${this.cardName}]卡牌攻击完成[${role.roleName}]`);
//     }

//     /**
//      * 攻击多个卡牌(同时&&轮流)
//      * @param attackCamp 要攻击的阵营
//      * @param attackCount 攻击数量
//      * @param isSimultaneously 是否同时攻击--默认同时
//      */
//     attackCards(attackCamp: CampBase, attackCount: number, isSimultaneously: boolean = true) {
//         //获取卡牌
//         let cardList = attackCamp.roleMgr.getRoleCard(attackCount)
//         let promiseList: Promise<unknown>[] = [];
//         //电脑攻击模式 -- 同时或轮流
//         if (this.camp == ECamp.Computer) {
//             isSimultaneously = FightManager.isComputerTurnAttack;
//         } else if (this.camp == ECamp.Player) {
//             isSimultaneously = FightManager.isPlayerTurnAttack;
//         }
//         if (isSimultaneously) {
//             //同步攻击
//             for (let card of cardList)
//                 promiseList.push(this.attackCard(card));
//         } else {
//             //异步攻击（轮流）
//             let selfCamp = this;
//             promiseList.push(new Promise<void>(async (resolve) => {
//                 for (let card of cardList)
//                     await selfCamp.attackCard(card);
//                 //完成攻击
//                 resolve();
//             }));
//         }
//         return promiseList;
//     }

// }
