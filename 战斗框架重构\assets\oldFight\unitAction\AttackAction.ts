export interface AttackActionProps {
    /** 前摇伤害，或者动画,单位ms */
    hurtStartTimeMs: number;
    /** 攻击持续的时间， 在攻击图中不允许攻击 */
    hurtEndTimeMs: number;
    /** 可以开始计算伤害 */
    onHurtStart: () => void;
    /** 计算伤害结束 */
    onHurtEnd: () => void;
}

/**
 * 攻击行为,触发攻击，支持一次或者多次
 */
export class AttackAction extends cc.Component {
    private isAttacking: boolean = false;
    private attackingTime: number = 0;
    private props: AttackActionProps = undefined;
    doAttackOnce(props: AttackActionProps): boolean {
        if (this.isAttacking) {
            return false;
        }
        this.isAttacking = true;
        this.attackingTime = 0;
        this.props = props;
        return true;
    }

    protected update(dt: number): void {
        if (!this.isAttacking) {
            return;
        }
        this.attackingTime += dt;
        if (this.attackingTime >= this.props.hurtStartTimeMs) {
            if (this.props.onHurtStart) {
                this.props.onHurtStart();
                this.props.onHurtStart = undefined;
            }
        }
        if (this.attackingTime >= this.props.hurtEndTimeMs) {
            // 结束
            this.isAttacking = false;
            this.attackingTime = 0;
            if (this.props.onHurtEnd) {
                this.props.onHurtEnd();
                this.props.onHurtEnd = undefined;
            }
        }
    }

}

