// import Base from "./Base/Base";
// import BuffBase from "./Base/BuffBase";

// /**  
//     * @Title : buff管理器
//     * @Description : 该类又技能类管理 - 关系： 技能 <-> buff  =  一对多
//  **/
// export default class BuffManager extends Base {
//     /**所有Buff */
//     buff: Map<string, BuffBase> = new Map();
//     /**正在释放的buff */
//     releaseBuffMap: Map<string, BuffBase> = new Map();
//     /**已失效Buff */
//     failureBuffMap: Map<string, BuffBase> = new Map();

//     /**初始化buff管理 */
//     initBuffManager(buffList: BuffBase[]) {
//         buffList.forEach(buff => {
//             this.buff.set(buff.data.id, buff);
//         });
//     }

//     /**使用buff */
//     useBuff(buff: BuffBase) {
//         buff.useBuff();
//         this.releaseBuffMap.set(buff.data.id, buff);
//     }

//     /**使用完毕，移除Buff */
//     removeBuff(buff: BuffBase) {
//         if (this.releaseBuffMap.delete(buff.data.id))
//             this.failureBuffMap.set(buff.data.id, buff);
//         else
//             cc.warn(`${this.className}移除Buff失败，未找到id:${buff.data.id}, name:${buff.data.name}的buff`);
//     }

//     /**叠加buff */
//     overlayBuff(buff: BuffBase) { }

//     /**释放buff */
//     releaseBuff(buff: BuffBase) {
//     }
// }
