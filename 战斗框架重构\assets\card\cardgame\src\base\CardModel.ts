// import { desLoadDataProxy } from "@judu233/cc-vm-core";

// declare global {
//     interface ICardModel {
//         /**心动 */
//         hert: number;
//         /**金钱 */
//         money: number;
//         /**体质 */
//         physical: number;
//         /**颜值 */
//         face: number;
//     }
// }
// @desLoadDataProxy('CardModel', 'CardModel')
// export class CardModel {
//     static hert: 10;
//     static money: 10;
//     static physical: 10;
//     static face: 1;
//     static data: IEventBar[][] = [];
//     static curDay = 1;
//     static curTab = 0;
// }