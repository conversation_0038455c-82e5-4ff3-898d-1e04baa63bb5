import { AudioMgr } from "../../../../scripts/framework/manager/AudioMgr";
import { AudioId } from "../../../../scripts/game/config/Config";
import { UtilsExtends } from "../../util/UtilsExtends";

const { ccclass, property } = cc._decorator;

export class TimelineObj {
    /**  已经运行了长时间了， 运行的时候会改变 */
    timeElapsed: number = 0;
    constructor(
        /** 深拷贝，TimelineModel中lifeDuration会修改 */
        public model: TimelineModel = UtilsExtends.deepClone(model),
        /** 是谁发起的timeline */
        public caster: cc.Node,
        /** 要发送给谁， 可以为空 */
        public target: cc.Node = undefined,
        /**多个敌人， 可以为空 */
        public targets: cc.Node[] = undefined
    ) { }
}
/*** 多个timeline组成的集合*/
export interface TimelineModel {
    nodes: TimelineNode[];
    id: number;
    name: string;
    /**Timeline一共多长时间（到时间了就丢掉了），单位秒 */
    lifeDuration: number;
}

/** * 每个timeline */
export interface TimelineNode {
    /**Timeline运行多久之后发生，单位：秒 */
    timeStartPoint: number;
    /**要执行的脚本函数 */
    event: TimelineEvent;
}

/***  具体Timeline的动作*/
export abstract class TimelineEvent {
    /** 执行具体的timeline动作 */
    abstract doEvent(timelineObj: TimelineObj, index?: number): void;
    playEffect(sound: string) {
        AudioMgr.getInstance().playEffectFree(AudioId[sound], 1, 10)
    }
}