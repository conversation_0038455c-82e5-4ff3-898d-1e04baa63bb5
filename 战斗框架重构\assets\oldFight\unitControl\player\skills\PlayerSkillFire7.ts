import FightCtrl from "../../../../ctrl/FightCtrl";
import { UtilsExtends } from "../../../../util/UtilsExtends";
import { TimelineEvent, TimelineObj, TimelineNode, TimelineModel } from "../../../timeline/TimelineObj";
import { BulletLauncher, BulletModel } from "../../bullet/BulletObj";
import { Skill, SkillModel } from "../../CharacterSkillObj";
import { CharacterControl } from "../../CharacterControl";
import { PlayerBulletHitCommon } from "../bulletOnHit/PlayerBulletHitCommon";
import { SkillNames } from "./SkillNames";
import { BulletTweenForwardFireArrowPlayer } from "../bulletTween/BulletTweenForwardFireArrowPlayer";
import { PlayerSkill1Hit } from "../bulletOnHit/PlayerSkillNormalHit";
import { AudioMgr } from "../../../../../../scripts/framework/manager/AudioMgr";
import { AudioId } from "../../../../../../scripts/game/config/Config";
import { DataMgr } from "../../../../../../scripts/game/manager/DataMgr";

class FireBulletEvent extends TimelineEvent {
    constructor(
        public launcher: BulletLauncher,
    ) { super() }
    doEvent(timelineObj: TimelineObj, index: number): void {
        let target = timelineObj.targets[index]
        if (!timelineObj.targets[index]) return

        let fireNode = FightCtrl.ins.skillStartPoint
        this.launcher.fireWorldPosition = fireNode.convertToWorldSpaceAR(cc.Vec3.ZERO)
        this.launcher.fireWorldPosition.y = FightCtrl.ins.enemyStartWordPoint.y + 100
        this.launcher.caster = timelineObj.caster;
        this.playEffect(DataMgr.getInstance().skillCfg[Number(this.launcher.model.id) - 1].sound)
        let bulletCom = FightCtrl.ins.createBullet(this.launcher, null, `partner/buddy_7_vfx/buddy_7_vfx`, 'skill_hit', true);
        bulletCom.bullet.targetWordPos = FightCtrl.ins.enemyStartWordPoint.clone()
        bulletCom.bullet.targetWordPos.x += 120
        bulletCom.bullet.targetWordPos.y = FightCtrl.ins.skillStartPoint.y

        bulletCom.scheduleOnce(() => {
            timelineObj.targets.forEach(t => bulletCom.playHit(t))
        }, 0.4)
    }
}
/**非猪向前猛冲，对全体目标造成攻击力{0}伤害*/
export class PlayerSkillFire7 extends Skill {
    constructor(
        public prefabKey: string,
    ) {
        super(SkillNames.playerSKill7);
        const launcher = new BulletLauncher();
        launcher.speed = 15
        launcher.model = new BulletModel("7", this.prefabKey);
        launcher.model.lifeDuration = 3
        launcher.model.tween = new BulletTweenForwardFireArrowPlayer();
        launcher.model.onHit = new PlayerSkill1Hit();
        this.skillMode = {
            name: this.name,
            effect: {
                id: UtilsExtends.generateUniqueId(),
                name: this.name,
                lifeDuration: 0.1,
                nodes: [
                    {
                        timeStartPoint: 0.01,
                        event: new FireBulletEvent(launcher)
                    } as TimelineNode,
                ]
            } as TimelineModel,
            buffs: [],
            coldTime: 0.5,
        }
    }
}
