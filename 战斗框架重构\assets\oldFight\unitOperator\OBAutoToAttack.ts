
import FightCtrl from "../../ctrl/FightCtrl";
import { CharacterRole } from "../unitControl/CharacterAttributes";
import { CharacterControl } from "../unitControl/CharacterControl";
import { OperatorBehavior } from "./BaseOperator";


/*** 自动并发起攻击*/
export class OBAutoToAttack extends OperatorBehavior {
    /**锁定的自动攻击的敌人 */
    public attackEnemy: CharacterControl = null
    override tickOperator(roleControl: CharacterControl, dt: number): void {
        if (FightCtrl.ins.player.isDead) {
            roleControl.playIdle(true)
            return
        }
        if (this.attackEnemy) {
            if (!this.attackEnemy.isDead) {
                roleControl.attack(this.attackEnemy);
            } else {
                if (roleControl.role != CharacterRole.HERO) {
                    roleControl.playIdle(true)
                }
            }
        } else {
            // if (roleControl.role != CharacterRole.HERO)
            //     roleControl.playIdle(true)
        }
    }
    onClear(roleControl: CharacterControl): void {
        this.attackEnemy = null;
    }
}