
// import CampManager, { ECamp } from "./CampManager";
// import { BRO } from "./EventManager";
// import SkillManager from "./SkillManager";

// /**
//  * @features : 战斗全局类
//  * @description : 战斗全局静态调用类
//  * @Date : 2023-11-10 16:14:27
//  * <AUTHOR> judu233(<EMAIL>)
//  * @LastEditTime : 2023-11-14 14:03:58
//  * @LastEditors : judu233
//  */
// export default class FightManager {
//     /**阵营管理 */
//     static campManager: CampManager;

//     /**战斗流程是否是自动的 */
//     static isAuto = false;
//     /**敌人··电脑是否轮流攻击 */
//     static isComputerTurnAttack: boolean = true;
//     /**玩家是否轮流攻击 */
//     static isPlayerTurnAttack: boolean = true;

//     static isCanUseCard = false
//     static isMonsterComing = false

//     static get playerCamp() { return this.campManager.getPlayerCamp(); }
//     static get enemyCamp() { return this.campManager.getEnemyCamp(); }

// }
