{"skeleton": {"hash": "KX1fsAvLsMI", "spine": "3.8-from-4.0-from-4.1.24", "x": -166.89, "y": -32.62, "width": 383.76, "height": 383.4, "images": "./images/", "audio": "H:/fan/goblin"}, "bones": [{"name": "root", "x": 2.72, "y": -42.68}, {"name": "bone", "parent": "root", "rotation": 4.06, "x": 18.6, "y": 144.88}, {"name": "bone2", "parent": "bone", "length": 94.61, "rotation": 80.23, "x": -0.62, "y": 4.94}, {"name": "bone3", "parent": "bone2", "length": 95.1, "rotation": 9.03, "x": 94.61}, {"name": "bone4", "parent": "bone3", "x": 55.35, "y": -30.16}, {"name": "bone5", "parent": "bone3", "x": 64.19, "y": 49.62}, {"name": "bone6", "parent": "bone", "length": 38.17, "rotation": -105.93, "x": -38.02, "y": -24.73}, {"name": "bone7", "parent": "bone6", "length": 30.9, "rotation": 7.02, "x": 38.17}, {"name": "bone8", "parent": "bone2", "length": 40.21, "rotation": -83.75, "x": 86.77, "y": -52.73}, {"name": "bone9", "parent": "bone8", "length": 62.88, "rotation": -46.13, "x": 40.21}, {"name": "bone10", "parent": "bone2", "length": 42.36, "rotation": 153.07, "x": 63.59, "y": 67.34}, {"name": "bone11", "parent": "bone10", "length": 56.67, "rotation": -41.99, "x": 34, "y": 2.87}, {"name": "bone12", "parent": "bone3", "length": 91.99, "rotation": -64.24, "x": 67.01, "y": -72}, {"name": "bone13", "parent": "bone3", "length": 89, "rotation": 61.69, "x": 63.34, "y": 68.13}, {"name": "bone14", "parent": "bone3", "x": 25.02, "y": 23.17}, {"name": "bone15", "parent": "bone3", "x": 41.37, "y": 48.09}, {"name": "bone16", "parent": "bone", "length": 38.17, "rotation": -102.75, "x": 45.7, "y": -28.76}, {"name": "bone17", "parent": "bone16", "length": 30.9, "rotation": 7.02, "x": 38.17}, {"name": "bone18", "parent": "root", "x": 1.82, "y": 34.33}], "slots": [{"name": "shadow", "bone": "bone18", "attachment": "shadow"}, {"name": "ear-right", "bone": "bone12", "attachment": "ear-right"}, {"name": "leg", "bone": "bone6", "attachment": "leg"}, {"name": "leg2", "bone": "bone16", "attachment": "leg"}, {"name": "arm-left", "bone": "bone10", "attachment": "arm-left"}, {"name": "ear-left", "bone": "bone13", "attachment": "ear-left"}, {"name": "body", "bone": "bone2", "attachment": "body"}, {"name": "arm-right", "bone": "bone8", "attachment": "arm-right"}, {"name": "head", "bone": "bone3", "attachment": "head"}, {"name": "mouse", "bone": "bone14", "attachment": "mouse"}, {"name": "mouse-beaten", "bone": "bone14"}, {"name": "eye", "bone": "bone4", "attachment": "eye"}, {"name": "eye2", "bone": "bone5", "attachment": "eye"}, {"name": "eye-beaten", "bone": "bone4"}, {"name": "eye-beaten2", "bone": "bone5"}, {"name": "nose", "bone": "bone15", "attachment": "nose"}], "skins": [{"name": "default", "attachments": {"arm-left": {"arm-left": {"type": "mesh", "uvs": [0, 0.19418, 0.1063, 0.00625, 0.30232, 0.00738, 0.43023, 0.53161, 0.61171, 0.48532, 0.62354, 0.47959, 0.63068, 0.46979, 0.72333, 0.29596, 0.87881, 0.18496, 1, 0.31712, 1, 0.36462, 0.87247, 0.59836, 0.86033, 0.62061, 0.84842, 0.64244, 0.84054, 0.65689, 0.82777, 0.68029, 0.81114, 0.68815, 0.78915, 0.69855, 0.76075, 0.71198, 0.74623, 0.71885, 0.56057, 0.80664, 0.44436, 1, 0.34372, 1, 0.20051, 0.83604, 0.18249, 0.76257, 0, 0.20426], "triangles": [21, 22, 20, 22, 23, 20, 23, 24, 20, 20, 4, 19, 4, 5, 19, 24, 3, 20, 20, 3, 4, 24, 25, 3, 1, 2, 0, 25, 2, 3, 19, 5, 18, 18, 5, 17, 14, 16, 17, 15, 16, 14, 17, 13, 14, 5, 13, 17, 13, 5, 6, 13, 6, 12, 12, 6, 11, 2, 25, 0, 6, 7, 11, 9, 10, 7, 10, 11, 7, 7, 8, 9], "vertices": [1, 11, 94.21, -74.74, 1, 1, 11, 73.76, -97.29, 1, 1, 11, 45.54, -91.48, 1, 2, 10, 53.84, -36.96, 0.07688, 11, 41.39, -16.34, 0.92312, 2, 10, 32.74, -19.42, 0.20818, 11, 13.97, -17.42, 0.79182, 2, 10, 31.06, -18.5, 0.39815, 11, 12.11, -17.86, 0.60185, 2, 10, 29.34, -18.47, 0.65457, 11, 10.81, -18.99, 0.34543, 2, 10, 1.83, -21.99, 0.8566, 11, -7.28, -40.01, 0.1434, 2, 10, -24.2, -12.89, 0.99991, 11, -32.72, -50.66, 9e-05, 2, 10, -20.12, 12.37, 0.99872, 11, -46.59, -29.15, 0.00128, 2, 10, -14.83, 16.32, 0.74548, 11, -45.29, -22.67, 0.25452, 2, 10, 22.42, 20.71, 0.45408, 11, -20.54, 5.51, 0.54592, 2, 10, 25.97, 21.12, 0.14248, 11, -18.18, 8.19, 0.85752, 2, 10, 29.45, 21.53, 0.08154, 11, -15.87, 10.82, 0.91846, 2, 10, 31.75, 21.81, 0.07442, 11, -14.34, 12.56, 0.92558, 2, 10, 35.48, 22.24, 0.11287, 11, -11.86, 15.39, 0.88713, 2, 10, 37.82, 20.94, 0.20591, 11, -9.25, 15.98, 0.79409, 2, 10, 40.91, 19.21, 0.3738, 11, -5.8, 16.76, 0.6262, 2, 10, 44.9, 16.98, 0.56086, 11, -1.34, 17.77, 0.43914, 2, 10, 46.94, 15.84, 0.53255, 11, 0.94, 18.29, 0.46745, 2, 10, 73.04, 1.25, 0.33902, 11, 30.1, 24.9, 0.66098, 2, 10, 104.8, 3.62, 0.09803, 11, 52.12, 47.91, 0.90197, 2, 10, 113.64, -8.24, 0.0142, 11, 66.63, 45.01, 0.9858, 2, 10, 107.95, -38.74, 0.00508, 11, 82.8, 18.53, 0.99492, 2, 10, 101.34, -46.97, 4e-05, 11, 83.4, 8, 0.99996, 1, 11, 94.48, -73.36, 1], "hull": 26, "edges": [0, 50, 0, 2, 2, 4, 4, 6, 16, 18, 18, 20, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 14, 16, 6, 8, 8, 10, 10, 12, 12, 14, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 24, 26, 36, 38, 38, 40, 20, 22, 22, 24], "width": 147, "height": 139}}, "arm-right": {"arm-right": {"type": "mesh", "uvs": [0.56688, 0.06964, 1, 0.60019, 1, 0.74011, 0.89181, 0.96033, 0.64872, 0.99462, 0.32048, 0.61873, 0.29636, 0.59111, 0.27602, 0.56782, 0.25462, 0.5433, 0.22619, 0.51075, 0.04986, 0.30882, 0, 0.25172, 0, 0.16244, 0.02943, 0.0194, 0.105, 0.01951, 0.32562, 0.01984, 0.36861, 0.0199, 0.44632, 0.02002, 0.52646, 0.02014], "triangles": [1, 4, 5, 7, 17, 0, 17, 18, 0, 2, 3, 4, 1, 2, 4, 0, 1, 5, 0, 5, 6, 0, 6, 7, 17, 8, 16, 15, 16, 9, 16, 8, 9, 9, 10, 15, 15, 10, 14, 10, 11, 14, 13, 14, 12, 14, 11, 12, 8, 17, 7], "vertices": [2, 8, 54.78, 10.84, 0.0996, 9, 0.72, 18.15, 0.9004, 1, 9, 70.38, 25.49, 1, 1, 9, 81.67, 17.46, 1, 1, 9, 92.74, -4.6, 1, 1, 9, 80.43, -27.77, 1, 2, 8, 31.81, -45.04, 0.45089, 9, 29.75, -34.84, 0.54911, 2, 8, 29.06, -42.47, 0.55034, 9, 26.03, -35.35, 0.44966, 2, 8, 26.75, -40.3, 0.64143, 9, 22.89, -35.79, 0.35857, 2, 8, 24.31, -38.02, 0.73542, 9, 19.59, -36.25, 0.26458, 2, 8, 21.08, -34.99, 0.82209, 9, 15.2, -36.87, 0.17791, 2, 8, 1.02, -16.19, 0.97056, 9, -12.03, -40.66, 0.02944, 2, 8, -4.65, -10.88, 0.99482, 9, -19.73, -41.73, 0.00518, 2, 8, -5.19, -2.06, 0.99999, 9, -26.93, -36.61, 1e-05, 1, 8, -2.92, 12.27, 1, 1, 8, 5.15, 12.76, 1, 2, 8, 28.71, 14.18, 0.99573, 9, -18.25, -0.04, 0.00427, 2, 8, 33.31, 14.45, 0.94947, 9, -15.58, 3.71, 0.05053, 2, 8, 41.61, 14.95, 0.61017, 9, -10.75, 10.48, 0.38983, 2, 8, 50.17, 15.47, 0.25458, 9, -5.78, 17.46, 0.74542], "hull": 19, "edges": [2, 4, 4, 6, 6, 8, 22, 24, 24, 26, 34, 36, 34, 14, 8, 10, 32, 34, 30, 32, 2, 0, 0, 36, 14, 16, 16, 18, 10, 12, 12, 14, 26, 28, 28, 30, 18, 20, 20, 22], "width": 107, "height": 99}}, "body": {"body": {"x": 32.76, "y": 3.74, "rotation": -80.23, "width": 187, "height": 186}}, "ear-left": {"ear-left": {"x": 23.72, "y": 12.65, "rotation": -150.95, "width": 137, "height": 104}}, "ear-right": {"ear-right": {"x": 33.63, "y": -10.61, "rotation": -25.02, "width": 163, "height": 92}}, "eye": {"eye": {"x": 3.73, "y": -0.4, "rotation": -89.26, "width": 40, "height": 54}}, "eye-beaten": {"eye-beaten": {"x": 0.76, "y": -2.94, "rotation": -89.26, "width": 47, "height": 26}}, "eye-beaten2": {"eye-beaten": {"x": 2.97, "y": 4.16, "scaleX": -0.684, "rotation": -102.14, "width": 47, "height": 26}}, "eye2": {"eye": {"x": 1.71, "y": -1.65, "scaleX": -0.9109, "scaleY": 0.9323, "rotation": -89.26, "width": 40, "height": 54}}, "head": {"head": {"x": 62.86, "y": -13.51, "rotation": -89.26, "width": 204, "height": 158}}, "leg": {"leg": {"type": "mesh", "uvs": [0.99062, 0.01229, 0.98863, 0.53453, 0.98845, 0.583, 0.98824, 0.6373, 0.98686, 1, 0.98416, 0.99004, 0, 0.99158, 0, 0.89012, 0.17881, 0.6397, 0.21358, 0.591, 0.24649, 0.54491, 0.28576, 0.48992, 0.3233, 0.43734, 0.58491, 0.07096], "triangles": [4, 5, 3, 6, 7, 5, 7, 8, 5, 5, 8, 3, 3, 8, 9, 3, 9, 10, 3, 10, 11, 2, 3, 11, 2, 11, 12, 2, 12, 1, 12, 13, 1, 1, 13, 0], "vertices": [2, 6, -20.94, 14.1, 0.90546, 7, -56.94, 21.22, 0.09454, 2, 6, 30.62, 22.4, 0.75513, 7, -4.75, 23.16, 0.24487, 2, 6, 35.4, 23.17, 0.54149, 7, 0.09, 23.34, 0.45851, 2, 6, 40.77, 24.04, 0.3027, 7, 5.52, 23.54, 0.6973, 2, 6, 76.57, 29.8, 0.1197, 7, 41.76, 24.88, 0.8803, 2, 6, 75.63, 29.44, 0, 7, 40.77, 24.64, 1, 2, 6, 87.72, -43.37, 0.00141, 7, 43.88, -49.11, 0.99859, 2, 6, 77.71, -45.02, 0.0662, 7, 33.74, -49.51, 0.9338, 2, 6, 50.82, -35.83, 0.16645, 7, 8.18, -37.11, 0.83355, 2, 6, 45.6, -34.05, 0.30757, 7, 3.21, -34.7, 0.69243, 2, 6, 40.65, -32.36, 0.44349, 7, -1.49, -32.42, 0.55651, 2, 6, 34.75, -30.34, 0.59777, 7, -7.1, -29.7, 0.40223, 2, 6, 29.1, -28.41, 0.78857, 7, -12.47, -27.09, 0.21143, 2, 6, -10.23, -14.98, 0.92119, 7, -49.86, -8.95, 0.07881], "hull": 14, "edges": [0, 26, 8, 10, 10, 12, 12, 14, 20, 22, 0, 2, 2, 4, 4, 6, 6, 8, 18, 20, 14, 16, 16, 18, 22, 24, 24, 26], "width": 75, "height": 100}}, "leg2": {"leg": {"type": "mesh", "uvs": [0.98709, 0.00764, 0.98759, 0.57545, 0.98762, 0.61228, 0.98765, 0.64521, 0.98795, 0.98588, 1, 0.9904, 0, 0.99015, 0, 0.88526, 0.17937, 0.63632, 0.20894, 0.59529, 0.24294, 0.5481, 0.27051, 0.50984, 0.29236, 0.47951, 0.31871, 0.44294, 0.34558, 0.40565, 0.58915, 0.06761], "triangles": [6, 4, 5, 4, 8, 3, 3, 11, 2, 7, 8, 4, 3, 8, 9, 3, 9, 10, 3, 10, 11, 11, 12, 2, 2, 12, 1, 1, 12, 13, 13, 14, 1, 4, 6, 7, 14, 15, 1, 15, 0, 1], "vertices": [1, 17, -57.4, 20.93, 1, 1, 17, -0.66, 23.24, 1, 1, 17, 3.02, 23.39, 1, 1, 17, 6.31, 23.53, 1, 1, 17, 40.35, 24.91, 1, 1, 17, 40.76, 25.83, 1, 1, 17, 43.73, -49.11, 1, 1, 17, 33.25, -49.53, 1, 1, 17, 7.84, -37.08, 1, 1, 17, 3.65, -35.03, 1, 1, 17, -1.16, -32.67, 1, 1, 17, -5.07, -30.76, 1, 1, 17, -8.16, -29.24, 1, 1, 17, -11.9, -27.41, 1, 1, 17, -15.7, -25.55, 1, 1, 17, -50.21, -8.65, 1], "hull": 16, "edges": [0, 30, 8, 10, 10, 12, 12, 14, 22, 24, 20, 22, 4, 6, 6, 8, 0, 2, 2, 4, 24, 26, 18, 20, 14, 16, 16, 18, 26, 28, 28, 30], "width": 75, "height": 100}}, "mouse": {"mouse": {"x": -7.28, "y": -12.13, "rotation": -93.32, "width": 79, "height": 27}}, "mouse-beaten": {"mouse-beaten": {"x": -7.6, "y": -17.62, "rotation": -93.32, "width": 64, "height": 33}}, "nose": {"nose": {"x": -14.71, "y": -7.56, "rotation": -89.26, "width": 73, "height": 71}}, "shadow": {"shadow": {"x": 15.72, "y": 18.23, "width": 304, "height": 85}}}}], "events": {"attack": {}}, "animations": {"attack": {"slots": {"mouse": {"attachment": [{"name": null}]}, "mouse-beaten": {"attachment": [{"name": "mouse-beaten"}]}}, "bones": {"bone2": {"rotate": [{}, {"time": 0.2333, "angle": -7.05}, {"time": 0.3333, "angle": 19.97}], "translate": [{}, {"time": 0.3333, "x": -11.65, "y": -3.63}]}, "bone3": {"rotate": [{}, {"time": 0.2333, "angle": -7.54}, {"time": 0.3333}]}, "bone6": {"rotate": [{}, {"time": 0.2333, "angle": -28.15}, {"time": 0.3333, "angle": -12.08}], "translate": [{}, {"time": 0.2333, "x": -1.4, "y": 14.78}, {"time": 0.3333}]}, "bone7": {"rotate": [{}, {"time": 0.2333, "angle": 25.53}, {"time": 0.3333, "angle": 15.43}]}, "bone8": {"rotate": [{}, {"time": 0.2333, "angle": 6.34}, {"time": 0.3333, "angle": 20.24}, {"time": 0.4667, "angle": 29.06}, {"time": 0.6667, "angle": 20.24}]}, "bone9": {"rotate": [{}, {"time": 0.2333, "angle": -12.2}, {"time": 0.3333, "angle": -20.24}, {"time": 0.4667, "angle": -38.57}, {"time": 0.6667, "angle": -20.24}]}, "bone10": {"rotate": [{}, {"time": 0.2333, "angle": -99.19}, {"time": 0.3333, "angle": -12.35}], "translate": [{}, {"time": 0.2333, "x": 9.54, "y": 2.25}, {"time": 0.3333, "x": 4.42, "y": -10.4}]}, "bone11": {"rotate": [{}, {"time": 0.2333, "angle": -15.13}, {"time": 0.3333, "angle": 32.25}]}, "bone12": {"rotate": [{}, {"time": 0.2333, "angle": 5}, {"time": 0.3333, "angle": -17.6}, {"time": 0.4667}, {"time": 0.6667, "angle": -17.6}]}, "bone13": {"rotate": [{}, {"time": 0.2333, "angle": -24.25}, {"time": 0.3333, "angle": 16.47}, {"time": 0.4667}, {"time": 0.6667, "angle": 16.47}]}, "bone14": {"translate": [{}, {"time": 0.2333, "x": -3.79, "y": 6.46}, {"time": 0.3333, "x": -2.25, "y": 5.43}, {"time": 0.4}]}, "bone15": {"rotate": [{}, {"time": 0.2333, "angle": -8.24}, {"time": 0.3333, "angle": 7.52}, {"time": 0.4333, "angle": -8.24}, {"time": 0.5333, "angle": 7.52}], "translate": [{}, {"time": 0.2333, "x": 4.99, "y": 0.43}, {"time": 0.3333, "x": -4.43, "y": 3.71}, {"time": 0.4333, "x": 4.99, "y": 0.43}, {"time": 0.5333, "x": -4.43, "y": 3.71}]}, "bone16": {"rotate": [{}, {"time": 0.2333, "angle": -8.75}, {"time": 0.3333}]}, "bone17": {"rotate": [{}, {"time": 0.2333, "angle": 6.75}, {"time": 0.3333}], "translate": [{"time": 1}]}}, "events": [{"time": 0.3333, "name": "attack"}]}, "hurt": {"slots": {"eye": {"attachment": [{"name": null}]}, "eye-beaten": {"attachment": [{"name": "eye-beaten"}]}, "eye-beaten2": {"attachment": [{"name": "eye-beaten"}]}, "eye2": {"attachment": [{"name": null}]}, "mouse": {"attachment": [{"name": null}]}, "mouse-beaten": {"attachment": [{"name": "mouse-beaten"}]}}, "bones": {"bone2": {"rotate": [{}, {"time": 0.1333, "angle": -5.73}, {"time": 0.4667, "angle": 8.44}, {"time": 1}], "translate": [{}, {"time": 0.1333, "x": 5.7, "y": 3.02}, {"time": 0.4667, "x": -5.72, "y": -5.58}, {"time": 1}]}, "bone3": {"rotate": [{}, {"time": 0.1333, "angle": -11.01}, {"time": 0.4667, "angle": 6.63}, {"time": 1}], "translate": [{}, {"time": 0.1333, "x": 5.1, "y": 3.95}, {"time": 0.4667}]}, "bone4": {"translate": [{}, {"time": 0.1333, "x": 9.48, "y": 2.94}, {"time": 0.4667}]}, "bone5": {"translate": [{}, {"time": 0.1333, "x": 9.48, "y": 2.94}, {"time": 0.4667}]}, "bone6": {"rotate": [{}, {"time": 0.1333, "angle": -42.11}, {"time": 0.4667}], "translate": [{}, {"time": 0.1333, "x": 6.23, "y": 10.52}, {"time": 0.4667}]}, "bone7": {"rotate": [{}, {"time": 0.1333, "angle": 30.77}, {"time": 0.4667}]}, "bone8": {"rotate": [{}, {"time": 0.1333, "angle": -10.41}, {"time": 0.4667, "angle": -14.31}, {"time": 1}]}, "bone9": {"rotate": [{}, {"time": 0.1333, "angle": -17.99}, {"time": 0.4667, "angle": -6.34}, {"time": 1}]}, "bone10": {"rotate": [{}, {"time": 0.1333, "angle": -49.01}, {"time": 0.4667, "angle": 2.25}, {"time": 1}]}, "bone11": {"rotate": [{}, {"time": 0.1333, "angle": 7.06}, {"time": 0.4667, "angle": -10.36}, {"time": 1}]}, "bone12": {"rotate": [{}, {"time": 0.1333, "angle": 18.34}, {"time": 0.4667, "angle": -16.11}, {"time": 0.6667, "angle": -12.9}, {"time": 1}]}, "bone13": {"rotate": [{}, {"time": 0.1333, "angle": -17.94}, {"time": 0.4667, "angle": 7.59}, {"time": 0.6667, "angle": 18.63}, {"time": 1}]}, "bone14": {"translate": [{}, {"time": 0.1333, "x": 3.76, "y": 1.59}, {"time": 0.4667, "x": -5.12, "y": 9.11}, {"time": 1}]}, "bone15": {"rotate": [{}, {"time": 0.1333, "angle": -12.96}, {"time": 0.4667}, {"time": 0.6667, "angle": -5.7}, {"time": 1}], "translate": [{}, {"time": 0.1333, "x": 10.18, "y": -0.27}, {"time": 0.4667}, {"time": 0.6667, "x": 6.74, "y": 2.06}, {"time": 1}]}, "bone16": {"rotate": [{}, {"time": 0.1333, "angle": -3.87}, {"time": 0.4667}]}, "bone17": {"rotate": [{}, {"time": 0.1333, "angle": 4.08}, {"time": 0.4667}]}}}, "idle": {"bones": {"bone14": {"translate": [{}, {"time": 0.3, "x": -1.31, "y": 1.05}, {"time": 0.5, "x": -4.34, "y": 1.97}, {"time": 1}]}, "bone2": {"rotate": [{}, {"time": 0.5, "angle": 4.46}, {"time": 1}], "translate": [{}, {"time": 0.5, "x": 0.19, "y": -6.66}, {"time": 1}]}, "bone8": {"rotate": [{}, {"time": 0.5, "angle": -12.11}, {"time": 1}]}, "bone9": {"rotate": [{}, {"time": 0.5, "angle": -7.04}, {"time": 1}]}, "bone10": {"rotate": [{}, {"time": 0.5, "angle": 12.09}, {"time": 1}], "translate": [{}, {"time": 0.5, "x": -1.43, "y": -9.91}, {"time": 1}]}, "bone11": {"rotate": [{}, {"time": 0.5, "angle": -13.16}, {"time": 1}]}, "bone12": {"rotate": [{}, {"time": 0.5, "angle": -8.89}, {"time": 1}]}, "bone13": {"rotate": [{}, {"time": 0.5, "angle": 12.74}, {"time": 1}]}, "bone15": {"rotate": [{}, {"time": 0.5, "angle": -6.69}, {"time": 1}], "translate": [{}, {"time": 0.5, "x": 4.14, "y": 2.18}, {"time": 1}]}}}}}