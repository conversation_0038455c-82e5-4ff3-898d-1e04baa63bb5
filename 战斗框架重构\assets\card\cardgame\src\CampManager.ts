// import Base from "./Base/Base";
// import CampBase from "./Base/CampBase";
// import RoleBase from "./Base/RoleBase";

// const { ccclass, property } = cc._decorator;

// /**卡牌阵营 */
// export enum ECamp {
//     /**基类 */
//     CampBase = `CampBase`,
//     /**玩家阵营 */
//     Player = `Player`,
//     /**电脑阵营 */
//     Computer = `Computer`,
// }
// declare global {
//     /**阵营数据 */
//     export interface ICampMgrDataType {
//         /**存储阵营的数据 */
//         campData: ICampDataType,
//         cardData: ICardDataType[],
//         roleData: IRoleBaseData[],
//     }
// }

// /**
//  * @features : 阵营管理
//  * @description : 说明
//  * @Date : 2020-08-17 10:24:21
//  * <AUTHOR> judu233(<EMAIL>)
//  * @LastEditTime : 2023-11-14 14:08:50
//  * @LastEditors : judu233
//  */
// @ccclass
// export default class CampManager extends Base {
//     /**卡牌阵营管理信息 */
//     data: ICampMgrDataType;

//     /**阵营列表 */
//     campList: CampBase[] = [];

//     /******关于阵营的操作*********** */
//     /**初始化阵营 */
//     initAllCamp(dataList: ICampMgrDataType[]) {
//         for (let campData of dataList) {
//             let camp = new CampBase
//             camp.initCamp(campData);
//             this.campList.push(camp)
//         }
//     }

//     /**根据阵营名字获取阵营 */
//     getCampByName(camp: ECamp) { return this.campList.find(predicate => predicate.campName == camp) }
//     /**获取先手方 */
//     getPlayerCamp() { return this.campList.find(predicate => predicate.campName == ECamp.Player); }
//     /**获取后手方 */
//     getEnemyCamp() { return this.campList.find(predicate => predicate.campName == ECamp.Computer); }
//     /**获取以玩家为首的先手方 */
//     getFirstPlayerCamp() {
//         return { firstCamp: this.getPlayerCamp(), lastCamp: this.getEnemyCamp() }
//     }
    
//     /**检查是否有任何一方已经全部死亡 */
//     checkDeath() {
//         return this.getPlayerCamp().isAllDeath || this.getEnemyCamp().isAllDeath;
//     }

//     checkMonsterDeath() {
//         return this.getEnemyCamp().curRole.isDeath;
//     }

//     checkPlayerCampIsDeath() {
//         return this.getPlayerCamp().isAllDeath;
//     }

// }
