/**
 * 角色属性实现类
 */

import { ICharacterAttributes, ICharacterResource, IAttributeModifier, AttributeModifierType } from "../interfaces/ICharacterAttributes";
import { CharacterAttributeData, CharacterResourceData } from "../types/CharacterTypes";
import { EventManager } from "../managers/EventManager";

/**
 * 角色属性实现
 */
export class CharacterAttributes implements ICharacterAttributes, ICharacterResource {
    // 基础属性
    private _baseAttributes: CharacterAttributeData;
    private _currentAttributes: CharacterAttributeData;
    private _resourceData: CharacterResourceData;

    // 属性修改器
    private _modifiers: Map<string, IAttributeModifier> = new Map();

    // 事件管理器
    private _eventManager: EventManager;

    constructor(initialData?: Partial<CharacterAttributeData>) {
        this._eventManager = new EventManager();
        this.initializeAttributes(initialData);
    }

    // ICharacterAttributes 接口实现
    get currentHp(): number { return this._resourceData.currentHp; }
    get maxHp(): number { return this._currentAttributes.maxHp; }
    get attack(): number { return this._currentAttributes.attack; }
    get defense(): number { return this._currentAttributes.defense; }
    get attackSpeed(): number { return this._currentAttributes.attackSpeed; }
    get moveSpeed(): number { return this._currentAttributes.moveSpeed; }
    get attackRange(): number { return this._currentAttributes.attackRange; }
    get level(): number { return this._currentAttributes.level; }

    // ICharacterResource 接口实现
    get currentMp(): number { return this._resourceData.currentMp; }
    get maxMp(): number { return this._currentAttributes.maxMp; }
    get currentStamina(): number { return this._resourceData.currentStamina; }
    get maxStamina(): number { return this._resourceData.maxStamina; }

    // 额外属性访问器
    get criticalRate(): number { return this._currentAttributes.criticalRate; }
    get criticalDamage(): number { return this._currentAttributes.criticalDamage; }
    get hitRate(): number { return this._currentAttributes.hitRate; }
    get dodgeRate(): number { return this._currentAttributes.dodgeRate; }
    get experience(): number { return this._currentAttributes.experience; }

    /**
     * 初始化属性
     */
    private initializeAttributes(initialData?: Partial<CharacterAttributeData>): void {
        // 设置默认值
        const defaultAttributes: CharacterAttributeData = {
            hp: 100,
            maxHp: 100,
            mp: 50,
            maxMp: 50,
            attack: 10,
            defense: 5,
            attackSpeed: 1,
            moveSpeed: 100,
            attackRange: 150,
            criticalRate: 0.05,
            criticalDamage: 1.5,
            hitRate: 0.95,
            dodgeRate: 0.05,
            level: 1,
            experience: 0
        };

        // 合并初始数据
        this._baseAttributes = { ...defaultAttributes, ...initialData };
        this._currentAttributes = { ...this._baseAttributes };

        // 初始化资源数据
        this._resourceData = {
            currentHp: this._baseAttributes.hp,
            currentMp: this._baseAttributes.mp,
            currentStamina: this._baseAttributes.maxStamina || 100,
            maxStamina: this._baseAttributes.maxStamina || 100
        };
    }

    /**
     * 修改生命值
     */
    modifyHp(amount: number): void {
        const oldHp = this._resourceData.currentHp;
        this._resourceData.currentHp = Math.max(0, Math.min(this.maxHp, this._resourceData.currentHp + amount));

        if (oldHp !== this._resourceData.currentHp) {
            this._eventManager.emit("hpChanged", {
                attributeName: "currentHp",
                oldValue: oldHp,
                newValue: this._resourceData.currentHp,
                delta: this._resourceData.currentHp - oldHp
            });
        }
    }

    /**
     * 修改魔法值
     */
    modifyMp(amount: number): void {
        const oldMp = this._resourceData.currentMp;
        this._resourceData.currentMp = Math.max(0, Math.min(this.maxMp, this._resourceData.currentMp + amount));

        if (oldMp !== this._resourceData.currentMp) {
            this._eventManager.emit("mpChanged", {
                attributeName: "currentMp",
                oldValue: oldMp,
                newValue: this._resourceData.currentMp,
                delta: this._resourceData.currentMp - oldMp
            });
        }
    }

    /**
     * 修改耐力
     */
    modifyStamina(amount: number): void {
        const oldStamina = this._resourceData.currentStamina;
        this._resourceData.currentStamina = Math.max(0, Math.min(this.maxStamina, this._resourceData.currentStamina + amount));

        if (oldStamina !== this._resourceData.currentStamina) {
            this._eventManager.emit("staminaChanged", {
                attributeName: "currentStamina",
                oldValue: oldStamina,
                newValue: this._resourceData.currentStamina,
                delta: this._resourceData.currentStamina - oldStamina
            });
        }
    }

    /**
     * 获取生命值百分比
     */
    getHpPercentage(): number {
        return this.maxHp > 0 ? this._resourceData.currentHp / this.maxHp : 0;
    }

    /**
     * 是否死亡
     */
    isDead(): boolean {
        return this._resourceData.currentHp <= 0;
    }

    /**
     * 重置到满血状态
     */
    resetToFull(): void {
        this.modifyHp(this.maxHp - this._resourceData.currentHp);
        this.modifyMp(this.maxMp - this._resourceData.currentMp);
        this.modifyStamina(this.maxStamina - this._resourceData.currentStamina);
    }

    /**
     * 检查是否有足够的魔法值
     */
    hasEnoughMp(amount: number): boolean {
        return this._resourceData.currentMp >= amount;
    }

    /**
     * 检查是否有足够的耐力
     */
    hasEnoughStamina(amount: number): boolean {
        return this._resourceData.currentStamina >= amount;
    }

    /**
     * 获取魔法值百分比
     */
    getMpPercentage(): number {
        return this.maxMp > 0 ? this._resourceData.currentMp / this.maxMp : 0;
    }

    /**
     * 获取耐力百分比
     */
    getStaminaPercentage(): number {
        return this.maxStamina > 0 ? this._resourceData.currentStamina / this.maxStamina : 0;
    }

    /**
     * 重置资源到满值状态
     */
    resetResourcesToFull(): void {
        this.modifyMp(this.maxMp - this._resourceData.currentMp);
        this.modifyStamina(this.maxStamina - this._resourceData.currentStamina);
    }

    /**
     * 添加属性修改器
     */
    addModifier(modifier: IAttributeModifier): void {
        this._modifiers.set(modifier.id, modifier);
        modifier.apply(this);
        this.recalculateAttributes();
    }

    /**
     * 移除属性修改器
     */
    removeModifier(modifierId: string): void {
        const modifier = this._modifiers.get(modifierId);
        if (modifier) {
            modifier.remove(this);
            this._modifiers.delete(modifierId);
            this.recalculateAttributes();
        }
    }

    /**
     * 获取属性修改器
     */
    getModifier(modifierId: string): IAttributeModifier | undefined {
        return this._modifiers.get(modifierId);
    }

    /**
     * 更新所有修改器
     */
    updateModifiers(deltaTime: number): void {
        const expiredModifiers: string[] = [];

        for (const [id, modifier] of this._modifiers) {
            if (modifier.update(deltaTime)) {
                expiredModifiers.push(id);
            }
        }

        // 移除过期的修改器
        for (const id of expiredModifiers) {
            this.removeModifier(id);
        }
    }

    /**
     * 重新计算属性
     */
    private recalculateAttributes(): void {
        // 重置为基础属性
        this._currentAttributes = { ...this._baseAttributes };

        // 应用所有修改器
        for (const modifier of this._modifiers.values()) {
            modifier.apply(this);
        }

        // 确保资源不超过最大值
        this._resourceData.currentHp = Math.min(this._resourceData.currentHp, this.maxHp);
        this._resourceData.currentMp = Math.min(this._resourceData.currentMp, this.maxMp);
        this._resourceData.currentStamina = Math.min(this._resourceData.currentStamina, this.maxStamina);
    }

    /**
     * 直接设置属性值（用于修改器）
     */
    setAttributeValue(attributeName: keyof CharacterAttributeData, value: number): void {
        const oldValue = this._currentAttributes[attributeName] as number;
        (this._currentAttributes as any)[attributeName] = value;

        this._eventManager.emit("attributeChanged", {
            attributeName,
            oldValue,
            newValue: value,
            delta: value - oldValue
        });
    }

    /**
     * 获取基础属性值
     */
    getBaseAttributeValue(attributeName: keyof CharacterAttributeData): number {
        return this._baseAttributes[attributeName] as number;
    }

    /**
     * 获取当前属性值
     */
    getCurrentAttributeValue(attributeName: keyof CharacterAttributeData): number {
        return this._currentAttributes[attributeName] as number;
    }

    /**
     * 设置事件监听器
     */
    on(event: string, callback: Function): void {
        this._eventManager.on(event, callback);
    }

    /**
     * 移除事件监听器
     */
    off(event: string, callback: Function): void {
        this._eventManager.off(event, callback);
    }

    /**
     * 获取属性数据的副本
     */
    getAttributeData(): CharacterAttributeData {
        return { ...this._currentAttributes };
    }

    /**
     * 获取资源数据的副本
     */
    getResourceData(): CharacterResourceData {
        return { ...this._resourceData };
    }

    /**
     * 清理资源
     */
    cleanup(): void {
        this._modifiers.clear();
        this._eventManager.cleanup();
    }
}
