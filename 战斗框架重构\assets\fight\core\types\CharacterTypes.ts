/**
 * 角色相关类型定义
 */

/**
 * 角色阵营枚举
 */
export enum CharacterRole {
    /** 无效/未定义 */
    INVALID = -1,
    
    /** 英雄/玩家 */
    HERO = 0,
    
    /** 敌人 */
    ENEMY = 1,
    
    /** 子弹/投射物 */
    BULLET = 2,
    
    /** 伙伴/宠物 */
    PARTNER = 3,
    
    /** 中立 */
    NEUTRAL = 4
}

/**
 * 角色状态枚举
 */
export enum CharacterState {
    /** 空闲 */
    IDLE = "idle",
    
    /** 移动 */
    MOVING = "moving",
    
    /** 攻击 */
    ATTACKING = "attacking",
    
    /** 释放技能 */
    CASTING = "casting",
    
    /** 受伤 */
    HURT = "hurt",
    
    /** 死亡 */
    DEAD = "dead",
    
    /** 眩晕 */
    STUNNED = "stunned",
    
    /** 沉默 */
    SILENCED = "silenced",
    
    /** 无敌 */
    INVINCIBLE = "invincible"
}

/**
 * 角色选择标签枚举
 */
export enum CharacterSelectTag {
    /** 未选中 */
    NONE = 0,
    
    /** 玩家选中 */
    PLAYER = 1,
    
    /** 伙伴选中 */
    PARTNER = 2
}

/**
 * 角色创建信息
 */
export interface CharacterCreateInfo {
    /** 预制体键值或节点 */
    prefabKey: string | cc.Node;
    
    /** 角色阵营 */
    role: CharacterRole;
    
    /** 角色名称 */
    name: string;
    
    /** 世界坐标位置 */
    worldPosition?: cc.Vec3;
    
    /** 父节点 */
    parent?: cc.Node;
    
    /** 初始属性 */
    initialAttributes?: Partial<CharacterAttributeData>;
    
    /** 初始技能列表 */
    initialSkills?: string[];
    
    /** 初始Buff列表 */
    initialBuffs?: string[];
}

/**
 * 角色属性数据
 */
export interface CharacterAttributeData {
    /** 生命值 */
    hp: number;
    
    /** 最大生命值 */
    maxHp: number;
    
    /** 魔法值 */
    mp: number;
    
    /** 最大魔法值 */
    maxMp: number;
    
    /** 攻击力 */
    attack: number;
    
    /** 防御力 */
    defense: number;
    
    /** 攻击速度 */
    attackSpeed: number;
    
    /** 移动速度 */
    moveSpeed: number;
    
    /** 攻击范围 */
    attackRange: number;
    
    /** 暴击率 */
    criticalRate: number;
    
    /** 暴击伤害 */
    criticalDamage: number;
    
    /** 命中率 */
    hitRate: number;
    
    /** 闪避率 */
    dodgeRate: number;
    
    /** 等级 */
    level: number;
    
    /** 经验值 */
    experience: number;
}

/**
 * 角色资源数据
 */
export interface CharacterResourceData {
    /** 当前生命值 */
    currentHp: number;
    
    /** 当前魔法值 */
    currentMp: number;
    
    /** 当前耐力 */
    currentStamina: number;
    
    /** 最大耐力 */
    maxStamina: number;
}

/**
 * 角色动画配置
 */
export interface CharacterAnimationConfig {
    /** 空闲动画 */
    idle: string;
    
    /** 移动动画 */
    move: string;
    
    /** 攻击动画 */
    attack: string;
    
    /** 受伤动画 */
    hurt: string;
    
    /** 死亡动画 */
    death: string;
    
    /** 技能动画映射 */
    skills?: { [skillId: string]: string };
}

/**
 * 角色音效配置
 */
export interface CharacterSoundConfig {
    /** 攻击音效 */
    attack?: string;
    
    /** 受伤音效 */
    hurt?: string;
    
    /** 死亡音效 */
    death?: string;
    
    /** 技能音效映射 */
    skills?: { [skillId: string]: string };
}

/**
 * 角色配置
 */
export interface CharacterConfig {
    /** 角色ID */
    id: string;
    
    /** 角色名称 */
    name: string;
    
    /** 角色描述 */
    description: string;
    
    /** 预制体路径 */
    prefabPath: string;
    
    /** 角色阵营 */
    role: CharacterRole;
    
    /** 基础属性 */
    baseAttributes: CharacterAttributeData;
    
    /** 动画配置 */
    animations: CharacterAnimationConfig;
    
    /** 音效配置 */
    sounds: CharacterSoundConfig;
    
    /** 默认技能列表 */
    defaultSkills: string[];
    
    /** 可学习技能列表 */
    learnableSkills: string[];
    
    /** 头像路径 */
    avatarPath?: string;
    
    /** 模型缩放 */
    scale?: number;
}

/**
 * 角色事件数据
 */
export interface CharacterEventData {
    /** 事件类型 */
    type: string;
    
    /** 角色 */
    character: any; // 这里使用any避免循环引用
    
    /** 额外数据 */
    data?: any;
    
    /** 时间戳 */
    timestamp: number;
}
