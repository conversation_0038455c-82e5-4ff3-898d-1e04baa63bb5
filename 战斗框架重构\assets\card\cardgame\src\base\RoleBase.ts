// import BuffManager from "../BuffManager";
// import { ECamp } from "../CampManager";
// import CardManager from "../CardManager";
// import EquipmentManager from "../EquipmentManager";
// import { BRO } from "../EventManager";
// import FightManager from "../FightManager";
// import SkillManager from "../SkillManager";
// import Base from "./Base";

// const { ccclass, property } = cc._decorator;

// export enum ERoleStatus {
//     /**角色状态-正常 */
//     Normal = `Normal`,
//     /**角色状态-死亡 */
//     Death = `Death`,
//     /**角色状态-冻结 */
//     Frozen = `Frozen`,
// }

// declare global {
//     type IRoleBaseName = 'RoleBase'
//     export interface IRoleBaseData extends IBaseDataType {
//         hp: number;
//         hpUp: number;
//         roleName: string;
//         /**攻击力 */
//         attack: number;
//         /**防御力 */
//         defence: number;
//         level: number;
//         camp: ECamp;

//         status?: ERoleStatus;
//         exp?: number;
//     }
// }

// /**
//  * @features : 角色控制基类
//  * @description: 游戏中多个角色的控制基类
//  * @Date : 2020-08-12 23:28:52
//  * <AUTHOR> judu233(<EMAIL>)
//  * @LastEditTime : 2023-11-14 13:57:04
//  * @LastEditors : judu233
//  */
// // @ccclass
// export default class RoleBase extends Base implements IRoleBaseData {
//     data: IRoleBaseData;

//     /**角色卡片管理 */
//     cardMgr = new CardManager
//     /**角色技能管理 */
//     skillMgr = new SkillManager
//     /**对角色使用buff管理 */
//     buffMgr = new BuffManager
//     /**角色装备 */
//     equipMgr = new EquipmentManager

//     /**角色id */
//     @Base.ViewLinked
//     id: string;

//     /**角色血量 */
//     @Base.ViewLinked
//     hp: number;
//     @Base.ViewLinked
//     hpUp: number;

//     /**角色名字 */
//     @Base.ViewLinked
//     roleName: string;

//     /**角色状态 */
//     @Base.ViewLinked
//     status: ERoleStatus;

//     /**角色的攻击力 */
//     @Base.ViewLinked
//     attack: number;

//     /**角色的防御力 */
//     @Base.ViewLinked
//     defence: number;

//     /**角色的等级 */
//     @Base.ViewLinked
//     level: number;

//     /**角色的经验值 */
//     @Base.ViewLinked
//     exp: number;

//     /**卡牌的阵营 */
//     @Base.ViewLinked
//     camp: ECamp;

//     get isDeath() { return this.status == ERoleStatus.Death; }

//     initRole(initData: IRoleBaseData) {
//         let { hp, hpUp, roleName, attack, defence, level, camp } = initData
//         this.data = { ...initData }
//         this.camp = camp
//         this.hpUp = hpUp
//         this.hp = hp
//         this.roleName = roleName
//         this.attack = attack
//         this.defence = defence
//         this.level = level
//         this.exp = 0
//         this.status = ERoleStatus.Normal
//     }

//     expChangeView(newValue: number) {
//         if (newValue >= 100) {
//             this.exp = 0;
//             this.level++;
//         }
//     }

//     hpChangeView(newValue: number) {
//         if (newValue <= 0) {
//             this.status = ERoleStatus.Death;
//             return 0
//         } else if (newValue > this.hpUp) {
//             return this.hpUp
//         } else {
//             return newValue
//         }
//     }

//     statusAfterChangeView(value: ERoleStatus) {
//         if (value == ERoleStatus.Death) {
//             BRO.broadcast(BRO.keys.RefreshUI, { [`${this.camp}${this.className}hp`]: 0 }, this as any)
//             FightManager.campManager.getCampByName(this.camp).roleMgr.deleateRole(this);
//         }
//     }
// }
