{"skeleton": {"hash": "rpoUJPm/yJY", "spine": "3.8-from-4.0-from-4.1.24", "x": -154.99, "y": -24.75, "width": 307.74, "height": 411.43, "images": "./images/", "audio": "H:/fan/robot"}, "bones": [{"name": "root", "x": 6.83, "y": -45.5}, {"name": "bone", "parent": "root", "x": -26.04, "y": 148.07}, {"name": "bone2", "parent": "bone", "length": 61.3, "rotation": 82.82, "x": -0.51, "y": -0.51}, {"name": "bone3", "parent": "bone2", "length": 61.59, "rotation": 1.95, "x": 61.3}, {"name": "bone4", "parent": "bone3", "length": 89.74, "rotation": -4.6, "x": 61.59}, {"name": "bone5a", "parent": "bone4", "length": 21.32, "rotation": -4.03, "x": 93.89, "y": -0.78}, {"name": "bone5b", "parent": "bone5a", "length": 14.21, "x": 21.32}, {"name": "bone5c", "parent": "bone5b", "length": 7.11, "x": 14.21}, {"name": "bone5", "parent": "bone3", "length": 58.1, "rotation": -93.08, "x": 31.42, "y": -54.68}, {"name": "bone6", "parent": "bone5", "length": 68.74, "rotation": -66.8, "x": 58.1}, {"name": "bone7", "parent": "bone6", "length": 32.46, "rotation": -27.68, "x": 68.74}, {"name": "bone8", "parent": "bone3", "length": 70.28, "rotation": 101.38, "x": 22.68, "y": 21.35}, {"name": "bone9", "parent": "bone8", "length": 57.49, "rotation": 69.61, "x": 70.28}, {"name": "bone10", "parent": "bone9", "length": 25.27, "rotation": 10.24, "x": 57.49}, {"name": "bone11", "parent": "bone", "length": 50.83, "rotation": -79.47, "x": 53.42, "y": -5.82}, {"name": "bone12", "parent": "bone11", "length": 35.41, "rotation": -8.38, "x": 50.83}, {"name": "bone13", "parent": "bone", "length": 38.25, "rotation": -98.53, "x": -18.5, "y": -17.1}, {"name": "bone14", "parent": "bone13", "length": 37.03, "rotation": 3.4, "x": 38.25}, {"name": "bone15", "parent": "root", "x": -6.72, "y": 50.89}, {"name": "bone16", "parent": "root", "x": -112.15, "y": 377.11}, {"name": "bone17", "parent": "root", "x": -97.96, "y": 340.23}, {"name": "bone18", "parent": "root", "x": -11.44, "y": 472.14}, {"name": "bone19", "parent": "bone4", "x": 59.08, "y": -12.29}, {"name": "bone20", "parent": "bone4", "x": 52.13, "y": 39.83}, {"name": "bone21", "parent": "root", "x": -45.57, "y": 446.21}, {"name": "bone22", "parent": "root", "x": 30.03, "y": 470.55}], "slots": [{"name": "shadow", "bone": "bone15", "attachment": "shadow"}, {"name": "leg", "bone": "root", "attachment": "leg"}, {"name": "leg2", "bone": "bone13", "attachment": "leg"}, {"name": "line", "bone": "root", "attachment": "line"}, {"name": "arm2", "bone": "bone8", "attachment": "arm"}, {"name": "waist", "bone": "bone2", "attachment": "waist"}, {"name": "crotch", "bone": "bone", "attachment": "crotch"}, {"name": "brust", "bone": "bone3", "attachment": "brust"}, {"name": "head", "bone": "bone4", "attachment": "head"}, {"name": "eye", "bone": "bone19", "attachment": "eye"}, {"name": "eye2", "bone": "bone20", "attachment": "eye"}, {"name": "eye-attack", "bone": "bone19"}, {"name": "eye-attack2", "bone": "bone20"}, {"name": "eye-hurt", "bone": "bone20"}, {"name": "arm", "bone": "bone5", "attachment": "arm"}, {"name": "hand-right", "bone": "bone7", "attachment": "hand-right"}, {"name": "hand-left", "bone": "bone10", "attachment": "hand-left"}, {"name": "effect", "bone": "bone16"}, {"name": "effect-2", "bone": "bone18"}, {"name": "effect-3", "bone": "bone21"}, {"name": "effect-4", "bone": "bone22"}, {"name": "screw", "bone": "bone16"}, {"name": "fly", "bone": "root", "attachment": "fly"}], "path": [{"name": "fly", "bones": ["bone16"], "target": "fly", "rotation": -106.72, "position": 0.2614}], "skins": [{"name": "default", "attachments": {"arm": {"arm": {"type": "mesh", "uvs": [0.32262, 0, 0.64535, 0.05625, 0.68682, 0.06348, 0.73741, 0.0723, 0.79297, 0.08199, 0.848, 0.09158, 0.87346, 0.14205, 0.89708, 0.18888, 0.91918, 0.23268, 0.94199, 0.2779, 0.99121, 0.37546, 0.98898, 1, 0.68467, 0.98563, 0.52755, 0.81099, 0.53394, 0.52057, 0.53222, 0.45394, 0.48974, 0.42851, 0.12381, 0.36221, 0, 0.18355, 0, 0.14114, 0.05338, 0], "triangles": [6, 15, 5, 10, 11, 13, 11, 12, 13, 10, 13, 14, 10, 15, 9, 9, 15, 8, 8, 15, 7, 7, 15, 6, 15, 10, 14, 15, 4, 5, 15, 3, 4, 15, 2, 3, 15, 1, 2, 15, 16, 1, 17, 0, 16, 16, 0, 1, 17, 18, 0, 20, 0, 19, 0, 18, 19], "vertices": [1, 8, 19.42, 17.5, 1, 2, 8, 49.9, 16.49, 0.99701, 9, -18.12, -3.31, 0.00299, 2, 8, 53.82, 16.36, 0.96931, 9, -16.91, 0.41, 0.03069, 2, 8, 58.6, 16.21, 0.86752, 9, -15.44, 4.96, 0.13248, 2, 8, 63.84, 16.03, 0.67961, 9, -13.82, 9.96, 0.32039, 2, 8, 69.04, 15.86, 0.52918, 9, -12.21, 14.9, 0.47082, 2, 8, 72.08, 11.41, 0.37, 9, -7.09, 16.59, 0.63, 2, 8, 74.91, 7.28, 0.18481, 9, -2.34, 18.16, 0.81519, 2, 8, 77.55, 3.42, 0.06634, 9, 2.1, 19.63, 0.93366, 2, 8, 80.28, -0.57, 0.01157, 9, 6.69, 21.15, 0.98843, 1, 9, 16.59, 24.42, 1, 1, 9, 75.97, 16.11, 1, 1, 9, 70.78, -11.74, 1, 2, 8, 49.54, -56.78, 1e-05, 9, 52.19, -23.96, 0.99999, 2, 8, 46.1, -29.11, 0.26791, 9, 24.65, -19.6, 0.73209, 2, 8, 45.01, -22.8, 0.56739, 9, 18.29, -18.9, 0.43261, 2, 8, 40.75, -20.96, 0.83714, 9, 15.33, -22.47, 0.16286, 2, 8, 6.16, -19.58, 0.99986, 9, 4.43, -55.34, 0.00014, 1, 8, -7.72, -4.28, 1, 1, 8, -8.31, -0.25, 1, 1, 8, -5.35, 13.87, 1], "hull": 21, "edges": [0, 40, 20, 22, 22, 24, 36, 38, 38, 40, 34, 36, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 8, 10, 10, 12, 12, 14, 6, 8, 4, 6, 14, 16, 16, 18, 18, 20, 0, 2, 2, 4, 30, 10], "width": 93, "height": 96}}, "arm2": {"arm": {"type": "mesh", "uvs": [0, 0.29509, 0.00167, 0.0831, 0.06679, 0, 0.2752, 0, 0.64779, 0.01507, 0.70764, 0.01749, 0.76872, 0.05492, 0.82539, 0.08965, 0.87838, 0.12213, 0.92442, 0.15035, 0.96294, 0.17395, 0.97896, 0.21431, 1, 0.26733, 1, 0.3263, 1, 0.72916, 1, 1, 0.7884, 1, 0.57999, 1, 0.57478, 0.8932, 0.55134, 0.72916, 0.53331, 0.50326, 0.53326, 0.48453, 0.53227, 0.46589, 0.52275, 0.44896, 0.50735, 0.43681, 0.48869, 0.43306, 0.17881, 0.3708], "triangles": [16, 14, 15, 19, 14, 16, 16, 17, 18, 16, 18, 19, 14, 20, 13, 13, 20, 11, 10, 11, 9, 8, 9, 11, 8, 11, 20, 7, 8, 21, 19, 20, 14, 6, 7, 22, 21, 8, 20, 21, 22, 7, 11, 12, 13, 6, 22, 5, 24, 4, 5, 5, 22, 23, 5, 23, 24, 24, 25, 4, 26, 3, 25, 25, 3, 4, 0, 1, 26, 26, 1, 3, 3, 1, 2], "vertices": [1, 11, 8.02, 9.65, 1, 1, 11, 5.99, -10.6, 1, 1, 11, 11.16, -19.18, 1, 1, 11, 30.43, -21.25, 1, 2, 11, 65.04, -23.52, 0.91027, 12, -23.87, -3.28, 0.08973, 2, 11, 70.6, -23.89, 0.81518, 12, -22.28, -8.62, 0.18482, 2, 11, 76.63, -20.92, 0.63594, 12, -17.4, -13.24, 0.36406, 2, 11, 82.23, -18.17, 0.40348, 12, -12.87, -17.53, 0.59652, 2, 11, 87.46, -15.6, 0.21036, 12, -8.63, -21.54, 0.78964, 2, 11, 92.01, -13.36, 0.09922, 12, -4.95, -25.02, 0.90078, 2, 11, 95.81, -11.49, 0.05211, 12, -1.88, -27.94, 0.94789, 2, 11, 97.71, -7.8, 0.0258, 12, 2.25, -28.43, 0.9742, 2, 11, 100.2, -2.95, 0.00489, 12, 7.66, -29.07, 0.99511, 1, 12, 13.15, -27.68, 1, 1, 12, 50.63, -18.16, 1, 1, 12, 75.83, -11.76, 1, 1, 12, 70.99, 7.32, 1, 1, 12, 66.22, 26.1, 1, 1, 12, 56.16, 24.05, 1, 2, 11, 63.46, 45.6, 0.0003, 12, 40.36, 22.28, 0.9997, 2, 11, 59.47, 24.21, 0.16887, 12, 18.93, 18.57, 0.83113, 2, 11, 59.27, 22.43, 0.2288, 12, 17.19, 18.13, 0.7712, 2, 11, 58.99, 20.66, 0.31433, 12, 15.43, 17.78, 0.68567, 2, 11, 57.93, 19.14, 0.43648, 12, 13.64, 18.24, 0.56352, 2, 11, 56.39, 18.13, 0.56261, 12, 12.15, 19.34, 0.43739, 2, 11, 54.62, 17.96, 0.66496, 12, 11.38, 20.93, 0.33504, 1, 11, 25.33, 15.1, 1], "hull": 27, "edges": [0, 2, 2, 4, 4, 6, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 0, 52, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 6, 8, 8, 10, 20, 22, 22, 24, 50, 52, 24, 26, 26, 28, 38, 40, 40, 42, 46, 48, 48, 50, 42, 44, 44, 46], "width": 93, "height": 96}}, "brust": {"brust": {"x": 9.06, "y": -13.29, "rotation": -84.76, "width": 144, "height": 102}}, "crotch": {"crotch": {"x": 17.36, "y": -8.33, "width": 129, "height": 45}}, "effect": {"effect": {"x": 14.33, "y": -41.93, "width": 52, "height": 78}}, "effect-2": {"effect-2": {"x": 1.12, "y": -3.46, "width": 43, "height": 93}}, "effect-3": {"effect-2": {"x": -2.69, "y": -0.03, "scaleX": 0.4693, "scaleY": 0.4693, "width": 43, "height": 93}}, "effect-4": {"effect-2": {"x": 0.28, "y": -0.09, "scaleX": 0.4693, "scaleY": 0.4693, "width": 43, "height": 93}}, "eye": {"eye": {"x": -0.82, "y": 11.79, "rotation": -80.16, "width": 32, "height": 53}}, "eye-attack": {"eye-attack": {"x": -0.82, "y": 11.79, "rotation": -80.16, "width": 32, "height": 53}}, "eye-attack2": {"eye-attack": {"x": -1.23, "y": -1.96, "scaleX": -0.8505, "scaleY": 0.9307, "rotation": -103.41, "width": 32, "height": 53}}, "eye-hurt": {"eye-hurt": {"x": 0.24, "y": -23.07, "rotation": -80.16, "width": 68, "height": 34}}, "eye2": {"eye": {"x": -0.88, "y": -2.45, "scaleX": -0.7826, "scaleY": 0.9269, "rotation": -101.5, "width": 32, "height": 53}}, "fly": {"fly": {"type": "path", "lengths": [161.06, 342.16], "vertexCount": 6, "vertices": [-62.58, 277.21, -78.63, 348.88, -108.95, 393.42, -165.07, 440.7, -212.18, 406.85, -245.37, 382.99]}}, "hand-left": {"hand-left": {"x": 11.16, "y": 5.08, "rotation": 94.01, "width": 88, "height": 68}}, "hand-right": {"hand-right": {"x": 13.41, "y": -4.75, "rotation": 109.92, "width": 72, "height": 68}}, "head": {"head": {"x": 31.14, "y": -4.18, "rotation": -80.16, "width": 164, "height": 137}}, "leg": {"leg": {"type": "mesh", "uvs": [0.25786, 1e-05, 0.38165, 0.00935, 0.4533, 0.07608, 0.55311, 0.40794, 0.55958, 0.42944, 0.57066, 0.46628, 0.57624, 0.48484, 0.58242, 0.50538, 0.58195, 0.52389, 0.58117, 0.55427, 0.58053, 0.57907, 0.57849, 0.65844, 0.88367, 0.70104, 1, 0.86131, 1, 1, 0.14005, 1, 0.12068, 0.55455, 0.12001, 0.53909, 0.11577, 0.52565, 0, 0.15844, 0, 0.10961, 0.12383, 0], "triangles": [15, 11, 14, 14, 11, 13, 11, 12, 13, 15, 16, 11, 11, 16, 10, 10, 16, 9, 16, 17, 9, 9, 17, 8, 8, 17, 7, 17, 5, 6, 5, 18, 4, 4, 18, 3, 18, 5, 17, 3, 19, 2, 1, 2, 0, 0, 2, 19, 21, 0, 20, 3, 18, 19, 7, 17, 6, 0, 19, 20], "vertices": [1, 14, -2.87, 3.49, 1, 1, 14, -0.31, 12.08, 1, 1, 14, 7.33, 15.91, 1, 2, 14, 41.92, 16.79, 0.90971, 15, -11.26, 15.31, 0.09029, 2, 14, 44.16, 16.85, 0.85047, 15, -9.05, 15.7, 0.14953, 2, 14, 48, 16.95, 0.70836, 15, -5.27, 16.35, 0.29164, 2, 14, 49.94, 17, 0.62232, 15, -3.36, 16.68, 0.37768, 2, 14, 52.08, 17.05, 0.52576, 15, -1.25, 17.05, 0.47424, 2, 14, 53.93, 16.67, 0.43926, 15, 0.63, 16.94, 0.56074, 2, 14, 56.96, 16.05, 0.29533, 15, 3.73, 16.77, 0.70467, 2, 14, 59.44, 15.54, 0.19322, 15, 6.25, 16.63, 0.80678, 2, 14, 67.37, 13.92, 0.01639, 15, 14.34, 16.18, 0.98361, 1, 15, 19.5, 37.98, 1, 1, 15, 36.15, 45.73, 1, 1, 15, 50.29, 45.2, 1, 1, 15, 47.97, -16.67, 1, 2, 14, 50.93, -16.55, 0.42082, 15, 2.51, -16.36, 0.57918, 2, 14, 49.37, -16.31, 0.51215, 15, 0.94, -16.35, 0.48785, 2, 14, 47.97, -16.36, 0.59377, 15, -0.44, -16.6, 0.40623, 1, 14, 9.62, -17.71, 1, 1, 14, 4.73, -16.8, 1, 1, 14, -4.64, -5.99, 1], "hull": 22, "edges": [0, 42, 0, 2, 26, 28, 28, 30, 38, 40, 40, 42, 24, 26, 22, 24, 2, 4, 12, 14, 14, 16, 10, 12, 8, 10, 16, 18, 18, 20, 20, 22, 4, 6, 6, 8, 14, 34, 34, 36, 36, 38, 30, 32, 32, 34], "width": 72, "height": 102}}, "leg2": {"leg": {"type": "mesh", "uvs": [0, 0.05836, 0.17899, 0, 0.37497, 0, 0.4456, 0.07997, 0.56547, 0.43681, 0.57611, 0.46848, 0.588, 0.50387, 0.58992, 0.53428, 0.59248, 0.57491, 0.59772, 0.65814, 0.90555, 0.70535, 1, 0.86139, 1, 0.93431, 0.97068, 1, 0.13888, 1, 0.11243, 0.54217, 0.11178, 0.53105, 0.11131, 0.52281, 0.10793, 0.46435, 0, 0.15807], "triangles": [14, 9, 13, 13, 9, 11, 12, 13, 11, 9, 15, 8, 8, 15, 7, 7, 15, 6, 9, 14, 15, 11, 9, 10, 5, 6, 16, 6, 15, 16, 16, 17, 5, 17, 4, 5, 17, 18, 4, 18, 3, 4, 3, 19, 1, 1, 2, 3, 3, 18, 19, 19, 0, 1], "vertices": [1, 16, -12, 16.39, 1, 1, 16, -15.97, 2.76, 1, 1, 16, -13.88, -11.19, 1, 1, 16, -5.06, -15.01, 1, 2, 16, 32.22, -18.15, 0.81117, 17, -7.1, -17.76, 0.18883, 2, 16, 35.52, -18.43, 0.68867, 17, -3.81, -18.23, 0.31133, 2, 16, 39.22, -18.74, 0.53389, 17, -0.14, -18.76, 0.46611, 2, 16, 42.31, -18.41, 0.40056, 17, 2.96, -18.62, 0.59944, 2, 16, 46.43, -17.98, 0.23343, 17, 7.11, -18.43, 0.76657, 2, 16, 54.89, -17.09, 0.02841, 17, 15.6, -18.05, 0.97159, 1, 17, 22.37, -39.7, 1, 1, 17, 38.83, -45.05, 1, 1, 17, 46.24, -44.38, 1, 1, 17, 52.73, -41.68, 1, 1, 17, 47.37, 17.97, 1, 2, 16, 38, 15.71, 0.45868, 17, 0.69, 15.69, 0.54132, 2, 16, 36.88, 15.58, 0.51906, 17, -0.44, 15.64, 0.48094, 2, 16, 36.04, 15.49, 0.564, 17, -1.28, 15.6, 0.436, 2, 16, 30.11, 14.85, 0.84295, 17, -7.24, 15.31, 0.15705, 1, 16, -1.94, 17.9, 1], "hull": 20, "edges": [0, 38, 0, 2, 2, 4, 22, 24, 24, 26, 26, 28, 36, 38, 20, 22, 18, 20, 4, 6, 12, 14, 10, 12, 6, 8, 8, 10, 14, 16, 16, 18, 32, 34, 34, 36, 28, 30, 30, 32], "width": 72, "height": 102}}, "line": {"line": {"type": "mesh", "uvs": [0.87361, 0, 1, 0.20608, 1, 0.30724, 0.83897, 0.51155, 0.81633, 0.54028, 0.78253, 0.58316, 0.75056, 0.62373, 0.72041, 0.66199, 0.69395, 0.69555, 0.663, 0.73482, 0.6338, 0.77188, 0.60999, 0.80208, 0.58031, 0.83974, 0.54869, 0.87987, 0.5236, 0.9117, 0.49861, 0.9434, 0.454, 1, 0.24267, 1, 0, 0.95881, 0, 0.9283, 0.00883, 0.88183, 0.01586, 0.84478, 0.02267, 0.80895, 0.03013, 0.76966, 0.03712, 0.73285, 0.04332, 0.70021, 0.04901, 0.67027, 0.05876, 0.61893, 0.06708, 0.5751, 0.07471, 0.53494, 0.08175, 0.49789, 0.09038, 0.45243, 0.09578, 0.424, 0.16565, 0.05616, 0.51196, 0], "triangles": [16, 17, 15, 20, 15, 17, 17, 18, 19, 21, 14, 15, 20, 17, 19, 22, 13, 14, 21, 15, 20, 23, 12, 13, 22, 14, 21, 24, 11, 12, 23, 13, 22, 25, 10, 11, 24, 12, 23, 26, 9, 10, 25, 11, 24, 10, 25, 26, 8, 9, 27, 28, 7, 8, 27, 9, 26, 29, 6, 7, 28, 8, 27, 30, 5, 6, 29, 7, 28, 30, 6, 29, 3, 32, 2, 32, 3, 4, 31, 4, 5, 31, 5, 30, 4, 31, 32, 34, 2, 32, 32, 33, 34, 2, 34, 1, 34, 0, 1], "vertices": [1, 7, 39.09, -9.84, 1, 2, 7, 22.17, -20.4, 0.97711, 6, 36.39, -20.4, 0.02289, 1, 7, 13.14, -22.63, 1, 2, 6, 7.21, -19.47, 0.8026, 5, 28.53, -19.47, 0.1974, 2, 6, 4.38, -19.03, 0.71624, 5, 25.7, -19.03, 0.28376, 3, 7, -14.06, -18.37, 0.06647, 6, 0.15, -18.37, 0.51292, 5, 21.47, -18.37, 0.42061, 3, 7, -18.06, -17.74, 0.02169, 6, -3.85, -17.74, 0.36559, 5, 17.48, -17.74, 0.61272, 3, 7, -21.83, -17.15, 0.00455, 6, -7.62, -17.15, 0.22301, 5, 13.7, -17.15, 0.77244, 3, 7, -25.14, -16.63, 0.00037, 6, -10.93, -16.63, 0.12528, 5, 10.4, -16.63, 0.87436, 2, 6, -14.8, -16.03, 0.0523, 5, 6.52, -16.03, 0.9477, 2, 6, -18.45, -15.45, 0.01699, 5, 2.87, -15.45, 0.98301, 2, 6, -21.43, -14.99, 0.00466, 5, -0.1, -14.99, 0.99534, 2, 6, -25.14, -14.41, 0.00023, 5, -3.82, -14.41, 0.99977, 1, 5, -7.77, -13.79, 1, 1, 5, -10.91, -13.29, 1, 1, 5, -14.04, -12.81, 1, 1, 5, -19.61, -11.93, 1, 1, 5, -22.1, -1.88, 1, 1, 5, -21.27, 10.57, 1, 1, 5, -18.54, 11.25, 1, 1, 5, -14.29, 11.85, 1, 1, 5, -10.9, 12.33, 1, 1, 5, -7.62, 12.8, 1, 1, 5, -4.02, 13.31, 1, 2, 6, -21.97, 13.79, 0.00108, 5, -0.65, 13.79, 0.99892, 2, 6, -18.98, 14.22, 0.00719, 5, 2.34, 14.22, 0.99281, 2, 6, -16.24, 14.61, 0.02283, 5, 5.08, 14.61, 0.97717, 2, 6, -11.54, 15.28, 0.0874, 5, 9.78, 15.28, 0.9126, 3, 7, -21.75, 15.85, 0.00245, 6, -7.53, 15.85, 0.19927, 5, 13.79, 15.85, 0.79829, 3, 7, -18.07, 16.37, 0.01517, 6, -3.85, 16.37, 0.34333, 5, 17.47, 16.37, 0.6415, 3, 7, -14.68, 16.85, 0.04542, 6, -0.46, 16.85, 0.48318, 5, 20.86, 16.85, 0.4714, 2, 6, 3.7, 17.44, 0.68568, 5, 25.02, 17.44, 0.31432, 2, 6, 6.3, 17.81, 0.77393, 5, 27.62, 17.81, 0.22607, 2, 7, 25.76, 22.6, 0.99768, 6, 39.98, 22.6, 0.00232, 1, 7, 34.85, 7.37, 1], "hull": 35, "edges": [0, 68, 0, 2, 2, 4, 32, 34, 34, 36, 36, 38, 66, 68, 46, 48, 22, 24, 44, 46, 24, 26, 46, 24, 42, 44, 26, 28, 48, 50, 20, 22, 50, 52, 18, 20, 52, 54, 16, 18, 54, 56, 14, 16, 56, 58, 12, 14, 58, 60, 10, 12, 60, 62, 8, 10, 62, 64, 64, 66, 4, 6, 6, 8, 38, 40, 40, 42, 28, 30, 30, 32], "width": 49, "height": 92}}, "screw": {"screw": {"x": 1.83, "y": -4.43, "width": 73, "height": 55}}, "shadow": {"shadow": {"x": -3.93, "y": -1.64, "width": 256, "height": 57}}, "waist": {"waist": {"x": 29.01, "y": -17.24, "rotation": -82.82, "width": 120, "height": 90}}}}], "events": {"attack": {}}, "animations": {"attack": {"slots": {"effect-2": {"color": [{"time": 0.8333, "color": "ffffffff"}, {"time": 0.8667, "color": "ffffff00"}], "attachment": [{"time": 0.4333, "name": "effect-2"}]}, "effect-3": {"color": [{"time": 0.8333, "color": "ffffffff"}, {"time": 0.8667, "color": "ffffff00"}], "attachment": [{"time": 0.4333, "name": "effect-2"}]}, "effect-4": {"color": [{"time": 0.8333, "color": "ffffffff"}, {"time": 0.8667, "color": "ffffff00"}], "attachment": [{"time": 0.4333, "name": "effect-2"}]}, "eye": {"attachment": [{"name": null}]}, "eye-attack": {"attachment": [{"name": "eye-attack"}]}, "eye-attack2": {"attachment": [{"name": "eye-attack"}]}, "eye2": {"attachment": [{"name": null}]}}, "bones": {"bone": {"rotate": [{}, {"time": 0.2667, "angle": -4.02}, {"time": 0.4333, "angle": 13.09, "curve": "stepped"}, {"time": 0.8333, "angle": 13.09}, {"time": 1}], "translate": [{}, {"time": 0.2667, "x": 6.18}, {"time": 1}]}, "bone2": {"rotate": [{}, {"time": 0.2667, "angle": -6.59}, {"time": 0.4333, "angle": 3.3, "curve": "stepped"}, {"time": 0.8333, "angle": 3.3}, {"time": 1}], "translate": [{}, {"time": 0.4333, "x": 2.61, "y": -7.26, "curve": "stepped"}, {"time": 0.8333, "x": 2.61, "y": -7.26}, {"time": 1}]}, "bone3": {"rotate": [{}, {"time": 0.2667, "angle": -2.82}, {"time": 0.4333, "angle": 10.51, "curve": "stepped"}, {"time": 0.8333, "angle": 10.51}, {"time": 1}], "translate": [{}, {"time": 0.4333, "x": -1.18, "y": 4.72, "curve": "stepped"}, {"time": 0.8333, "x": -1.18, "y": 4.72}, {"time": 1}]}, "bone4": {"rotate": [{}, {"time": 0.2667, "angle": -4.17}, {"time": 0.4333, "angle": 12.53}, {"time": 0.5, "angle": 18.2}, {"time": 0.6333, "angle": 8.57}, {"time": 0.8333, "angle": 12.53}, {"time": 1}], "translate": [{}, {"time": 0.4333, "x": -2.29, "y": 13.28}, {"time": 0.5, "x": -8.54, "y": 22.97}, {"time": 0.6333, "x": -1.95, "y": 9.29}, {"time": 0.8333, "x": -2.29, "y": 13.28}, {"time": 1}]}, "bone5a": {"rotate": [{}, {"time": 0.2667, "angle": -20.21}, {"time": 0.4333, "angle": 32.24}, {"time": 0.6333, "angle": 12.98}, {"time": 0.8333, "angle": 32.24}, {"time": 1}], "translate": [{}, {"time": 0.4333, "x": -1.18, "y": 26.38, "curve": "stepped"}, {"time": 0.8333, "x": -1.18, "y": 26.38}, {"time": 1}]}, "bone5b": {"rotate": [{}, {"time": 0.2667, "angle": -20.21}, {"time": 0.4333, "angle": 32.24}, {"time": 0.6333, "angle": -0.37}, {"time": 0.8333, "angle": 32.24}, {"time": 1}]}, "bone5c": {"rotate": [{}, {"time": 0.4333, "angle": -0.71}, {"time": 0.6333, "angle": -1.34}, {"time": 0.8333, "angle": -0.71}, {"time": 1}]}, "bone5": {"rotate": [{}, {"time": 0.2667, "angle": 12.53}, {"time": 0.4333, "angle": 30.55}, {"time": 0.5, "angle": 38.84}, {"time": 0.6333, "angle": 24.91}, {"time": 0.8333, "angle": 30.55}, {"time": 1}], "translate": [{}, {"time": 0.4333, "x": 3.25, "y": 3.43}, {"time": 1}]}, "bone6": {"rotate": [{}, {"time": 0.2667, "angle": -8.92}, {"time": 0.4333, "angle": 11.44}, {"time": 0.5, "angle": 19.73}, {"time": 0.6333, "angle": 5.8}, {"time": 0.8333, "angle": 11.44}, {"time": 1}]}, "bone7": {"rotate": [{}, {"time": 0.2667, "angle": -9.95}, {"time": 0.4333, "angle": 18.46}, {"time": 0.5, "angle": 26.75}, {"time": 0.6333, "angle": 12.82}, {"time": 0.8333, "angle": 18.46}, {"time": 1}]}, "bone8": {"rotate": [{}, {"time": 0.2667, "angle": -20.32}, {"time": 0.4333, "angle": 1.56}, {"time": 0.5, "angle": 7.72}, {"time": 0.6333, "angle": -2.43}, {"time": 0.8333, "angle": 1.56}, {"time": 1}]}, "bone9": {"rotate": [{}, {"time": 0.2667, "angle": 13.03}, {"time": 0.4333, "angle": -24.9}, {"time": 0.5, "angle": -18.74}, {"time": 0.6333, "angle": -28.89}, {"time": 0.8333, "angle": -24.9}, {"time": 1}]}, "bone10": {"rotate": [{}, {"time": 0.2667, "angle": 15.71}, {"time": 0.4333, "angle": 6.36}, {"time": 0.5, "angle": 12.51}, {"time": 0.6333, "angle": 2.37}, {"time": 0.8333, "angle": 6.36}, {"time": 1}]}, "bone11": {"rotate": [{}, {"time": 0.2667, "angle": 4.53}, {"time": 0.4333, "angle": -3.8, "curve": "stepped"}, {"time": 0.8333, "angle": -3.8}, {"time": 1}], "translate": [{}, {"time": 0.2667, "x": -0.68, "y": 1.15}, {"time": 0.4333, "x": 0.97, "y": -14.31, "curve": "stepped"}, {"time": 0.8333, "x": 0.97, "y": -14.31}, {"time": 1}]}, "bone12": {"rotate": [{}, {"time": 0.2667, "angle": -3.86}, {"time": 0.4333, "angle": -13.59, "curve": "stepped"}, {"time": 0.8333, "angle": -13.59}, {"time": 1}]}, "bone13": {"rotate": [{}, {"time": 0.2667, "angle": -6.38}, {"time": 0.4333, "angle": -21.21, "curve": "stepped"}, {"time": 0.8333, "angle": -21.21}, {"time": 1}]}, "bone14": {"rotate": [{}, {"time": 0.2667, "angle": 9.98}, {"time": 0.4333, "angle": 9.95, "curve": "stepped"}, {"time": 0.8333, "angle": 9.95}, {"time": 1}]}, "bone16": {"rotate": [{"angle": 28.54}], "translate": [{"x": 6.5, "y": 4.36}]}, "bone18": {"rotate": [{}, {"time": 0.4333, "angle": 48.03}], "translate": [{}, {"time": 0.4333, "x": -256.21, "y": -62.53}], "scale": [{"time": 0.4333}, {"time": 0.5667, "x": 0.452, "y": 0.452}, {"time": 0.6667}, {"time": 0.7667, "x": 0.452, "y": 0.452}, {"time": 0.8333}]}, "bone19": {"translate": [{}, {"time": 0.2667, "x": 5.66, "y": -4.11}, {"time": 0.4333, "x": -5.04, "y": 5.56}, {"time": 1}], "scale": [{}, {"time": 0.2667, "x": 0.815, "y": 0.815}, {"time": 0.4333}]}, "bone20": {"translate": [{}, {"time": 0.2667, "x": 5.66, "y": -4.11}, {"time": 0.4333, "x": -5.04, "y": 5.56}, {"time": 1}], "scale": [{}, {"time": 0.2667, "x": 0.815, "y": 0.815}, {"time": 0.4333}]}, "bone21": {"rotate": [{}, {"time": 0.4333, "angle": 91.59}], "translate": [{}, {"time": 0.4333, "x": -198.97, "y": -91.53}], "scale": [{"time": 0.4333}, {"time": 0.5667, "x": 2.187, "y": 2.187}, {"time": 0.6667}, {"time": 0.7667, "x": 2.187, "y": 2.187}, {"time": 0.8333}]}, "bone22": {"rotate": [{}, {"time": 0.4333, "angle": 14.62}, {"time": 1}], "translate": [{}, {"time": 0.4333, "x": -234.65, "y": -60.3}], "scale": [{"time": 0.4333}, {"time": 0.5667, "x": 2.187, "y": 2.187}, {"time": 0.6667}, {"time": 0.7667, "x": 2.187, "y": 2.187}, {"time": 0.8333}]}}, "events": [{"time": 0.4333, "name": "attack"}]}, "hurt": {"slots": {"effect": {"color": [{"color": "ffffff00"}, {"time": 0.3333, "color": "ffffffff"}, {"time": 0.4333, "color": "ffffff00"}], "attachment": [{"name": "effect"}]}, "effect-3": {"color": [{"time": 0.1667, "color": "ffffffff"}, {"time": 0.2, "color": "ffffff00"}], "attachment": [{"name": "effect-2"}]}, "effect-4": {"color": [{"time": 0.1667, "color": "ffffffff"}, {"time": 0.2, "color": "ffffff00"}], "attachment": [{"name": "effect-2"}]}, "eye": {"attachment": [{"name": null}]}, "eye-hurt": {"attachment": [{"name": "eye-hurt"}]}, "eye2": {"attachment": [{"name": null}]}, "screw": {"color": [{"color": "ffffff00"}, {"time": 0.1, "color": "ffffffff", "curve": "stepped"}, {"time": 0.3667, "color": "ffffffff"}, {"time": 0.4333, "color": "ffffff00"}], "attachment": [{"name": "screw"}]}}, "bones": {"bone": {"rotate": [{}, {"time": 0.2333, "angle": -3.39}, {"time": 0.5667}], "translate": [{}, {"time": 0.2333, "x": 2.54, "y": 4.23}, {"time": 0.5667}]}, "bone2": {"rotate": [{}, {"time": 0.2333, "angle": -3.63}, {"time": 0.5667}, {"time": 0.7, "angle": 2.86}, {"time": 1}]}, "bone3": {"rotate": [{}, {"time": 0.2333, "angle": -5.41}, {"time": 0.5667}, {"time": 0.7, "angle": 2.33}, {"time": 1}]}, "bone4": {"rotate": [{}, {"time": 0.2333, "angle": -4.67}, {"time": 0.5667}]}, "bone5a": {"rotate": [{}, {"time": 0.2333, "angle": -12.71}, {"time": 0.5667}, {"time": 0.7, "angle": 10.33}, {"time": 1}]}, "bone5b": {"rotate": [{}, {"time": 0.2333, "angle": -12.71}, {"time": 0.5667}, {"time": 0.7, "angle": 10.33}, {"time": 1}]}, "bone5c": {"rotate": [{}, {"time": 0.2333, "angle": -12.71}, {"time": 0.5667}, {"time": 0.7, "angle": 10.33}, {"time": 1}]}, "bone5": {"rotate": [{}, {"time": 0.2333, "angle": 17.32}, {"time": 0.5667}, {"time": 0.7, "angle": -3.12}, {"time": 1}]}, "bone6": {"rotate": [{}, {"time": 0.2333, "angle": 17.1}, {"time": 0.5667}, {"time": 0.7, "angle": -4.51}, {"time": 1}]}, "bone7": {"rotate": [{}, {"time": 0.2333, "angle": 13.25}, {"time": 0.5667}, {"time": 0.7, "angle": -1.15}, {"time": 1}]}, "bone8": {"rotate": [{}, {"time": 0.2333, "angle": -15.46}, {"time": 0.5667}, {"time": 0.7, "angle": 3.73}, {"time": 1}]}, "bone9": {"rotate": [{}, {"time": 0.2333, "angle": -19.83}, {"time": 0.5667}, {"time": 0.7, "angle": 0.46}, {"time": 1}]}, "bone10": {"rotate": [{}, {"time": 0.2333, "angle": -11.22}, {"time": 0.5667}, {"time": 0.7, "angle": 0.63}, {"time": 1}]}, "bone12": {"rotate": [{}, {"time": 0.2333, "angle": 1.64}, {"time": 0.5667}]}, "bone13": {"rotate": [{}, {"time": 0.2333, "angle": -20.55}, {"time": 0.5667}]}, "bone14": {"rotate": [{}, {"time": 0.2333, "angle": 11.77}, {"time": 0.5667}]}, "bone16": {"rotate": [{"angle": 28.54}], "translate": [{"x": 6.5, "y": 4.36}]}, "bone21": {"rotate": [{"angle": 77}], "translate": [{"x": -34.32, "y": -94.53}, {"time": 0.2, "x": -70.97, "y": -101.34}], "scale": [{}, {"time": 0.2, "x": 1.524, "y": 1.524}]}, "bone22": {"rotate": [{"angle": 6.95}], "translate": [{"x": -90.64, "y": -99.3}, {"time": 0.2, "x": -106.05, "y": -42.81}], "scale": [{}, {"time": 0.2, "x": 1.524, "y": 1.524}]}}, "path": {"fly": {"position": [{"position": 0.2614}, {"time": 0.4333, "position": 1}]}}}, "idle": {"bones": {"bone2": {"rotate": [{}, {"time": 0.5, "angle": -2.33}, {"time": 1}], "translate": [{}, {"time": 0.5, "y": 4.73}, {"time": 1}]}, "bone3": {"rotate": [{}, {"time": 0.5, "angle": -1.7}, {"time": 1}]}, "bone4": {"rotate": [{}, {"time": 0.5, "angle": -2.17}, {"time": 1}]}, "bone5a": {"rotate": [{}, {"time": 0.5, "angle": -9.51}, {"time": 1}]}, "bone5b": {"rotate": [{}, {"time": 0.5, "angle": -9.51}, {"time": 1}]}, "bone5": {"rotate": [{}, {"time": 0.5, "angle": 9.02}, {"time": 1}]}, "bone6": {"rotate": [{}, {"time": 0.5, "angle": -5.41}, {"time": 1}]}, "bone7": {"rotate": [{}, {"time": 0.5, "angle": -7.14}, {"time": 1}]}, "bone8": {"rotate": [{}, {"time": 0.5, "angle": -8.1}, {"time": 1}]}, "bone9": {"rotate": [{}, {"time": 0.5, "angle": 7.35}, {"time": 1}]}, "bone10": {"rotate": [{}, {"time": 0.5, "angle": 2.85}, {"time": 1}]}, "bone11": {"rotate": [{}, {"time": 0.5, "angle": 4.47}, {"time": 1}]}, "bone12": {"rotate": [{}, {"time": 0.5, "angle": -4.48}, {"time": 1}]}, "bone13": {"rotate": [{}, {"time": 0.5, "angle": -4.22}, {"time": 1}]}, "bone14": {"rotate": [{}, {"time": 0.5, "angle": 4.52}, {"time": 1}]}, "bone16": {"rotate": [{"angle": 28.54}], "translate": [{"x": 6.5, "y": 4.36}]}}}}}