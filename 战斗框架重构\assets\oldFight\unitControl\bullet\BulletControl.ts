import { BulletLauncher, BulletObj } from './BulletObj';
import { CharacterControl } from '../CharacterControl';
import FightCtrl from '../../../ctrl/FightCtrl';
import { CharacterRole } from '../CharacterAttributes';
import { EPrefabKey } from '../../../const/constEnum';
const { ccclass, property } = cc._decorator;

/*** 子弹控制，碰撞等信息生命周期等*/
@ccclass
export class BulletControl extends CharacterControl {
    launcher: BulletLauncher;
    bullet: BulletObj;
    checkDistance: number = 50;
    curBulletSelfWordPos: cc.Vec3 = null
    curTargetWordPos: cc.Vec3
    /**是否需要碰撞后结束 */
    isNeedCollisionStop = true
    /**是否计算旋转角度 */
    isNeedCalculateRotation = true
    /**是否计算移动位置 */
    isNeedCalculatePos = true
    /**是否造成伤害 */
    isCanHert = true
    /**旋转角度 */
    rotation = 0

    /**碰撞后的回调 */
    onCollisionEvent: () => void
    initWithLauncher(launcher: BulletLauncher, target: cc.Node = undefined) {
        this.launcher = launcher;
        this.bullet = new BulletObj();
        this.bullet.selfNode = this.node;
        this.bullet.target = target;
        this.bullet.copyFrom(this.launcher);
        this.isDead = false;
        this.isNeedCalculateRotation = true;
        this.isNeedCollisionStop = true
        this.isNeedCalculatePos = true
        this.checkDistance = 50;
        this.isCanHert = true
    }
    start(): void {
        super.start();
        this.enabledCollision = true;
    }
    protected onEnable(): void {
        this.enabledCollision = true;
    }
    onDisable(): void {
        super.onDisable();
        this.enabledCollision = false;
    }
    private isDeadBullet(bullet: BulletObj): boolean {
        return (bullet.timeElapsed >= bullet.model.lifeDuration) || bullet.model.hitTimes <= 0
    }
    private removeBullet() {
        this.isDead = true;
        this.bullet.model.onRemove?.process(this.bullet);
        FightCtrl.ins.removeBullet(this.node);
    }
    calculatePos() {
        this.curBulletSelfWordPos = this.bullet.selfNode.convertToWorldSpaceAR(cc.Vec3.ZERO)
        if (this.bullet.target) {
            if (this.bullet.target.getComponent(CharacterControl).isDead) {
                this.checkDistance = -1
                return
            }
            this.curTargetWordPos = this.bullet.target.convertToWorldSpaceAR(cc.Vec3.ZERO)
            return this.curTargetWordPos.clone().add(cc.v3(0, 20))  // 对子弹目标点做一个向上的偏移
        } else if (this.bullet.targetWordPos) {
            this.curTargetWordPos = this.bullet.targetWordPos.clone().add(cc.v3(0, 20))
        }
    }
    protected override  update(dt: number): void {
        super.update(dt);
        if (this.isDead) {
            return;
        }
        if (this.bullet.timeElapsed === 0) {
            this.bullet.model.onCreate?.process(this.bullet);
        }
        if (this.isDeadBullet(this.bullet)) {
            this.removeBullet();
            return;
        }
        //如果子弹没有目标就开始回收了
        if (this.bullet.model.isRecycleWhenNoTarget && (this.bullet.target == null || this.bullet.target.parent == null) && this.bullet.targetWordPos == null) {
            this.bullet.model.hitTimes = 0
            return
        }
        if (this.bullet.model.isCollision && this.isNeedCollisionStop) {
            this.bullet.model.hitTimes = 0
            return
        }
        // 计算移动
        if (this.isNeedCalculatePos) {
            if (this.checkDistance != -1) {
                this.calculatePos()
            }
            if (this.bullet.model.tween) {
                let moveForce = this.bullet.model.tween.process(this.bullet.timeElapsed, this.bullet, this.launcher).clone();
                moveForce.multiplyScalar(this.launcher.speed);
                this.moveAction.moveBy(moveForce)
            } else {
                this.moveAction.moveBy(cc.Vec3.UP.clone().multiplyScalar(this.launcher.speed));   // 控制子弹移动, 2D默认up是正方向
            }
        }

        if (this.curTargetWordPos) {
            //判断和目标的距离
            if (this.checkDistance != -1 && cc.Vec3.distance(this.curBulletSelfWordPos, this.curTargetWordPos) <= this.checkDistance) {
                this.enterCollision()
            }
        } else {
            //如果这个目标的y 值超过skill回收的位置
            if (this.checkDistance != -1 && (this.curBulletSelfWordPos.y - FightCtrl.ins.skillPlantWordPoint.y) <= this.checkDistance) {
                this.enterCollision()
            }
        }
        // 最后计算时间，避免有回调首次收到时，时间不是0
        this.bullet.timeElapsed += dt;
    }

    enterCollision() {
        this.onCollisionEvent?.()
        if (this.bullet.model.hitTimes > 0) {
            let sucees = this.playHit()
            // 记录撞击次数
            if (sucees) {
                this.bullet.model.hitTimes -= 1;
            }
        }
        this.bullet.model.isCollision = true
    }

    playHit(target = this.bullet.target) {
        if (target) {
            return this.bullet.model.onHit?.process(this.bullet, target);
        } else {
            return this.isNeedCollisionStop
        }
    }
}

