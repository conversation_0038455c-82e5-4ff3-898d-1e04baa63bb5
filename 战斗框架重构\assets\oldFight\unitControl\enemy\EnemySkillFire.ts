import { AudioMgr } from "../../../../../scripts/framework/manager/AudioMgr"
import { UIUtils } from "../../../../../scripts/framework/utils/UIUtils"
import { AudioId } from "../../../../../scripts/game/config/Config"
import { DataMgr } from "../../../../../scripts/game/manager/DataMgr"
import FightCtrl from "../../../ctrl/FightCtrl"
import { UtilsExtends } from "../../../util/UtilsExtends"
import { TimelineEvent, TimelineObj, TimelineNode, TimelineModel } from "../../timeline/TimelineObj"
import { BulletLauncher, BulletModel } from "../bullet/BulletObj"
import { CharacterControl } from "../CharacterControl"
import { Skill, SkillModel } from "../CharacterSkillObj"
import { BulletTweenForwardFireArrowPlayer } from "../player/bulletTween/BulletTweenForwardFireArrowPlayer"
import { SkillNames } from "../player/skills/SkillNames"
import { EnemyBulletHitCommon } from "./EnemyBulletHitCommon"



class FireBulletEvent extends TimelineEvent {
    constructor(
        public launcher: BulletLauncher,
        public isBoss: boolean
    ) {
        super()
    }
    doEvent(timelineObj: TimelineObj): void {
        let casterCharacter = timelineObj.caster.getComponent(CharacterControl)
        if (!timelineObj.target || casterCharacter.isDead) return

        this.launcher.fireWorldPosition = casterCharacter.fireNode.convertToWorldSpaceAR(cc.Vec3.ZERO)
        this.launcher.caster = timelineObj.caster;

        let bulltType = FightCtrl.ins.enemyCfg[casterCharacter.characterAttr.id - 1].bulletRes
        this.playEffect(DataMgr.getInstance().data.enemyCfg[Number(this.launcher.model.id) - 1].soundEnemy)
        if (casterCharacter.spine) {
            casterCharacter.playAttack(false, (trackEntry: sp.spine.TrackEntry, event: sp.spine.Event) => {
                if (event.data.name == 'attack' && FightCtrl.ins.isRun) {
                    if (casterCharacter.isDead) return
                    let bulletCom = FightCtrl.ins.createBullet(this.launcher, timelineObj.target, `monster/${bulltType}`);
                    bulletCom.rotation = 180
                    // if (!timelineObj.target) {
                    //     bulletCom.bullet.targetWordPos = targetWordPoint
                    // }
                    // bulletCom.bullet.model.isRecycleWhenNoTarget = true
                }
            }, true).then(() => {
                casterCharacter.playIdle(true)
            })
        } else {
            let bulletCom = FightCtrl.ins.createBullet(this.launcher, timelineObj.target, `monster/${bulltType}`);
            bulletCom.rotation = 180
        }
    }
}

export class EnemySkillFire extends Skill {
    constructor(
        public prefabKey: string,
        public bulletId: string,
        public isBoss: boolean
    ) {
        super(SkillNames.enemyAttack)
        const launcher = new BulletLauncher();
        launcher.caster = null;
        launcher.speed = 10
        launcher.model = new BulletModel(this.bulletId, this.prefabKey);
        launcher.model.tween = new BulletTweenForwardFireArrowPlayer();
        launcher.model.lifeDuration = 5;
        launcher.model.onHit = new EnemyBulletHitCommon();
        this.skillMode = {
            name: this.name,
            effect: {
                id: UtilsExtends.generateUniqueId(),
                name: this.name,
                lifeDuration: 0.1,
                nodes: [
                    {
                        timeStartPoint: 0.01,
                        event: new FireBulletEvent(launcher, this.isBoss)
                    } as TimelineNode,
                    //一次技能连发效果
                    // {
                    //     timeStartPoint: 0.2,
                    //     event: new FireBulletEvent(this.createBulletLauncher())
                    // } as TimelineNode,
                ]
            } as TimelineModel,
            buffs: [],
            coldTime: 1,
        }
    }
}