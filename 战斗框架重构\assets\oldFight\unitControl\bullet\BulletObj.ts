import { UtilsExtends } from '../../../util/UtilsExtends';
const { ccclass, property } = cc._decorator;

/** 子弹，对象过程中调整 */
export class BulletObj {
    /**子弹的负责人是谁，可以是null */
    public caster: cc.Node;
    /**要添加给谁，追踪弹就需要有 */
    public target: cc.Node;
    /**子弹目标点, 如果有目标点位就不去计算target的目标点了 */
    public targetWordPos?: cc.Vec3;
    /**bullet本身，如果是追踪弹就需要知道自己的位置 */
    public selfNode?: cc.Node;
    /**开火点 */
    public firePoint: cc.Vec3;
    /**描述这是个什么，这个值在运行过程中会修改 */
    public model: BulletModel;
    /**已经存在了多少时间了，单位：秒 */
    public timeElapsed: number = 0;
    copyFrom(launcher: BulletLauncher) {
        this.caster = launcher.caster;
        this.model = UtilsExtends.deepClone(launcher.model);
    }
}

export abstract class BulletOnCreate {
    abstract process(Obj: BulletObj): void;
}
export abstract class BulletOnHit {
    /**返回false即代表自己处理碰撞次数来管理子弹，需要注意，如果自己管理，需要注意这个函数会一直调用，直到销毁 */
    abstract process(Obj: BulletObj, target: cc.Node): boolean;
}
export abstract class BulletOnRemoved {
    abstract process(Obj: BulletObj): void;
}

/**
 * 子弹数据，一般为配置文件，只读（一个mode只对应一个）
 */
export class BulletModel {
    /**Timeline一共多长时间（到时间了就丢掉了），单位秒 */
    lifeDuration: number = 5;
    /**子弹可以碰触的次数，每次碰到合理目标-1，到0的时候子弹就结束了。如果可以穿透则，将这个值增加 */
    hitTimes: number = 1;
    /**子弹移动轨迹，如果是追踪弹，则通过这个实现 */
    tween?: BulletTween;
    /**子弹在被添加时候触发的事件 */
    onCreate?: BulletOnCreate;
    /**子弹命中目标的时候触发的事件 */
    onHit?: BulletOnHit;
    /**子弹移除事件 */
    onRemove?: BulletOnRemoved;
    /**是否命中碰撞了 */
    isCollision = false
    /**是否没有目标就回收，默认关闭 */
    isRecycleWhenNoTarget = false;
    constructor(
        public id: string,
        /**子弹需要用的prefab key */
        public bulletKey: string
    ) { }
    clone(): BulletModel {
        let cloneBulletModel = new BulletModel(this.id, this.bulletKey);
        cloneBulletModel.lifeDuration = this.lifeDuration;
        cloneBulletModel.hitTimes = this.hitTimes;
        cloneBulletModel.tween = this.tween;
        cloneBulletModel.onCreate = this.onCreate;
        cloneBulletModel.onHit = this.onHit;
        cloneBulletModel.onRemove = this.onRemove;
        return cloneBulletModel;
    }
}
/** 子弹发射器 */
export class BulletLauncher {
    /**子弹的负责人是谁，可以是null */
    caster: cc.Node;
    /**子弹数据 */
    model: BulletModel;
    /**发射的坐标 */
    fireWorldPosition = cc.Vec3.ZERO;
    /**发射的归一化向量 */
    fireNormalizedVector = cc.Vec3.ZERO;
    /**发射的角度，单位：角度 */
    fireDegree: number = 0;
    /**子弹的初速度，单位：米/秒 */
    speed: number = 0
    /**子弹的生命周期，单位：秒 */
    duration: number = 5;
}
/**
 * 子弹移动轨迹
 * t: 子弹飞行了多久的时间点，单位秒。
 * return  世界坐标
 */
export abstract class BulletTween {
    // 返回这一时间点上的速度和偏移
    abstract process(t: number, Obj: BulletObj, lunch: BulletLauncher): cc.Vec3
}
