[{"__type__": "cc.Prefab", "_name": "Card", "_objFlags": 0, "_native": "", "data": {"__id__": 1}, "optimizationPolicy": 0, "asyncLoadAssets": false, "readonly": false, "persistent": false}, {"__type__": "cc.Node", "_name": "Card", "_objFlags": 0, "_parent": null, "_children": [{"__id__": 2}, {"__id__": 10}, {"__id__": 18}, {"__id__": 28}, {"__id__": 36}, {"__id__": 44}, {"__id__": 50}], "_active": true, "_components": [{"__id__": 58}, {"__id__": 60}, {"__id__": 62}], "_prefab": {"__id__": 64}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_id": "", "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "_cardFace", "_objFlags": 512, "_parent": {"__id__": 1}, "_children": [], "_active": true, "_components": [{"__id__": 3}, {"__id__": 5}, {"__id__": 7}], "_prefab": {"__id__": 9}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_id": "", "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 2}, "_enabled": true, "__prefab": {"__id__": 4}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8"}, "_visFlags": 0, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "5aecf0be-7e8f-4c72-8d49-2236a8c980f8@f9941"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "8aMxDwbHFLoq3fXt0vGbDf"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "node": {"__id__": 2}, "_enabled": true, "__prefab": {"__id__": 6}, "_opacity": 255}, {"__type__": "cc.CompPrefabInfo", "fileId": "d81+io0+BHfJnQ87EDffDJ"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 2}, "_enabled": true, "__prefab": {"__id__": 8}, "_priority": 0, "_contentSize": {"__type__": "cc.Size", "width": 366, "height": 501}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}}, {"__type__": "cc.CompPrefabInfo", "fileId": "e8HNTk47ZJhpbxbXOsls3w"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "d9ESnhlftL3Iu9wjlP3f/Q"}, {"__type__": "cc.Node", "_name": "_breakHp", "_objFlags": 512, "_parent": {"__id__": 1}, "_children": [], "_active": false, "_components": [{"__id__": 11}, {"__id__": 13}, {"__id__": 15}], "_prefab": {"__id__": 17}, "_lpos": {"__type__": "cc.Vec3", "x": -20.147, "y": -250.729, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_id": "", "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "sp.Skeleton", "_name": "", "_objFlags": 0, "node": {"__id__": 10}, "_enabled": true, "__prefab": {"__id__": 12}, "_visFlags": 0, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "loop": false, "timeScale": 1, "_useTint": false, "_preCacheMode": 0, "_cacheMode": 0, "_defaultCacheMode": 0, "_debugBones": false, "_debugSlots": false, "_skeletonData": {"__uuid__": "b5cb330e-7a4d-4073-9f6f-38db5e7d7d77"}, "defaultSkin": "default", "defaultAnimation": "animation", "_sockets": [], "_debugMesh": false, "_materials": [{"__uuid__": "7afd064b-113f-480e-b793-8817d19f63c3"}], "paused": false, "premultipliedAlpha": true, "_accTime": 0, "_playCount": 0, "_animationName": "animation", "_animationQueue": [], "_playTimes": 0, "_isAniComplete": true, "__defaultCacheMode": 0, "_enableBatch": false, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "c15YLs09lBaqKvmO4iIvPK"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "node": {"__id__": 10}, "_enabled": true, "__prefab": {"__id__": 14}, "_opacity": 255}, {"__type__": "cc.CompPrefabInfo", "fileId": "5fHY/TbNVJJZFpg5ofR3KR"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 10}, "_enabled": true, "__prefab": {"__id__": 16}, "_priority": 0, "_contentSize": {"__type__": "cc.Size", "width": 367, "height": 503}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}}, {"__type__": "cc.CompPrefabInfo", "fileId": "8eol/pChlC57yLS60Bcgrd"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "6awROf/JNEP6Ca36T3LBMP"}, {"__type__": "cc.Node", "_name": "_cardValue", "_objFlags": 512, "_parent": {"__id__": 1}, "_children": [], "_active": true, "_components": [{"__id__": 19}, {"__id__": 21}, {"__id__": 23}, {"__id__": 25}], "_prefab": {"__id__": 27}, "_lpos": {"__type__": "cc.Vec3", "x": 64.726, "y": 188.328, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1.543}, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_id": "", "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 18}, "_enabled": true, "__prefab": {"__id__": 20}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8"}, "_visFlags": 0, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_useOriginalSize": true, "_string": "666", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 0, "_fontSize": 60, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 60, "_overflow": 0, "_enableWrapText": true, "_font": {"__uuid__": "6c499e91-b85d-4647-8f0e-0f22d10a798c"}, "_isSystemFontUsed": false, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 0, "_cacheMode": 0, "_spacingX": 0, "_batchAsBitmap": false, "_styleFlags": 1, "_id": "", "_enableOutline": true, "_outlineWidth": 3, "_outlineColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}}, {"__type__": "cc.CompPrefabInfo", "fileId": "3cPJbQqM1CFoC/viSq7qZV"}, {"__type__": "cc.LabelOutline", "_name": "", "_objFlags": 0, "node": {"__id__": 18}, "_enabled": true, "__prefab": {"__id__": 22}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "cc/BFxKclMJY3fJlbM6Yhm"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "node": {"__id__": 18}, "_enabled": true, "__prefab": {"__id__": 24}, "_opacity": 255}, {"__type__": "cc.CompPrefabInfo", "fileId": "baWiXn6WpJar5ZQ5AThHtY"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 18}, "_enabled": true, "__prefab": {"__id__": 26}, "_priority": 0, "_contentSize": {"__type__": "cc.Size", "width": 94.15, "height": 81.6}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}}, {"__type__": "cc.CompPrefabInfo", "fileId": "81t834hI1LSLLLPKtvuX8W"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "bfNIPqynpHGYIB7c1i4+O+"}, {"__type__": "cc.Node", "_name": "_use", "_objFlags": 512, "_parent": {"__id__": 1}, "_children": [], "_active": true, "_components": [{"__id__": 29}, {"__id__": 31}, {"__id__": 33}], "_prefab": {"__id__": 35}, "_lpos": {"__type__": "cc.Vec3", "x": 11.987, "y": 318.532, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_id": "", "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 28}, "_enabled": true, "__prefab": {"__id__": 30}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8"}, "_visFlags": 0, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "abed2575-3d36-4dbe-ad78-43f72510a17e@f9941"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "a4ck9ydhVBFIYFs/ELjyId"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "node": {"__id__": 28}, "_enabled": true, "__prefab": {"__id__": 32}, "_opacity": 255}, {"__type__": "cc.CompPrefabInfo", "fileId": "98sJECh4dNN60HBA/GEzef"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 28}, "_enabled": true, "__prefab": {"__id__": 34}, "_priority": 0, "_contentSize": {"__type__": "cc.Size", "width": 222, "height": 167}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}}, {"__type__": "cc.CompPrefabInfo", "fileId": "8fMtOEsFNK0KG/NIUHTQvX"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "9eqjgv6SJB7Z8r9/wH5ByW"}, {"__type__": "cc.Node", "_name": "_bomb", "_objFlags": 0, "_parent": {"__id__": 1}, "_children": [], "_active": false, "_components": [{"__id__": 37}, {"__id__": 39}, {"__id__": 41}], "_prefab": {"__id__": 43}, "_lpos": {"__type__": "cc.Vec3", "x": 95.975, "y": -27.333, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0.7117870345471062, "w": 0.7023953427028375}, "_lscale": {"__type__": "cc.Vec3", "x": 2.16, "y": 2.16, "z": 2.16}, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 90.761}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_id": "", "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "sp.Skeleton", "_name": "", "_objFlags": 0, "node": {"__id__": 36}, "_enabled": true, "__prefab": {"__id__": 38}, "_visFlags": 0, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "loop": false, "timeScale": 1, "_useTint": false, "_preCacheMode": 0, "_cacheMode": 0, "_defaultCacheMode": 0, "_debugBones": false, "_debugSlots": false, "_skeletonData": {"__uuid__": "11a94671-67d1-4a13-b2ea-7f5bfe6cc9d3"}, "defaultSkin": "boom2", "defaultAnimation": "49ignite", "_sockets": [], "_debugMesh": false, "_materials": [{"__uuid__": "7afd064b-113f-480e-b793-8817d19f63c3"}], "paused": false, "premultipliedAlpha": false, "_accTime": 0, "_playCount": 0, "_animationName": "49ignite", "_animationQueue": [], "_playTimes": 1, "_isAniComplete": true, "__defaultCacheMode": 0, "_enableBatch": false, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "58vVnRF9tEjYqE9ey8WDNT"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "node": {"__id__": 36}, "_enabled": true, "__prefab": {"__id__": 40}, "_opacity": 255}, {"__type__": "cc.CompPrefabInfo", "fileId": "e4MUtwXZBGzpEdkEKtI1aX"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 36}, "_enabled": true, "__prefab": {"__id__": 42}, "_priority": 0, "_contentSize": {"__type__": "cc.Size", "width": 84.96, "height": 181.92}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}}, {"__type__": "cc.CompPrefabInfo", "fileId": "a0XdhJDQBJY7NzTEtrPg35"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "01U+sNiI9E1YSJd20lmoSM"}, {"__type__": "cc.Node", "_name": "_bombLbPoint", "_objFlags": 0, "_parent": {"__id__": 1}, "_children": [], "_active": true, "_components": [{"__id__": 45}, {"__id__": 47}], "_prefab": {"__id__": 49}, "_lpos": {"__type__": "cc.Vec3", "x": -1.562, "y": -107.643, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1.543}, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_id": "", "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "node": {"__id__": 44}, "_enabled": true, "__prefab": {"__id__": 46}, "_opacity": 255}, {"__type__": "cc.CompPrefabInfo", "fileId": "920gtHUTFHKrjbOZNdA2ed"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 44}, "_enabled": true, "__prefab": {"__id__": 48}, "_priority": 0, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 0}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}}, {"__type__": "cc.CompPrefabInfo", "fileId": "7e/HDF5+9Pz4RpoaxCm4Tp"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "7aWt5dXylDw4+EyYE/SE5S"}, {"__type__": "cc.Node", "_name": "_slime", "_objFlags": 0, "_parent": {"__id__": 1}, "_children": [], "_active": false, "_components": [{"__id__": 51}, {"__id__": 53}, {"__id__": 55}], "_prefab": {"__id__": 57}, "_lpos": {"__type__": "cc.Vec3", "x": -26.212, "y": -86.792, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_id": "", "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "sp.Skeleton", "_name": "", "_objFlags": 0, "node": {"__id__": 50}, "_enabled": true, "__prefab": {"__id__": 52}, "_visFlags": 0, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "loop": true, "timeScale": 1, "_useTint": false, "_preCacheMode": 0, "_cacheMode": 0, "_defaultCacheMode": 0, "_debugBones": false, "_debugSlots": false, "_skeletonData": {"__uuid__": "e103911c-24aa-44f8-9270-2b42b43b3cf3"}, "defaultSkin": "default", "defaultAnimation": "idle", "_sockets": [], "_debugMesh": false, "_materials": [{"__uuid__": "7afd064b-113f-480e-b793-8817d19f63c3"}], "paused": false, "premultipliedAlpha": false, "_accTime": 0, "_playCount": 0, "_animationName": "idle", "_animationQueue": [], "_playTimes": 0, "_isAniComplete": true, "__defaultCacheMode": 0, "_enableBatch": false, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "0d8pUW39ZEJogfJ5+bqX7c"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "node": {"__id__": 50}, "_enabled": true, "__prefab": {"__id__": 54}, "_opacity": 255}, {"__type__": "cc.CompPrefabInfo", "fileId": "72gK1bSENJgLjF0pmEqLBM"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 50}, "_enabled": true, "__prefab": {"__id__": 56}, "_priority": 0, "_contentSize": {"__type__": "cc.Size", "width": 319.35, "height": 207}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}}, {"__type__": "cc.CompPrefabInfo", "fileId": "3eZ1KIDwZEPpBZhc/pPucM"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "c7ji9wbl1NYJkJ1iYVZPm/"}, {"__type__": "cc.MotionStreak", "_name": "", "_objFlags": 0, "node": {"__id__": 1}, "_enabled": false, "__prefab": {"__id__": 59}, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_visFlags": 0, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 184}, "_preview": false, "_fadeTime": 0.5, "_minSeg": 1, "_stroke": 50, "_texture": {"__uuid__": "ab4771b5-3f27-4260-9452-6ea9bee85cdd@6c48a"}, "_fastMode": true, "_N$preview": true, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "7cEBQPSqpNzoNSTr/wApL2"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "node": {"__id__": 1}, "_enabled": true, "__prefab": {"__id__": 61}, "_opacity": 255}, {"__type__": "cc.CompPrefabInfo", "fileId": "40KngupCVNo4gqdKEqd34B"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 1}, "_enabled": true, "__prefab": {"__id__": 63}, "_priority": 0, "_contentSize": {"__type__": "cc.Size", "width": 200, "height": 300}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}}, {"__type__": "cc.CompPrefabInfo", "fileId": "d4BjnXh95CersJAXNo72nT"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "7auBX4yJRI4KKBm3dzhRre"}]