
// import BuffManager from "../BuffManager";
// import { ECamp } from "../CampManager";
// import SkillManager from "../SkillManager";
// import Base from "./Base";

// /**buff类型 */
// export enum EBuffType {
//     None = `None`,
// }
// /**卡片的状态 */
// export enum EquipmentStatus {
//     /**正常 */
//     Normal = `Normal`,
//     /**损坏 */
//     Broken = `Broken`,
//     /**已装备 */
//     Equiped = `Equiped`,
//     /**无法使用 */
//     Unable = `Unable`,
// }

// declare global {
//     type IEquipmentName = 'Equipment'
//     export interface IEquipmentData {
//         equipmentName: string;
//         /**耐久 */
//         durable: number
//         /**提供的防御力 */
//         defence: number
//         /**提供的攻击力 */
//         attack: number
//         status: EquipmentStatus
//         camp: ECamp
//         orginValue: number
//     }
//     // interface IMainUIData {

//     // }
// }

// /** 
//  * @features : 游戏装备
//  * @description : 针对游戏技能的Buff基类， 所有技能buff都将继承该类 ，并且该类及子类不可挂载到场景中
//  * @Date : 2020-08-12 23:28:43
//  * <AUTHOR> judu233(<EMAIL>)
//  * @LastEditTime : 2023-11-14 14:01:01
//  * @LastEditors : judu233
//  */
// export default class Equipment extends Base implements IEquipmentData {
//     /**技能数据 */
//     data: IEquipmentData;

//     /**装备技能管理 */
//     skillMgr = new SkillManager
//     /**对装备使用buff管理 */
//     buffMgr = new BuffManager

//     /**buff的Id */
//     @Base.ViewLinked
//     id: string

//     /**buff的类型 */
//     @Base.ViewLinked
//     type: string

//     /**耐久 */
//     @Base.ViewLinked
//     durable: number

//     @Base.ViewLinked
//     defence: number
//     @Base.ViewLinked
//     attack: number

//     @Base.ViewLinked
//     status: EquipmentStatus

//     @Base.ViewLinked
//     equipmentName: string

//     @Base.ViewLinked
//     camp: ECamp

//     @Base.ViewLinked
//     orginValue: number

//     init() {
//         this.status = EquipmentStatus.Normal
//     }

//     durableChangeView(newValue: number) {
//         if (newValue <= 0) {
//             this.status = EquipmentStatus.Broken
//         }
//     }

//     /**使用buff ` 子类负责实现具体逻辑*/
//     useBuff() { }
//     /**移除Buff 子类负责实现具体逻辑*/
//     removeBuff() { }
//     /**叠加一层buff 子类负责实现具体逻辑*/
//     overlayBuff() { }
//     /**减少一层buff */
//     reduceBuff() { }
// }
