import { CharacterSkillObj } from './CharacterSkillObj';
import { TimelineObj } from '../timeline/TimelineObj';
import { DamageInfo } from './damage/DamageInfo';
import { UtilsExtends } from '../../util/UtilsExtends';
const { ccclass, property } = cc._decorator;

/**  游戏中运行的、角色身上存在的buff  */
export class CharacteBuffObj {
    /**  buff的负责人是谁，可以是null  */
    caster: cc.Node;
    /**  buff要添加给谁，这个必须有  */
    target: cc.Node;
    /**  这是个什么buff  */
    model: BuffModel;
    /**  buff已经存在了多少时间了，单位：秒  */
    timeElapsed: number = 0;
    copyForm(info: AddBuffInfo) {
        this.caster = info.caster;
        this.target = info.target;
        // 需要拷贝，因为这个值会修改，原始值不会改
        this.model = UtilsExtends.deepClone(info.model);
    }
}
export abstract class BuffOnOccur {
    abstract process: (buffObj: CharacteBuffObj) => void;
}
export abstract class BuffOnTick {
    abstract process: (buffObj: CharacteBuffObj) => void;
}
export abstract class BuffOnRemoved {
    abstract process: (buffObj: CharacteBuffObj) => void;
}
export abstract class BuffOnCast {
    abstract process: (buffObj: CharacteBuffObj, skillObj: CharacterSkillObj, timelineObj: TimelineObj) => TimelineObj;
}
/** 在伤害流程中，持有这个buff的人作为攻击者会发生的事情  */
export abstract class BuffOnHit {
    /** target 挨打的角色对象  */
    abstract process(buff: CharacteBuffObj, damageInfo: DamageInfo, target: cc.Node): DamageInfo;
}
/** 在伤害流程中，持有这个buff的人, 被打了会如何处理，减少伤害，还是如何  */
export abstract class BuffOnBeHurt {
    /** attacker 打我的的角色对象  */
    abstract process(buff: CharacteBuffObj, damageInfo: DamageInfo, attacker: cc.Node): DamageInfo;
}

/*** 一个buffmode只对应一个buff*/
export interface BuffModel {
    name: string;
    /**Timeline一共多长时间（到时间了就丢掉了），单位秒 */
    lifeDuration: number;
    /**buff在被添加时候触发的事件 */
    onOccur?: BuffOnOccur;
    /** buff在每个工作周期会执行的函数，如果这个函数为空，或者tickTime<=0，都不会发生周期性工作 */
    onTick?: BuffOnTick;
    /**移除时触发 */
    onRemoved?: BuffOnRemoved;
    /**在释放技能的时候运行的buff，执行这个buff获得最终技能要产生的Timeline */
    onCast?: BuffOnCast;
    /**出现攻击的时候会调用，为攻击增加buff效果 */
    onHit?: BuffOnHit;
    /** 当收到伤害 */
    onBeHurt?: BuffOnBeHurt;
}
/** 用于添加一条buff的信息 */
export interface AddBuffInfo {
    /** buff的负责人是谁，可以是null  */
    caster: cc.Node;
    /**  buff要添加给谁，这个必须有  */
    target: cc.Node;
    /** buff的数据 */
    model: BuffModel;
}

// elegate void BuffOnOccur(BuffObj buff, int modifyStack);
//  delegate void BuffOnRemoved(BuffObj buff);
//  delegate void BuffOnTick(BuffObj buff);
//  delegate void BuffOnHit(BuffObj buff, ref DamageInfo damageInfo, GameObject target);
//  delegate void BuffOnBeHurt(BuffObj buff, ref DamageInfo damageInfo, GameObject attacker);
//  delegate void BuffOnKill(BuffObj buff, DamageInfo damageInfo, GameObject target);
//  delegate void BuffOnBeKilled(BuffObj buff, DamageInfo damageInfo, GameObject attacker);
//  delegate TimelineObj BuffOnCast(BuffObj buff, SkillObj skill, TimelineObj timeline);