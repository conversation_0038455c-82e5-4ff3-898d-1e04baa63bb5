/**
 * 战斗系统核心模块导出
 * 提供统一的入口点来访问所有核心功能
 */

// 接口导出
export * from "./interfaces/ICharacter";
export * from "./interfaces/ICharacterAttributes";
export * from "./interfaces/ISkill";
export * from "./interfaces/IBuff";
export * from "./interfaces/IDamage";

// 类型导出
export * from "./types/CharacterTypes";

// 基类导出
export * from "./base/BaseCharacter";
export * from "./base/CharacterAttributes";

// 管理器导出
export * from "./managers/EventManager";
export * from "./managers/SkillManager";
export * from "./managers/BuffManager";

// 常用枚举和常量
export {
    CharacterRole,
    CharacterState,
    CharacterSelectTag
} from "./types/CharacterTypes";

export {
    SkillType,
    SkillTargetType,
    SkillEffectType
} from "./interfaces/ISkill";

export {
    BuffType,
    BuffModifierType,
    BuffPeriodicEffectType
} from "./interfaces/IBuff";

export {
    DamageType,
    DamageTag
} from "./interfaces/IDamage";

export {
    AttributeModifierType
} from "./interfaces/ICharacterAttributes";
