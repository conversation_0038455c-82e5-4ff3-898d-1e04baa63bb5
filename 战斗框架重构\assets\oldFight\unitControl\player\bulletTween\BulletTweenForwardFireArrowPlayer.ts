import { BulletControl } from '../../bullet/BulletControl';
import { <PERSON>etLaunch<PERSON>, BulletObj, BulletTween } from '../../bullet/BulletObj';

/**
 * 直线弹
 * 效果： 沿着发射时候的方向 
 */
export class BulletTweenForwardFireArrowPlayer extends BulletTween {
    /** 每帧方向分量，控制方向和位移 */
    private v = cc.Vec3.RIGHT

    process(t: number, obj: BulletObj, lunch: BulletLauncher): cc.Vec3 {
        if (t === 0) {
            this.v = lunch.fireNormalizedVector ?? cc.Vec3.RIGHT
            // if (obj.selfNode && obj.caster && obj.caster.parent) {
            //     obj.selfNode.angle = Math.atan2(lunch.fireNormalizedVector.y, lunch.fireNormalizedVector.x) * (180 / Math.PI)
            // }
        }
        let bullet = obj.selfNode.getComponent(BulletControl)
        if (obj.model.isCollision && bullet.isNeedCollisionStop) {
            return this.v
        }
        if (!bullet.curTargetWordPos) {
            return this.v
        }
        this.v = bullet.curTargetWordPos.clone().subtract(bullet.curBulletSelfWordPos).normalize();
        if (obj.caster?.parent && bullet.isNeedCalculateRotation) {
            obj.selfNode.angle = Math.atan2(this.v.y, this.v.x) * (180 / Math.PI) + bullet.rotation
        }
        return this.v;
    }
}
