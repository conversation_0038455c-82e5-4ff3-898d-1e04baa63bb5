import { AttackAction } from '../unitAction/AttackAction';
import { CharacterAttributes, CharacterResource, CharacterRole } from './CharacterAttributes';
import { CharacterSkillObj, SkillModel } from './CharacterSkillObj';
import { AddBuffInfo, CharacteBuffObj } from './CharacteBuffObj';
import { TimelineObj } from '../timeline/TimelineObj';
import { MoveAction } from '../unitAction/MoveAction';
import FightCtrl from '../../ctrl/FightCtrl';
import AnimValueProgressHP from '../../util/animValue/AnimValueProgressHP';
import BaseCtrl from '../../../../scripts/framework/base/BaseCtrl';
import { AudioId, EventName } from '../../../../scripts/game/config/Config';
import { EFightState } from '../../const/constEnum';
import { AudioMgr } from '../../../../scripts/framework/manager/AudioMgr';
const { ccclass, property } = cc._decorator;

declare global {
    type CharacterControlOnRemove = () => void;
}
export enum EEnemySelectTag {
    /**没有选中 */
    none,
    /**玩家选中 */
    player,
    /**宠物选中 */
    partners,
}

/**
 * 角色控制
 * 普攻、技能
 * 角色信息
 */
@ccclass
export class CharacterControl extends BaseCtrl {
    // 默认支持的动作
    protected attackAction: AttackAction;
    protected moveAction: MoveAction;
    /** 角色信息 */
    characterAttr = new CharacterAttributes();
    /** 技能  */
    skills = new Array<CharacterSkillObj>();
    /** buff */
    buffs = new Array<CharacteBuffObj>();
    /** 阵营 */
    role = CharacterRole.INVALID;
    /**角色是否死亡 */
    isDead = false;
    /**角色名字 */
    roleName = ""
    /**攻击技能名字 */
    attackSkillName = "";
    /**实例化prefabKey */
    prefabKey: string;

    /**被选中Tag */
    selectTag = EEnemySelectTag.none
    /**血条 */
    progressAnimValue: AnimValueProgressHP
    /**角色骨骼 */
    spine: sp.Skeleton

    /**开火点 */
    fireNode: cc.Node
    /**原始开火点-保存 */
    firePos: cc.Vec3
    /**相对于开火点的基础点， 用于计算发射角度的 */
    fireBaseNode: cc.Node;

    /** 生命周期 */
    /**每个角色被移除时触发 */
    onRemove: CharacterControlOnRemove;
    /**收到攻击时触发，一般是闪白 */
    onHitEvent: () => void

    start(): void {
        this.attackAction = this.node.addComponent(AttackAction);
        this.moveAction = this.node.addComponent(MoveAction);
    }
    protected onDisable(): void {

    }
    protected update(dt: number): void {
        // 处理技能冷却
        this.skills.forEach(skill => {
            // 处理技能冷却, 冷却计数
            if (skill.coldTimeElapsed < skill.model.coldTime) {
                skill.coldTimeElapsed += dt;
            }
        })
        // 处理身上的buff
        let index = 0;
        while (this.buffs.length > index) {
            const buffItem = this.buffs[index];
            buffItem.timeElapsed += dt;
            // 每帧触发时机
            buffItem.model?.onTick?.process(buffItem);
            // 检查存活时间
            if (buffItem.timeElapsed >= buffItem.model.lifeDuration) {
                buffItem.model?.onRemoved?.process(buffItem);
                this.buffs.splice(index, 1);
            } else {
                index++;
            }
        }
    }
    /**启用碰撞监听 */
    set enabledCollision(value: boolean) { this.node.getComponent(cc.Collider).enabled = value; }
    /**当碰撞产生的时候调用 */
    // onCollisionEnter(collider1: cc.Collider, collider2: cc.Collider) {
    //     // console.log('on collision enter');
    //     if (this.role == CharacterRole.BULLET) {
    //         if (collider1.getComponent(CharacterControl).role == this.role) {
    //             this.collisionEnterCall(collider1, collider2, collider2)
    //         } else if (collider2.getComponent(CharacterControl).role == this.role) {
    //             this.collisionEnterCall(collider2, collider1, collider1)
    //         }
    //     }
    // }
    // collisionEnterCall(selfCollider: cc.Collider, otherCollider: cc.Collider, target: cc.Collider) { }
    // /**当碰撞产生后，碰撞结束前的情况下，每次计算碰撞结果后调用 */
    // onCollisionStay(other, self) { }
    // /**当碰撞结束后调用*/
    // onCollisionExit(other, self) { }

    /** 移动 */
    moveWithDir(dir?: cc.Vec3): boolean {
        if (!this.moveAction) {
            return false;
        }
        this.moveAction.moveBy(dir.clone().multiplyScalar(this.characterAttr.moveSpeed));
        return true;
    }
    /*** 学习普通攻击，也是一种技能，内部会调用learnSkill*/
    learnAttack(skillModel: SkillModel) {
        this.attackSkillName = skillModel.name;
        this.learnSkill(skillModel);
    }
    /** * 学习了技能才可以使用，普通攻击也是技能 */
    learnSkill(skillModel: SkillModel) {
        this.skills.push(new CharacterSkillObj(skillModel));
        if (skillModel.buffs != null) {
            for (let i = 0; i < skillModel.buffs.length; i++) {
                this.addBuff(skillModel.buffs[i]);
            }
        }
    }
    /** 物理攻击 */
    attack(char?: CharacterControl): boolean {
        return this.castSkill(this.attackSkillName, char?.node);
    }
    /** 强制攻击目标一下 */
    forceAttackTo(char: CharacterControl): boolean {
        if (!char) {
            // 如果目标为空，则无效
            console.error('forceAttackTo but target is null');
            return false;
        }
        return this.castSkill(this.attackSkillName, char.node);
    }
    // 普通攻击也是一种技能 ，target: 可以指定攻击者
    castSkill(skillName: string, target: cc.Node = undefined): boolean {
        const skillObj = this.getSkillById(skillName);
        if (skillObj == null) {
            console.log(this.roleName, 'not learn the skill', skillName);
            return false;
        }
        // 检查是否可以放技能
        if (!skillObj.canUse()) {
            return false;
        }
        // 放技能 重置冷却
        skillObj.coldTimeElapsed = 0;
        let timeline = new TimelineObj(skillObj.model.effect, this.node, target);
        for (let i = 0; i < this.buffs.length; i++) {
            if (this.buffs[i].model.onCast != null) {
                timeline = this.buffs[i].model?.onCast.process(this.buffs[i], skillObj, timeline);
            }
        }
        if (timeline != null) {
            FightCtrl.ins.timelineManager.addTimeline(timeline);
        } else {
            return false;
        }
        // 并执行所有buff
        return true;
    }
    /**groupSkill */
    castMoreTargetsSkill(skillName: string, targets: cc.Node[] = undefined): boolean {
        const skillObj = this.getSkillById(skillName);
        if (skillObj == null) {
            console.log(this.roleName, 'not learn the skill', skillName);
            return false;
        }
        // 检查是否可以放技能
        if (!skillObj.canUse()) {
            return false;
        }
        skillObj.model.onAdd?.(skillObj, targets)
        // 放技能 重置冷却
        skillObj.coldTimeElapsed = 0;
        let timeline = new TimelineObj(skillObj.model.effect, this.node, null, targets);
        for (let i = 0; i < this.buffs.length; i++) {
            if (this.buffs[i].model.onCast != null) {
                timeline = this.buffs[i].model?.onCast.process(this.buffs[i], skillObj, timeline);
            }
        }
        if (timeline != null) {
            FightCtrl.ins.timelineManager.addTimeline(timeline);
        } else {
            return false;
        }
        // 并执行所有buff
        return true;
    }
    /** 根据id获得角色学会的技能（skillObj），如果没有则返回null */
    getSkillById(skillNameId: string): CharacterSkillObj {
        const res = this.skills.filter(e => e.model.name === skillNameId);
        return res.length > 0 ? res[0] : null;
    }

    /**为角色添加buff，当然，删除也是走这个的 */
    addBuff(buffInfo: AddBuffInfo) {
        const bCaster = new Array<cc.Node>();
        if (buffInfo.caster) bCaster.push(buffInfo.caster);

        const hasOnes = this.getBuffById(buffInfo.model.name, bCaster);
        let toRemove = false;
        let toAddBuff = null;
        if (hasOnes.length > 0) {
            // 已经存在
            // TODO: 支持buff叠加或者更新
        } else {
            //新建
            toAddBuff = new CharacteBuffObj();
            toAddBuff.copyForm(buffInfo)
            this.buffs.push(toAddBuff);
        }
        if (toRemove == false) {
            toAddBuff.model.onOccur?.process(toAddBuff);
        }
    }
    /**获取角色身上对应的buffObj */
    getBuffById(id: string, caster: Array<cc.Node> = null): Array<CharacteBuffObj> {
        const res = new Array<CharacteBuffObj>();
        for (let i = 0; i < this.buffs.length; i++) {
            if (this.buffs[i].model.name == id) {
                res.push(this.buffs[i]);
            }
        }
        return res;
    }
    /**改变角色生命状态 */
    modResource(resource: CharacterResource) {
        if (resource.hp == 0) {
            debugger
            return;
        }
        let addV = this.characterAttr.hp + resource.hp
        if (addV <= this.characterAttr.hpUp) {
            this.characterAttr.hp = addV
            this.progressAnimValue?.setValue(this.characterAttr.hp / this.characterAttr.hpUp, true)
        }
        if (this.characterAttr.hp <= 0 && !this.isDead) {
            this.kill()
        } else {
            // cc.log(`name: ${this.node.name}, roleName: ${this.roleName}, hp: ${this.characterAttr.hp}`)
        }
    }
    killSpineName = ''
    /**调用攻击时播放的名字 */
    attackSpineName = 'attack'
    idleName = 'idle'
    moveName = 'move'

    /**isEmitEvent 为true说明是正常销毁，false是结束时管理销毁 */
    kill(isEmitEvent = true) {
        //如果战斗已经打完怪物了，已经结束了，但是依然走到这里，比如怪物和主角都死了，只不过怪物比主角先判定，所以被判定win，继续投放怪物，但是主角播放死亡动画，后又发送死亡事件
        //这里的做法是强制锁血，锁住一滴血
        if (FightCtrl.ins.fightState != EFightState.fighting && this.role == CharacterRole.HERO) {
            this.characterAttr.hp = 1
            cc.log('强制锁血1')
            return
        }

        cc.log(`击杀角色:${this.node.name}${this.roleName}, 类型： ${CharacterRole[this.role]}`)
        this.isDead = true;
        let dieCall = () => {
            if (FightCtrl.ins.fightState != EFightState.fighting && this.role == CharacterRole.HERO) {
                this.characterAttr.hp = 1
                cc.log('强制锁血1')
                return
            }
            cc.log(`回收角色:${this.node.name}${this.roleName},类型： ${CharacterRole[this.role]}`)
            this.onRemove?.();
            FightCtrl.ins.removeCharacter(this.node, this.role != CharacterRole.HERO);
            if (isEmitEvent) {
                if (this.role == CharacterRole.HERO) {
                    this.sendEvent(EventName.PlayerDead)
                } else if (this.role == CharacterRole.ENEMY) {
                    this.sendEvent(EventName.EnenmyDead)
                }
            }
        }
        if (this.role == CharacterRole.ENEMY || this.role == CharacterRole.HERO) {
            if (isEmitEvent && this.role == CharacterRole.ENEMY) {
                FightCtrl.ins.killEnemyReward(this.node)
            }
            FightCtrl.ins.createEnemyDieSpine(this.node)
            AudioMgr.getInstance().playEffectFree(AudioId.die, 1, 10)
        }
        if (this.killSpineName != '') {
            this.spine.clearTracks();
            this.spine.setToSetupPose();
            this.playDie()
            this.scheduleOnce(() => { dieCall() }, 1)
        } else {
            dieCall()
        }
    }

    /**播放动画 */

    playIdle(loop?: boolean,) {
        return this.spine.playAsync(this.idleName, loop)
    }

    playMove(loop?: boolean,) {
        return this.spine.playAsync(this.moveName, loop)
    }

    playAttack(loop?: boolean, event?: (trackEntry: sp.spine.TrackEntry, event: sp.spine.Event) => void, isClearEvent?: boolean) {
        return this.spine.playAsync(this.attackSpineName, loop, event, isClearEvent)
    }

    playDie() {
        return this.spine.playAsync(this.killSpineName)
    }
}

