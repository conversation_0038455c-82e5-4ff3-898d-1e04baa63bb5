/**
 * 重构后的角色类
 * 替代原来的 CharacterControl，提供更清晰的架构
 */

import { BaseCharacter } from "../core/base/BaseCharacter";
import { CharacterRole, CharacterState, CharacterCreateInfo, CharacterAttributeData } from "../core/types/CharacterTypes";
import { ICharacter } from "../core/interfaces/ICharacter";
import { AttackAction } from "../actions/AttackAction";
import { MoveAction } from "../actions/MoveAction";

const { ccclass, property } = cc._decorator;

/**
 * 角色类
 */
@ccclass
export class Character extends BaseCharacter {
    // 动作组件
    private _attackAction: AttackAction;
    private _moveAction: MoveAction;

    // 角色配置
    @property(cc.String)
    public prefabKey: string = "";

    @property(cc.String)
    public roleName: string = "";

    @property(cc.String)
    public attackSkillName: string = "";

    // 动画配置
    @property(cc.String)
    public idleAnimationName: string = "idle";

    @property(cc.String)
    public moveAnimationName: string = "move";

    @property(cc.String)
    public attackAnimationName: string = "attack";

    @property(cc.String)
    public deathAnimationName: string = "death";

    // 音效配置
    @property(cc.String)
    public attackSoundId: string = "";

    @property(cc.String)
    public hurtSoundId: string = "";

    @property(cc.String)
    public deathSoundId: string = "";

    /**
     * 初始化角色
     */
    protected onCharacterStart(): void {
        this.initializeActions();
        this.initializeAnimationConfig();
        this.initializeSoundConfig();
        this.setupCollision();
    }

    /**
     * 更新角色
     */
    protected onCharacterUpdate(deltaTime: number): void {
        // 基类已经处理了技能和Buff的更新
        // 这里可以添加角色特定的更新逻辑
        this.updateActions(deltaTime);
    }

    /**
     * 角色死亡处理
     */
    protected onCharacterDeath(): void {
        this.playDeathAnimation();
        this.playDeathSound();
        this.disableCollision();

        // 延迟移除角色
        this.scheduleOnce(() => {
            this.removeCharacter();
        }, 1.0);
    }

    /**
     * 执行移动
     */
    protected performMove(direction: cc.Vec3): void {
        if (this._moveAction) {
            const moveVector = direction.clone().multiplyScalar(this.attributes.moveSpeed);
            this._moveAction.moveBy(moveVector);
            this.playMoveAnimation();
        }
    }

    /**
     * 初始化动作组件
     */
    private initializeActions(): void {
        this._attackAction = this.node.getComponent(AttackAction) || this.node.addComponent(AttackAction);
        this._moveAction = this.node.getComponent(MoveAction) || this.node.addComponent(MoveAction);
    }

    /**
     * 初始化动画配置
     */
    private initializeAnimationConfig(): void {
        this._animationConfig = {
            idle: this.idleAnimationName,
            move: this.moveAnimationName,
            attack: this.attackAnimationName,
            death: this.deathAnimationName
        };
    }

    /**
     * 初始化音效配置
     */
    private initializeSoundConfig(): void {
        this._soundConfig = {
            attack: this.attackSoundId,
            hurt: this.hurtSoundId,
            death: this.deathSoundId
        };
    }

    /**
     * 设置碰撞
     */
    private setupCollision(): void {
        const collider = this.node.getComponent(cc.Collider);
        if (collider) {
            collider.enabled = true;
        }
    }

    /**
     * 禁用碰撞
     */
    private disableCollision(): void {
        const collider = this.node.getComponent(cc.Collider);
        if (collider) {
            collider.enabled = false;
        }
    }

    /**
     * 更新动作组件
     */
    private updateActions(deltaTime: number): void {
        // 动作组件会自己更新，这里可以添加额外的逻辑
    }

    /**
     * 播放移动动画
     */
    private playMoveAnimation(): void {
        if (this._spine && this._animationConfig.move) {
            this._spine.setAnimation(0, this._animationConfig.move, true);
        }
    }

    /**
     * 播放攻击动画
     */
    public playAttackAnimation(loop: boolean = false): void {
        if (this._spine && this._animationConfig.attack) {
            this._spine.setAnimation(0, this._animationConfig.attack, loop);
        }
    }

    /**
     * 播放空闲动画
     */
    public playIdleAnimation(): void {
        if (this._spine && this._animationConfig.idle) {
            this._spine.setAnimation(0, this._animationConfig.idle, true);
        }
    }

    /**
     * 播放死亡音效
     */
    private playDeathSound(): void {
        if (this._soundConfig.death) {
            // 这里应该调用音效管理器播放音效
            // AudioManager.getInstance().playEffect(this._soundConfig.death);
        }
    }

    /**
     * 移除角色
     */
    private removeCharacter(): void {
        // 通知战斗管理器移除角色
        this.node.emit("characterRemoved", this);

        // 销毁节点
        this.node.destroy();
    }

    /**
     * 强制攻击目标
     */
    public forceAttackTo(target: ICharacter): boolean {
        if (!target || target.isDead) {
            console.error('forceAttackTo but target is null or dead');
            return false;
        }

        return this.attack(target);
    }

    /**
     * 执行攻击动作
     */
    public performAttack(target?: cc.Node, onHitCallback?: () => void): boolean {
        if (!this._attackAction) {
            return false;
        }

        this.setState(CharacterState.ATTACKING);
        this.playAttackAnimation();

        // 配置攻击动作
        const attackProps = {
            hurtStartTimeMs: 300, // 300ms后开始造成伤害
            hurtEndTimeMs: 600,   // 600ms后攻击结束
            onHurtStart: () => {
                if (onHitCallback) {
                    onHitCallback();
                }
                // 这里可以添加伤害计算逻辑
            },
            onHurtEnd: () => {
                this.setState(CharacterState.IDLE);
                this.playIdleAnimation();
            }
        };

        return this._attackAction.doAttackOnce(attackProps);
    }

    /**
     * 设置角色数据
     */
    public setCharacterData(data: CharacterCreateInfo): void {
        this._name = data.name;
        this._role = data.role;
        this.roleName = data.name;
        this.prefabKey = typeof data.prefabKey === 'string' ? data.prefabKey : '';

        // 设置初始属性
        if (data.initialAttributes) {
            // 这里应该设置角色的初始属性
            // this._attributes.setInitialData(data.initialAttributes);
        }

        // 设置位置
        if (data.worldPosition) {
            this.node.position = data.parent ?
                data.parent.convertToNodeSpaceAR(data.worldPosition) :
                data.worldPosition;
        }

        // 设置父节点
        if (data.parent) {
            this.node.parent = data.parent;
        }
    }

    /**
     * 获取开火点世界坐标
     */
    public getFireWorldPosition(): cc.Vec3 {
        if (this._fireNode) {
            return this._fireNode.convertToWorldSpaceAR(cc.Vec3.ZERO);
        }
        return this.node.convertToWorldSpaceAR(cc.Vec3.ZERO);
    }

    /**
     * 获取开火方向
     */
    public getFireDirection(target: cc.Vec3): cc.Vec3 {
        const firePos = this.getFireWorldPosition();
        return target.subtract(firePos).normalize();
    }

    /**
     * 检查是否在攻击范围内
     */
    public isInAttackRange(target: Character): boolean {
        if (!target || target.isDead) {
            return false;
        }

        const distance = cc.Vec3.distance(
            this.node.convertToWorldSpaceAR(cc.Vec3.ZERO),
            target.node.convertToWorldSpaceAR(cc.Vec3.ZERO)
        );

        return distance <= this.attributes.attackRange;
    }

    /**
     * 面向目标
     */
    public faceTarget(target: cc.Vec3): void {
        const currentPos = this.node.convertToWorldSpaceAR(cc.Vec3.ZERO);
        const direction = target.subtract(currentPos);

        if (direction.x < 0) {
            this.node.scaleX = -Math.abs(this.node.scaleX);
        } else {
            this.node.scaleX = Math.abs(this.node.scaleX);
        }
    }

    /**
     * 获取角色信息
     */
    public getCharacterInfo(): any {
        return {
            id: this.id,
            name: this.name,
            role: this.role,
            state: this.state,
            isDead: this.isDead,
            attributes: this.attributes.getAttributeData(),
            position: this.node.convertToWorldSpaceAR(cc.Vec3.ZERO),
            skillCount: this.skills.length,
            buffCount: this.buffs.length
        };
    }
}
