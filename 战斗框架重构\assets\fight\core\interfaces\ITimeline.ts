/**
 * Timeline系统接口定义
 * 基于时间轴的技能效果系统，支持复杂的时序控制
 */

import { ICharacter } from "./ICharacter";

/**
 * Timeline接口
 */
export interface ITimeline {
    /** Timeline ID */
    readonly id: string;
    
    /** Timeline名称 */
    readonly name: string;
    
    /** 总持续时间（秒） */
    readonly duration: number;
    
    /** 已经运行的时间 */
    timeElapsed: number;
    
    /** Timeline节点列表 */
    readonly nodes: ITimelineNode[];
    
    /** 施法者 */
    readonly caster: ICharacter;
    
    /** 单个目标 */
    readonly target?: ICharacter;
    
    /** 多个目标 */
    readonly targets?: ICharacter[];
    
    /** 目标位置 */
    readonly targetPosition?: cc.Vec3;
    
    /** 是否已完成 */
    readonly isCompleted: boolean;
    
    /** 是否暂停 */
    isPaused: boolean;
    
    /**
     * 更新Timeline
     * @param deltaTime 时间间隔
     * @returns 是否已完成
     */
    update(deltaTime: number): boolean;
    
    /**
     * 暂停Timeline
     */
    pause(): void;
    
    /**
     * 恢复Timeline
     */
    resume(): void;
    
    /**
     * 停止Timeline
     */
    stop(): void;
    
    /**
     * 重置Timeline
     */
    reset(): void;
    
    /**
     * 跳转到指定时间点
     * @param time 目标时间
     */
    seekTo(time: number): void;
}

/**
 * Timeline节点接口
 */
export interface ITimelineNode {
    /** 节点ID */
    readonly id: string;
    
    /** 触发时间点（秒） */
    readonly triggerTime: number;
    
    /** 节点事件 */
    readonly event: ITimelineEvent;
    
    /** 是否已触发 */
    isTriggered: boolean;
    
    /** 是否可重复触发 */
    readonly repeatable: boolean;
    
    /** 重复间隔（如果可重复） */
    readonly repeatInterval?: number;
    
    /** 最大重复次数（-1表示无限） */
    readonly maxRepeats?: number;
    
    /** 当前重复次数 */
    currentRepeats: number;
    
    /**
     * 检查是否应该触发
     * @param currentTime 当前时间
     * @param deltaTime 时间间隔
     * @returns 是否应该触发
     */
    shouldTrigger(currentTime: number, deltaTime: number): boolean;
    
    /**
     * 触发节点事件
     * @param timeline Timeline实例
     * @param nodeIndex 节点索引
     */
    trigger(timeline: ITimeline, nodeIndex: number): void;
    
    /**
     * 重置节点状态
     */
    reset(): void;
}

/**
 * Timeline事件接口
 */
export interface ITimelineEvent {
    /** 事件ID */
    readonly id: string;
    
    /** 事件类型 */
    readonly type: TimelineEventType;
    
    /**
     * 执行事件
     * @param timeline Timeline实例
     * @param nodeIndex 节点索引
     * @param context 上下文信息
     */
    execute(timeline: ITimeline, nodeIndex: number, context?: any): void;
    
    /**
     * 播放音效（辅助方法）
     * @param soundId 音效ID
     */
    playSound?(soundId: string): void;
    
    /**
     * 播放特效（辅助方法）
     * @param effectId 特效ID
     * @param position 位置
     */
    playEffect?(effectId: string, position?: cc.Vec3): void;
}

/**
 * Timeline事件类型枚举
 */
export enum TimelineEventType {
    /** 伤害事件 */
    DAMAGE = "damage",
    
    /** 治疗事件 */
    HEAL = "heal",
    
    /** 子弹发射事件 */
    FIRE_BULLET = "fire_bullet",
    
    /** Buff添加事件 */
    ADD_BUFF = "add_buff",
    
    /** Buff移除事件 */
    REMOVE_BUFF = "remove_buff",
    
    /** 动画播放事件 */
    PLAY_ANIMATION = "play_animation",
    
    /** 音效播放事件 */
    PLAY_SOUND = "play_sound",
    
    /** 特效播放事件 */
    PLAY_EFFECT = "play_effect",
    
    /** 移动事件 */
    MOVE = "move",
    
    /** 传送事件 */
    TELEPORT = "teleport",
    
    /** 召唤事件 */
    SUMMON = "summon",
    
    /** 自定义事件 */
    CUSTOM = "custom"
}

/**
 * Timeline配置接口
 */
export interface ITimelineConfig {
    /** Timeline ID */
    id: string;
    
    /** Timeline名称 */
    name: string;
    
    /** 总持续时间 */
    duration: number;
    
    /** 节点配置列表 */
    nodes: ITimelineNodeConfig[];
    
    /** 是否循环 */
    loop?: boolean;
    
    /** 循环次数（-1表示无限循环） */
    loopCount?: number;
    
    /** 时间缩放 */
    timeScale?: number;
}

/**
 * Timeline节点配置接口
 */
export interface ITimelineNodeConfig {
    /** 节点ID */
    id: string;
    
    /** 触发时间点 */
    triggerTime: number;
    
    /** 事件配置 */
    event: ITimelineEventConfig;
    
    /** 是否可重复触发 */
    repeatable?: boolean;
    
    /** 重复间隔 */
    repeatInterval?: number;
    
    /** 最大重复次数 */
    maxRepeats?: number;
}

/**
 * Timeline事件配置接口
 */
export interface ITimelineEventConfig {
    /** 事件ID */
    id: string;
    
    /** 事件类型 */
    type: TimelineEventType;
    
    /** 事件参数 */
    params?: any;
    
    /** 条件检查函数 */
    condition?: (timeline: ITimeline) => boolean;
}

/**
 * Timeline管理器接口
 */
export interface ITimelineManager {
    /** 所有活跃的Timeline */
    readonly activeTimelines: ReadonlyArray<ITimeline>;
    
    /**
     * 添加Timeline
     * @param timeline Timeline实例
     */
    addTimeline(timeline: ITimeline): void;
    
    /**
     * 根据配置创建并添加Timeline
     * @param config Timeline配置
     * @param caster 施法者
     * @param target 目标
     * @param targets 多个目标
     * @param position 目标位置
     */
    createTimeline(
        config: ITimelineConfig,
        caster: ICharacter,
        target?: ICharacter,
        targets?: ICharacter[],
        position?: cc.Vec3
    ): ITimeline;
    
    /**
     * 移除Timeline
     * @param timelineId Timeline ID
     */
    removeTimeline(timelineId: string): void;
    
    /**
     * 根据施法者移除Timeline
     * @param caster 施法者
     */
    removeTimelinesByCaster(caster: ICharacter): void;
    
    /**
     * 暂停所有Timeline
     */
    pauseAll(): void;
    
    /**
     * 恢复所有Timeline
     */
    resumeAll(): void;
    
    /**
     * 清除所有Timeline
     */
    clearAll(): void;
    
    /**
     * 更新所有Timeline
     * @param deltaTime 时间间隔
     */
    update(deltaTime: number): void;
    
    /**
     * 获取Timeline统计信息
     */
    getStats(): {
        activeCount: number;
        pausedCount: number;
        totalExecutedEvents: number;
    };
}
