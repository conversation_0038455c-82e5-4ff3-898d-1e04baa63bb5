{"skeleton": {"hash": "3VfpUymlKL33BLwaBSbJ3YL8PAQ", "spine": "3.8.99", "x": -11.11, "y": -72.97, "width": 84.96, "height": 181.92, "images": "./images/", "audio": "/Users/<USER>/Documents/git_art/spine文件/25/bomb"}, "bones": [{"name": "root"}, {"name": "blast", "parent": "root", "x": 20.86, "y": 25.64, "scaleX": 2.4199, "scaleY": 2.4199}, {"name": "bomb", "parent": "root", "length": 67.38, "rotation": 89.5, "x": 22.28, "y": 15.61}, {"name": "line2", "parent": "bomb", "length": 17.97, "rotation": 171.04, "x": -8.86, "y": -0.08}, {"name": "line3", "parent": "line2", "length": 15.37, "rotation": 32.08, "x": 23.61, "y": 2.14}, {"name": "line4", "parent": "line3", "length": 11.41, "rotation": 46.13, "x": 20.46, "y": 3}, {"name": "line5", "parent": "line4", "length": 12.98, "rotation": -8.82, "x": 17.44, "y": -0.83}, {"name": "line6", "parent": "line5", "length": 16.14, "rotation": -36.18, "x": 17.31, "y": -1.59}, {"name": "fire", "parent": "root", "x": 71.34, "y": -63.58}], "slots": [{"name": "bomb", "bone": "bomb", "attachment": "bomb2"}, {"name": "line", "bone": "line2", "attachment": "line"}, {"name": "bomb2", "bone": "bomb", "attachment": "boom2"}, {"name": "fire", "bone": "fire"}, {"name": "100", "bone": "blast"}, {"name": "91", "bone": "blast"}, {"name": "92", "bone": "blast"}, {"name": "93", "bone": "blast"}, {"name": "94", "bone": "blast"}, {"name": "95", "bone": "blast"}, {"name": "96", "bone": "blast"}, {"name": "97", "bone": "blast"}, {"name": "98", "bone": "blast"}, {"name": "99", "bone": "blast"}], "skins": [{"name": "default", "attachments": {"91": {"91": {"y": 0.5, "width": 168, "height": 171}}, "92": {"92": {"y": 0.5, "width": 168, "height": 171}}, "93": {"93": {"y": 0.5, "width": 168, "height": 171}}, "94": {"94": {"y": 0.5, "width": 168, "height": 171}}, "95": {"95": {"y": 0.5, "width": 168, "height": 171}}, "96": {"96": {"y": 0.5, "width": 168, "height": 171}}, "97": {"97": {"y": 0.5, "width": 168, "height": 171}}, "98": {"98": {"y": 0.5, "width": 168, "height": 171}}, "99": {"99": {"y": 0.5, "width": 168, "height": 171}}, "100": {"100": {"y": 0.5, "width": 168, "height": 171}}, "fire": {"fire": {"x": -4.95, "y": 3.03, "width": 67, "height": 64}}, "line": {"line": {"type": "mesh", "uvs": [0.28025, 0.12386, 0.21305, 0.15045, 0.19184, 0.22778, 0.17415, 0.37761, 0.21305, 0.49603, 0.30147, 0.57336, 0.46062, 0.62411, 0.60562, 0.67003, 0.68696, 0.71836, 0.75415, 0.78361, 0.7683, 0.84403, 0.83549, 0.85853, 0.90623, 0.83436, 0.89208, 0.74978, 0.83549, 0.64586, 0.71879, 0.59511, 0.5561, 0.52261, 0.39342, 0.4767, 0.30854, 0.39211, 0.32976, 0.26403, 0.3722, 0.17461, 0.35805, 0.13353], "triangles": [12, 11, 9, 11, 10, 9, 9, 13, 12, 13, 9, 14, 9, 8, 14, 8, 15, 14, 8, 7, 15, 15, 7, 16, 5, 17, 6, 7, 6, 16, 6, 17, 16, 5, 4, 17, 4, 18, 17, 4, 3, 18, 20, 0, 21, 0, 20, 1, 19, 2, 20, 18, 3, 19, 3, 2, 19, 20, 2, 1], "vertices": [1, 3, -8.36, -1.16, 1, 1, 3, -4.31, -6.07, 1, 1, 3, 5.13, -6.26, 1, 2, 3, 23.1, -4.74, 0.44682, 4, -4.08, -5.55, 0.55318, 1, 4, 10.26, -8.08, 1, 2, 4, 21.62, -4.95, 0.50992, 5, -4.93, -6.35, 0.49008, 2, 5, 9.44, -7.29, 0.85123, 6, -6.92, -7.62, 0.14877, 3, 5, 22.52, -8.12, 0.00187, 6, 6.13, -6.43, 0.97321, 7, -6.16, -10.5, 0.02492, 2, 6, 14.81, -8.11, 0.29707, 7, 1.83, -6.73, 0.70293, 1, 7, 11.22, -4.84, 1, 1, 7, 18.32, -6.7, 1, 1, 7, 22.13, -2.36, 1, 1, 7, 21.81, 4.12, 1, 1, 7, 12.06, 7.14, 1, 2, 6, 20.99, 5.53, 0.1453, 7, -1.23, 7.92, 0.8547, 2, 6, 9.66, 6, 0.99886, 7, -10.65, 1.61, 0.00114, 2, 5, 12.32, 6.9, 0.81946, 6, -6.24, 6.85, 0.18054, 2, 4, 13.81, 6.47, 0.60879, 5, -2.11, 7.2, 0.39121, 2, 3, 23.01, 6.42, 0.1036, 4, 1.76, 3.95, 0.8964, 1, 3, 7.56, 5.61, 1, 1, 3, -3.6, 7.28, 1, 1, 3, -8.27, 5.32, 1], "hull": 22, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 0, 42], "width": 82, "height": 120}}}}, {"name": "boom1", "attachments": {"bomb": {"bomb2": {"name": "bomb", "x": 32.37, "y": -3.32, "rotation": -89.5, "width": 74, "height": 122}, "bomb_w": {"x": 32.69, "y": -2.89, "rotation": -88.79, "width": 74, "height": 122}}}}, {"name": "boom2", "attachments": {"bomb2": {"boom2": {"x": 23.94, "y": 6.85, "rotation": 109.27, "width": 135, "height": 138}, "boom2_W": {"x": 23.9, "y": -0.05, "rotation": 109.22, "width": 124, "height": 137}}}}], "events": {"blowup": {}}, "animations": {"49extinguish": {"bones": {"line5": {"translate": [{"x": -13.41, "y": 1.16}]}, "line3": {"translate": [{"x": -14.29, "y": -2.18}]}, "line4": {"translate": [{"x": -16.23, "y": -9.11}]}, "line6": {"rotate": [{"angle": 18.98}], "translate": [{"x": -8.92, "y": 2.67}]}, "fire": {"translate": [{"x": -33.66, "y": 43.68}]}}}, "49ignite": {"slots": {"bomb": {"attachment": [{"time": 4.1667, "name": "bomb_w"}, {"time": 4.3333, "name": "bomb2"}, {"time": 5.5, "name": "bomb_w"}, {"time": 5.6667, "name": "bomb2"}, {"time": 6.8333, "name": "bomb_w"}, {"time": 7, "name": "bomb2"}, {"time": 7.8333, "name": "bomb_w"}, {"time": 8, "name": "bomb2"}, {"time": 8.3333, "name": "bomb_w"}, {"time": 8.5, "name": "bomb2"}, {"time": 8.6667, "name": "bomb_w"}, {"time": 8.7333, "name": "bomb2"}, {"time": 8.8, "name": "bomb_w"}, {"time": 8.8667, "name": "bomb2"}, {"time": 8.9333, "name": "bomb_w"}, {"time": 9, "name": "bomb2"}, {"time": 9.0667, "name": "bomb_w"}, {"time": 9.1333, "name": "bomb2"}, {"time": 9.2, "name": "bomb_w"}, {"time": 9.2667, "name": "bomb2"}, {"time": 9.3333, "name": "bomb_w"}, {"time": 9.4667, "name": "bomb2"}, {"time": 9.5333, "name": "bomb_w"}, {"time": 9.6667, "name": "bomb2"}, {"time": 9.7333, "name": "bomb_w"}, {"time": 9.8667, "name": "bomb2"}, {"time": 9.9333, "name": "bomb_w"}, {"time": 10, "name": "bomb2"}]}, "fire": {"attachment": [{"time": 0.0333, "name": "fire"}]}}, "bones": {"bomb": {"scale": [{"time": 4}, {"time": 4.1667, "y": 1.153}, {"time": 4.3333, "curve": "stepped"}, {"time": 5.3333}, {"time": 5.5, "y": 1.153}, {"time": 5.6667, "curve": "stepped"}, {"time": 6.6667}, {"time": 6.8333, "y": 1.153}, {"time": 7, "curve": "stepped"}, {"time": 7.6667}, {"time": 7.8333, "y": 1.153}, {"time": 8, "curve": "stepped"}, {"time": 8.1667}, {"time": 8.3333, "y": 1.153}, {"time": 8.5, "curve": "stepped"}, {"time": 8.6}, {"time": 8.6667, "y": 1.153}, {"time": 8.7333}, {"time": 8.8, "y": 1.153}, {"time": 8.8667}, {"time": 8.9333, "y": 1.153}, {"time": 9}, {"time": 9.0667, "y": 1.153}, {"time": 9.1333}, {"time": 9.2, "y": 1.153}, {"time": 9.2667}, {"time": 9.3333, "y": 1.153, "curve": "stepped"}, {"time": 9.4, "y": 1.153}, {"time": 9.4667}, {"time": 9.5333, "y": 1.153, "curve": "stepped"}, {"time": 9.6, "y": 1.153}, {"time": 9.6667}, {"time": 9.7333, "y": 1.153, "curve": "stepped"}, {"time": 9.8, "y": 1.153}, {"time": 9.8667}, {"time": 9.9333, "y": 1.153}, {"time": 10}]}, "line3": {"translate": [{}, {"time": 6.6667, "x": -14.29, "y": -2.18}, {"time": 10, "x": -21.43, "y": -3.27}]}, "line4": {"translate": [{}, {"time": 6.6667, "x": -11.21, "y": -4.77}, {"time": 10, "x": -16.81, "y": -7.15}]}, "line5": {"rotate": [{"curve": "stepped"}, {"time": 0.1, "angle": 18.24}, {"time": 1.6667, "curve": "stepped"}, {"time": 1.7667, "angle": 18.24}, {"time": 3.3333, "curve": "stepped"}, {"time": 3.4333, "angle": 18.24}, {"time": 5, "curve": "stepped"}, {"time": 5.1, "angle": 18.24}, {"time": 6.6667, "curve": "stepped"}, {"time": 6.7667, "angle": 18.24}, {"time": 8.3333, "curve": "stepped"}, {"time": 8.4333, "angle": 18.24}], "translate": [{}, {"time": 6.6667, "x": -13.41, "y": 1.16}, {"time": 10, "x": -20.11, "y": 1.74}]}, "line6": {"translate": [{}, {"time": 6.6667, "x": -12.1, "y": 3.52}, {"time": 10, "x": -18.15, "y": 5.28}]}, "fire": {"rotate": [{"curve": "stepped"}, {"time": 0.0333, "angle": -24.87, "curve": "stepped"}, {"time": 0.0667, "angle": 66.16, "curve": "stepped"}, {"time": 0.1, "angle": 45.08}, {"time": 0.1333, "angle": 111.26, "curve": "stepped"}, {"time": 0.1667, "angle": 82.23, "curve": "stepped"}, {"time": 0.2, "angle": 110.47, "curve": "stepped"}, {"time": 0.2333, "angle": 142.72, "curve": "stepped"}, {"time": 0.2667, "angle": 173.71, "curve": "stepped"}, {"time": 0.3333, "angle": 145.17, "curve": "stepped"}, {"time": 0.4333, "angle": 112.04, "curve": "stepped"}, {"time": 0.4667, "angle": 137.72}, {"time": 1.6667, "curve": "stepped"}, {"time": 1.7, "angle": -24.87, "curve": "stepped"}, {"time": 1.7333, "angle": 66.16, "curve": "stepped"}, {"time": 1.7667, "angle": 45.08}, {"time": 1.8, "angle": 111.26, "curve": "stepped"}, {"time": 1.8333, "angle": 82.23, "curve": "stepped"}, {"time": 1.8667, "angle": 110.47, "curve": "stepped"}, {"time": 1.9, "angle": 142.72, "curve": "stepped"}, {"time": 1.9333, "angle": 173.71, "curve": "stepped"}, {"time": 2, "angle": 145.17, "curve": "stepped"}, {"time": 2.1, "angle": 112.04, "curve": "stepped"}, {"time": 2.1333, "angle": 137.72}, {"time": 3.3333, "curve": "stepped"}, {"time": 3.3667, "angle": -24.87, "curve": "stepped"}, {"time": 3.4, "angle": 66.16, "curve": "stepped"}, {"time": 3.4333, "angle": 45.08}, {"time": 3.4667, "angle": 111.26, "curve": "stepped"}, {"time": 3.5, "angle": 82.23, "curve": "stepped"}, {"time": 3.5333, "angle": 110.47, "curve": "stepped"}, {"time": 3.5667, "angle": 142.72, "curve": "stepped"}, {"time": 3.6, "angle": 173.71, "curve": "stepped"}, {"time": 3.6667, "angle": 145.17, "curve": "stepped"}, {"time": 3.7667, "angle": 112.04, "curve": "stepped"}, {"time": 3.8, "angle": 137.72}, {"time": 5, "curve": "stepped"}, {"time": 5.0333, "angle": -24.87, "curve": "stepped"}, {"time": 5.0667, "angle": 66.16, "curve": "stepped"}, {"time": 5.1, "angle": 45.08}, {"time": 5.1333, "angle": 111.26, "curve": "stepped"}, {"time": 5.1667, "angle": 82.23, "curve": "stepped"}, {"time": 5.2, "angle": 110.47, "curve": "stepped"}, {"time": 5.2333, "angle": 142.72, "curve": "stepped"}, {"time": 5.2667, "angle": 173.71, "curve": "stepped"}, {"time": 5.3333, "angle": 145.17, "curve": "stepped"}, {"time": 5.4333, "angle": 112.04, "curve": "stepped"}, {"time": 5.4667, "angle": 137.72}, {"time": 6.6667, "curve": "stepped"}, {"time": 6.7, "angle": -24.87, "curve": "stepped"}, {"time": 6.7333, "angle": 66.16, "curve": "stepped"}, {"time": 6.7667, "angle": 45.08}, {"time": 6.8, "angle": 111.26, "curve": "stepped"}, {"time": 6.8333, "angle": 82.23, "curve": "stepped"}, {"time": 6.8667, "angle": 110.47, "curve": "stepped"}, {"time": 6.9, "angle": 142.72, "curve": "stepped"}, {"time": 6.9333, "angle": 173.71, "curve": "stepped"}, {"time": 7, "angle": 145.17, "curve": "stepped"}, {"time": 7.1, "angle": 112.04, "curve": "stepped"}, {"time": 7.1333, "angle": 137.72}, {"time": 8.3333, "curve": "stepped"}, {"time": 8.3667, "angle": -24.87, "curve": "stepped"}, {"time": 8.4, "angle": 66.16, "curve": "stepped"}, {"time": 8.4333, "angle": 45.08}, {"time": 8.4667, "angle": 111.26, "curve": "stepped"}, {"time": 8.5, "angle": 82.23, "curve": "stepped"}, {"time": 8.5333, "angle": 110.47, "curve": "stepped"}, {"time": 8.5667, "angle": 142.72, "curve": "stepped"}, {"time": 8.6, "angle": 173.71, "curve": "stepped"}, {"time": 8.6667, "angle": 145.17, "curve": "stepped"}, {"time": 8.7667, "angle": 112.04, "curve": "stepped"}, {"time": 8.8, "angle": 137.72}], "translate": [{}, {"time": 0.0333, "x": -4.89, "y": 3.54}, {"time": 6.6667, "x": -33.66, "y": 43.68}, {"time": 10, "x": -48.11, "y": 63.85}], "scale": [{"curve": "stepped"}, {"time": 0.3, "y": -1, "curve": "stepped"}, {"time": 0.3667, "x": -1, "y": -1, "curve": "stepped"}, {"time": 0.4, "curve": "stepped"}, {"time": 0.5, "x": -1, "curve": "stepped"}, {"time": 0.5333, "curve": "stepped"}, {"time": 0.5667, "y": -1, "curve": "stepped"}, {"time": 0.6, "x": -1, "y": -1, "curve": "stepped"}, {"time": 0.6333, "x": -1, "curve": "stepped"}, {"time": 0.6667, "curve": "stepped"}, {"time": 0.7, "x": -1, "curve": "stepped"}, {"time": 0.7333, "x": -1, "y": -1, "curve": "stepped"}, {"time": 0.7667, "y": -1, "curve": "stepped"}, {"time": 0.8, "curve": "stepped"}, {"time": 0.8333, "y": -1}, {"time": 0.8667}, {"time": 0.9, "y": -1}, {"time": 0.9333, "x": -1, "y": -1}, {"time": 0.9667, "x": -1, "curve": "stepped"}, {"time": 1, "curve": "stepped"}, {"time": 1.0333, "y": -1, "curve": "stepped"}, {"time": 1.0667, "x": -1, "y": -1, "curve": "stepped"}, {"time": 1.1, "x": -1, "curve": "stepped"}, {"time": 1.1333, "curve": "stepped"}, {"time": 1.1667, "x": -1, "curve": "stepped"}, {"time": 1.2, "x": -1, "y": -1, "curve": "stepped"}, {"time": 1.2333, "y": -1, "curve": "stepped"}, {"time": 1.2667, "y": -1, "curve": "stepped"}, {"time": 1.3, "x": -1, "y": -1, "curve": "stepped"}, {"time": 1.3333, "x": -1, "curve": "stepped"}, {"time": 1.3667, "curve": "stepped"}, {"time": 1.4, "x": -1, "curve": "stepped"}, {"time": 1.4333, "x": -1, "y": -1, "curve": "stepped"}, {"time": 1.4667, "y": -1, "curve": "stepped"}, {"time": 1.5, "x": -1, "y": -1, "curve": "stepped"}, {"time": 1.5333, "x": -1, "curve": "stepped"}, {"time": 1.5667, "curve": "stepped"}, {"time": 1.6, "x": -1, "curve": "stepped"}, {"time": 1.6333, "x": -1, "y": -1, "curve": "stepped"}, {"time": 1.6667, "curve": "stepped"}, {"time": 1.9667, "y": -1, "curve": "stepped"}, {"time": 2.0333, "x": -1, "y": -1, "curve": "stepped"}, {"time": 2.0667, "curve": "stepped"}, {"time": 2.1667, "x": -1, "curve": "stepped"}, {"time": 2.2, "curve": "stepped"}, {"time": 2.2333, "y": -1, "curve": "stepped"}, {"time": 2.2667, "x": -1, "y": -1, "curve": "stepped"}, {"time": 2.3, "x": -1, "curve": "stepped"}, {"time": 2.3333, "curve": "stepped"}, {"time": 2.3667, "x": -1, "curve": "stepped"}, {"time": 2.4, "x": -1, "y": -1, "curve": "stepped"}, {"time": 2.4333, "y": -1, "curve": "stepped"}, {"time": 2.4667, "curve": "stepped"}, {"time": 2.5, "y": -1}, {"time": 2.5333}, {"time": 2.5667, "y": -1}, {"time": 2.6, "x": -1, "y": -1}, {"time": 2.6333, "x": -1, "curve": "stepped"}, {"time": 2.6667, "curve": "stepped"}, {"time": 2.7, "y": -1, "curve": "stepped"}, {"time": 2.7333, "x": -1, "y": -1, "curve": "stepped"}, {"time": 2.7667, "x": -1, "curve": "stepped"}, {"time": 2.8, "curve": "stepped"}, {"time": 2.8333, "x": -1, "curve": "stepped"}, {"time": 2.8667, "x": -1, "y": -1, "curve": "stepped"}, {"time": 2.9, "y": -1, "curve": "stepped"}, {"time": 2.9333, "y": -1, "curve": "stepped"}, {"time": 2.9667, "x": -1, "y": -1, "curve": "stepped"}, {"time": 3, "x": -1, "curve": "stepped"}, {"time": 3.0333, "curve": "stepped"}, {"time": 3.0667, "x": -1, "curve": "stepped"}, {"time": 3.1, "x": -1, "y": -1, "curve": "stepped"}, {"time": 3.1333, "y": -1, "curve": "stepped"}, {"time": 3.1667, "x": -1, "y": -1, "curve": "stepped"}, {"time": 3.2, "x": -1, "curve": "stepped"}, {"time": 3.2333, "curve": "stepped"}, {"time": 3.2667, "x": -1, "curve": "stepped"}, {"time": 3.3, "x": -1, "y": -1, "curve": "stepped"}, {"time": 3.3333, "curve": "stepped"}, {"time": 3.6333, "y": -1, "curve": "stepped"}, {"time": 3.7, "x": -1, "y": -1, "curve": "stepped"}, {"time": 3.7333, "curve": "stepped"}, {"time": 3.8333, "x": -1, "curve": "stepped"}, {"time": 3.8667, "curve": "stepped"}, {"time": 3.9, "y": -1, "curve": "stepped"}, {"time": 3.9333, "x": -1, "y": -1, "curve": "stepped"}, {"time": 3.9667, "x": -1, "curve": "stepped"}, {"time": 4, "curve": "stepped"}, {"time": 4.0333, "x": -1, "curve": "stepped"}, {"time": 4.0667, "x": -1, "y": -1, "curve": "stepped"}, {"time": 4.1, "y": -1, "curve": "stepped"}, {"time": 4.1333, "curve": "stepped"}, {"time": 4.1667, "y": -1}, {"time": 4.2}, {"time": 4.2333, "y": -1}, {"time": 4.2667, "x": -1, "y": -1}, {"time": 4.3, "x": -1, "curve": "stepped"}, {"time": 4.3333, "curve": "stepped"}, {"time": 4.3667, "y": -1, "curve": "stepped"}, {"time": 4.4, "x": -1, "y": -1, "curve": "stepped"}, {"time": 4.4333, "x": -1, "curve": "stepped"}, {"time": 4.4667, "curve": "stepped"}, {"time": 4.5, "x": -1, "curve": "stepped"}, {"time": 4.5333, "x": -1, "y": -1, "curve": "stepped"}, {"time": 4.5667, "y": -1, "curve": "stepped"}, {"time": 4.6, "y": -1, "curve": "stepped"}, {"time": 4.6333, "x": -1, "y": -1, "curve": "stepped"}, {"time": 4.6667, "x": -1, "curve": "stepped"}, {"time": 4.7, "curve": "stepped"}, {"time": 4.7333, "x": -1, "curve": "stepped"}, {"time": 4.7667, "x": -1, "y": -1, "curve": "stepped"}, {"time": 4.8, "y": -1, "curve": "stepped"}, {"time": 4.8333, "x": -1, "y": -1, "curve": "stepped"}, {"time": 4.8667, "x": -1, "curve": "stepped"}, {"time": 4.9, "curve": "stepped"}, {"time": 4.9333, "x": -1, "curve": "stepped"}, {"time": 4.9667, "x": -1, "y": -1, "curve": "stepped"}, {"time": 5, "curve": "stepped"}, {"time": 5.3, "y": -1, "curve": "stepped"}, {"time": 5.3667, "x": -1, "y": -1, "curve": "stepped"}, {"time": 5.4, "curve": "stepped"}, {"time": 5.5, "x": -1, "curve": "stepped"}, {"time": 5.5333, "curve": "stepped"}, {"time": 5.5667, "y": -1, "curve": "stepped"}, {"time": 5.6, "x": -1, "y": -1, "curve": "stepped"}, {"time": 5.6333, "x": -1, "curve": "stepped"}, {"time": 5.6667, "curve": "stepped"}, {"time": 5.7, "x": -1, "curve": "stepped"}, {"time": 5.7333, "x": -1, "y": -1, "curve": "stepped"}, {"time": 5.7667, "y": -1, "curve": "stepped"}, {"time": 5.8, "curve": "stepped"}, {"time": 5.8333, "y": -1}, {"time": 5.8667}, {"time": 5.9, "y": -1}, {"time": 5.9333, "x": -1, "y": -1}, {"time": 5.9667, "x": -1, "curve": "stepped"}, {"time": 6, "curve": "stepped"}, {"time": 6.0333, "y": -1, "curve": "stepped"}, {"time": 6.0667, "x": -1, "y": -1, "curve": "stepped"}, {"time": 6.1, "x": -1, "curve": "stepped"}, {"time": 6.1333, "curve": "stepped"}, {"time": 6.1667, "x": -1, "curve": "stepped"}, {"time": 6.2, "x": -1, "y": -1, "curve": "stepped"}, {"time": 6.2333, "y": -1, "curve": "stepped"}, {"time": 6.2667, "y": -1, "curve": "stepped"}, {"time": 6.3, "x": -1, "y": -1, "curve": "stepped"}, {"time": 6.3333, "x": -1, "curve": "stepped"}, {"time": 6.3667, "curve": "stepped"}, {"time": 6.4, "x": -1, "curve": "stepped"}, {"time": 6.4333, "x": -1, "y": -1, "curve": "stepped"}, {"time": 6.4667, "y": -1, "curve": "stepped"}, {"time": 6.5, "x": -1, "y": -1, "curve": "stepped"}, {"time": 6.5333, "x": -1, "curve": "stepped"}, {"time": 6.5667, "curve": "stepped"}, {"time": 6.6, "x": -1, "curve": "stepped"}, {"time": 6.6333, "x": -1, "y": -1, "curve": "stepped"}, {"time": 6.6667, "curve": "stepped"}, {"time": 6.9667, "y": -1, "curve": "stepped"}, {"time": 7.0333, "x": -1, "y": -1, "curve": "stepped"}, {"time": 7.0667, "curve": "stepped"}, {"time": 7.1667, "x": -1, "curve": "stepped"}, {"time": 7.2, "curve": "stepped"}, {"time": 7.2333, "y": -1, "curve": "stepped"}, {"time": 7.2667, "x": -1, "y": -1, "curve": "stepped"}, {"time": 7.3, "x": -1, "curve": "stepped"}, {"time": 7.3333, "curve": "stepped"}, {"time": 7.3667, "x": -1, "curve": "stepped"}, {"time": 7.4, "x": -1, "y": -1, "curve": "stepped"}, {"time": 7.4333, "y": -1, "curve": "stepped"}, {"time": 7.4667, "curve": "stepped"}, {"time": 7.5, "y": -1}, {"time": 7.5333}, {"time": 7.5667, "y": -1}, {"time": 7.6, "x": -1, "y": -1}, {"time": 7.6333, "x": -1, "curve": "stepped"}, {"time": 7.6667, "curve": "stepped"}, {"time": 7.7, "y": -1, "curve": "stepped"}, {"time": 7.7333, "x": -1, "y": -1, "curve": "stepped"}, {"time": 7.7667, "x": -1, "curve": "stepped"}, {"time": 7.8, "curve": "stepped"}, {"time": 7.8333, "x": -1, "curve": "stepped"}, {"time": 7.8667, "x": -1, "y": -1, "curve": "stepped"}, {"time": 7.9, "y": -1, "curve": "stepped"}, {"time": 7.9333, "y": -1, "curve": "stepped"}, {"time": 7.9667, "x": -1, "y": -1, "curve": "stepped"}, {"time": 8, "x": -1, "curve": "stepped"}, {"time": 8.0333, "curve": "stepped"}, {"time": 8.0667, "x": -1, "curve": "stepped"}, {"time": 8.1, "x": -1, "y": -1, "curve": "stepped"}, {"time": 8.1333, "y": -1, "curve": "stepped"}, {"time": 8.1667, "x": -1, "y": -1, "curve": "stepped"}, {"time": 8.2, "x": -1, "curve": "stepped"}, {"time": 8.2333, "curve": "stepped"}, {"time": 8.2667, "x": -1, "curve": "stepped"}, {"time": 8.3, "x": -1, "y": -1, "curve": "stepped"}, {"time": 8.3333, "curve": "stepped"}, {"time": 8.6333, "y": -1, "curve": "stepped"}, {"time": 8.7, "x": -1, "y": -1, "curve": "stepped"}, {"time": 8.7333, "curve": "stepped"}, {"time": 8.8333, "x": -1, "curve": "stepped"}, {"time": 8.8667, "curve": "stepped"}, {"time": 8.9, "y": -1, "curve": "stepped"}, {"time": 8.9333, "x": -1, "y": -1, "curve": "stepped"}, {"time": 8.9667, "x": -1, "curve": "stepped"}, {"time": 9, "curve": "stepped"}, {"time": 9.0333, "x": -1, "curve": "stepped"}, {"time": 9.0667, "x": -1, "y": -1, "curve": "stepped"}, {"time": 9.1, "y": -1, "curve": "stepped"}, {"time": 9.1333, "curve": "stepped"}, {"time": 9.1667, "y": -1}, {"time": 9.2}, {"time": 9.2333, "y": -1}, {"time": 9.2667, "x": -1, "y": -1}, {"time": 9.3, "x": -1, "curve": "stepped"}, {"time": 9.3333, "curve": "stepped"}, {"time": 9.3667, "y": -1, "curve": "stepped"}, {"time": 9.4, "x": -1, "y": -1, "curve": "stepped"}, {"time": 9.4333, "x": -1, "curve": "stepped"}, {"time": 9.4667, "curve": "stepped"}, {"time": 9.5, "x": -1, "curve": "stepped"}, {"time": 9.5333, "x": -1, "y": -1, "curve": "stepped"}, {"time": 9.5667, "y": -1, "curve": "stepped"}, {"time": 9.6, "y": -1, "curve": "stepped"}, {"time": 9.6333, "x": -1, "y": -1, "curve": "stepped"}, {"time": 9.6667, "x": -1, "curve": "stepped"}, {"time": 9.7, "curve": "stepped"}, {"time": 9.7333, "x": -1, "curve": "stepped"}, {"time": 9.7667, "x": -1, "y": -1, "curve": "stepped"}, {"time": 9.8, "y": -1, "curve": "stepped"}, {"time": 9.8333, "x": -1, "y": -1, "curve": "stepped"}, {"time": 9.8667, "x": -1, "curve": "stepped"}, {"time": 9.9, "curve": "stepped"}, {"time": 9.9333, "x": -1, "curve": "stepped"}, {"time": 9.9667, "x": -1, "y": -1}]}}}, "49ignite2": {"bones": {"bomb": {"scale": [{"time": 4}, {"time": 4.1667, "y": 1.153}, {"time": 4.3333, "curve": "stepped"}, {"time": 5.3333}, {"time": 5.5, "y": 1.153}, {"time": 5.6667, "curve": "stepped"}, {"time": 6.6667}, {"time": 6.8333, "y": 1.153}, {"time": 7, "curve": "stepped"}, {"time": 7.6667}, {"time": 7.8333, "y": 1.153}, {"time": 8, "curve": "stepped"}, {"time": 8.1667}, {"time": 8.3333, "y": 1.153}, {"time": 8.5, "curve": "stepped"}, {"time": 8.6}, {"time": 8.6667, "y": 1.153}, {"time": 8.7333}, {"time": 8.8, "y": 1.153}, {"time": 8.8667}, {"time": 8.9333, "y": 1.153}, {"time": 9}, {"time": 9.0667, "y": 1.153}, {"time": 9.1333}, {"time": 9.2, "y": 1.153}, {"time": 9.2667}, {"time": 9.3333, "y": 1.153, "curve": "stepped"}, {"time": 9.4, "y": 1.153}, {"time": 9.4667}, {"time": 9.5333, "y": 1.153, "curve": "stepped"}, {"time": 9.6, "y": 1.153}, {"time": 9.6667}, {"time": 9.7333, "y": 1.153, "curve": "stepped"}, {"time": 9.8, "y": 1.153}, {"time": 9.8667}, {"time": 9.9333, "y": 1.153}, {"time": 10}]}, "line3": {"translate": [{}, {"time": 6.6667, "x": -14.29, "y": -2.18}, {"time": 10, "x": -21.43, "y": -3.27}]}, "line4": {"translate": [{}, {"time": 6.6667, "x": -11.21, "y": -4.77}, {"time": 10, "x": -16.81, "y": -7.15}]}, "line5": {"rotate": [{"curve": "stepped"}, {"time": 0.1, "angle": 18.24}, {"time": 1.6667, "curve": "stepped"}, {"time": 1.7667, "angle": 18.24}, {"time": 3.3333, "curve": "stepped"}, {"time": 3.4333, "angle": 18.24}, {"time": 5, "curve": "stepped"}, {"time": 5.1, "angle": 18.24}, {"time": 6.6667, "curve": "stepped"}, {"time": 6.7667, "angle": 18.24}, {"time": 8.3333, "curve": "stepped"}, {"time": 8.4333, "angle": 18.24}], "translate": [{}, {"time": 6.6667, "x": -13.41, "y": 1.16}, {"time": 10, "x": -20.11, "y": 1.74}]}, "line6": {"translate": [{}, {"time": 6.6667, "x": -12.1, "y": 3.52}, {"time": 10, "x": -18.15, "y": 5.28}]}, "fire": {"rotate": [{"curve": "stepped"}, {"time": 0.0333, "angle": -24.87, "curve": "stepped"}, {"time": 0.0667, "angle": 66.16, "curve": "stepped"}, {"time": 0.1, "angle": 45.08}, {"time": 0.1333, "angle": 111.26, "curve": "stepped"}, {"time": 0.1667, "angle": 82.23, "curve": "stepped"}, {"time": 0.2, "angle": 110.47, "curve": "stepped"}, {"time": 0.2333, "angle": 142.72, "curve": "stepped"}, {"time": 0.2667, "angle": 173.71, "curve": "stepped"}, {"time": 0.3333, "angle": 145.17, "curve": "stepped"}, {"time": 0.4333, "angle": 112.04, "curve": "stepped"}, {"time": 0.4667, "angle": 137.72}, {"time": 1.6667, "curve": "stepped"}, {"time": 1.7, "angle": -24.87, "curve": "stepped"}, {"time": 1.7333, "angle": 66.16, "curve": "stepped"}, {"time": 1.7667, "angle": 45.08}, {"time": 1.8, "angle": 111.26, "curve": "stepped"}, {"time": 1.8333, "angle": 82.23, "curve": "stepped"}, {"time": 1.8667, "angle": 110.47, "curve": "stepped"}, {"time": 1.9, "angle": 142.72, "curve": "stepped"}, {"time": 1.9333, "angle": 173.71, "curve": "stepped"}, {"time": 2, "angle": 145.17, "curve": "stepped"}, {"time": 2.1, "angle": 112.04, "curve": "stepped"}, {"time": 2.1333, "angle": 137.72}, {"time": 3.3333, "curve": "stepped"}, {"time": 3.3667, "angle": -24.87, "curve": "stepped"}, {"time": 3.4, "angle": 66.16, "curve": "stepped"}, {"time": 3.4333, "angle": 45.08}, {"time": 3.4667, "angle": 111.26, "curve": "stepped"}, {"time": 3.5, "angle": 82.23, "curve": "stepped"}, {"time": 3.5333, "angle": 110.47, "curve": "stepped"}, {"time": 3.5667, "angle": 142.72, "curve": "stepped"}, {"time": 3.6, "angle": 173.71, "curve": "stepped"}, {"time": 3.6667, "angle": 145.17, "curve": "stepped"}, {"time": 3.7667, "angle": 112.04, "curve": "stepped"}, {"time": 3.8, "angle": 137.72}, {"time": 5, "curve": "stepped"}, {"time": 5.0333, "angle": -24.87, "curve": "stepped"}, {"time": 5.0667, "angle": 66.16, "curve": "stepped"}, {"time": 5.1, "angle": 45.08}, {"time": 5.1333, "angle": 111.26, "curve": "stepped"}, {"time": 5.1667, "angle": 82.23, "curve": "stepped"}, {"time": 5.2, "angle": 110.47, "curve": "stepped"}, {"time": 5.2333, "angle": 142.72, "curve": "stepped"}, {"time": 5.2667, "angle": 173.71, "curve": "stepped"}, {"time": 5.3333, "angle": 145.17, "curve": "stepped"}, {"time": 5.4333, "angle": 112.04, "curve": "stepped"}, {"time": 5.4667, "angle": 137.72}, {"time": 6.6667, "curve": "stepped"}, {"time": 6.7, "angle": -24.87, "curve": "stepped"}, {"time": 6.7333, "angle": 66.16, "curve": "stepped"}, {"time": 6.7667, "angle": 45.08}, {"time": 6.8, "angle": 111.26, "curve": "stepped"}, {"time": 6.8333, "angle": 82.23, "curve": "stepped"}, {"time": 6.8667, "angle": 110.47, "curve": "stepped"}, {"time": 6.9, "angle": 142.72, "curve": "stepped"}, {"time": 6.9333, "angle": 173.71, "curve": "stepped"}, {"time": 7, "angle": 145.17, "curve": "stepped"}, {"time": 7.1, "angle": 112.04, "curve": "stepped"}, {"time": 7.1333, "angle": 137.72}, {"time": 8.3333, "curve": "stepped"}, {"time": 8.3667, "angle": -24.87, "curve": "stepped"}, {"time": 8.4, "angle": 66.16, "curve": "stepped"}, {"time": 8.4333, "angle": 45.08}, {"time": 8.4667, "angle": 111.26, "curve": "stepped"}, {"time": 8.5, "angle": 82.23, "curve": "stepped"}, {"time": 8.5333, "angle": 110.47, "curve": "stepped"}, {"time": 8.5667, "angle": 142.72, "curve": "stepped"}, {"time": 8.6, "angle": 173.71, "curve": "stepped"}, {"time": 8.6667, "angle": 145.17, "curve": "stepped"}, {"time": 8.7667, "angle": 112.04, "curve": "stepped"}, {"time": 8.8, "angle": 137.72}], "translate": [{}, {"time": 0.0333, "x": -4.89, "y": 3.54}, {"time": 6.6667, "x": -33.66, "y": 43.68}, {"time": 10, "x": -48.11, "y": 63.85}], "scale": [{"curve": "stepped"}, {"time": 0.3, "y": -1, "curve": "stepped"}, {"time": 0.3667, "x": -1, "y": -1, "curve": "stepped"}, {"time": 0.4, "curve": "stepped"}, {"time": 0.5, "x": -1, "curve": "stepped"}, {"time": 0.5333, "curve": "stepped"}, {"time": 0.5667, "y": -1, "curve": "stepped"}, {"time": 0.6, "x": -1, "y": -1, "curve": "stepped"}, {"time": 0.6333, "x": -1, "curve": "stepped"}, {"time": 0.6667, "curve": "stepped"}, {"time": 0.7, "x": -1, "curve": "stepped"}, {"time": 0.7333, "x": -1, "y": -1, "curve": "stepped"}, {"time": 0.7667, "y": -1, "curve": "stepped"}, {"time": 0.8, "curve": "stepped"}, {"time": 0.8333, "y": -1}, {"time": 0.8667}, {"time": 0.9, "y": -1}, {"time": 0.9333, "x": -1, "y": -1}, {"time": 0.9667, "x": -1, "curve": "stepped"}, {"time": 1, "curve": "stepped"}, {"time": 1.0333, "y": -1, "curve": "stepped"}, {"time": 1.0667, "x": -1, "y": -1, "curve": "stepped"}, {"time": 1.1, "x": -1, "curve": "stepped"}, {"time": 1.1333, "curve": "stepped"}, {"time": 1.1667, "x": -1, "curve": "stepped"}, {"time": 1.2, "x": -1, "y": -1, "curve": "stepped"}, {"time": 1.2333, "y": -1, "curve": "stepped"}, {"time": 1.2667, "y": -1, "curve": "stepped"}, {"time": 1.3, "x": -1, "y": -1, "curve": "stepped"}, {"time": 1.3333, "x": -1, "curve": "stepped"}, {"time": 1.3667, "curve": "stepped"}, {"time": 1.4, "x": -1, "curve": "stepped"}, {"time": 1.4333, "x": -1, "y": -1, "curve": "stepped"}, {"time": 1.4667, "y": -1, "curve": "stepped"}, {"time": 1.5, "x": -1, "y": -1, "curve": "stepped"}, {"time": 1.5333, "x": -1, "curve": "stepped"}, {"time": 1.5667, "curve": "stepped"}, {"time": 1.6, "x": -1, "curve": "stepped"}, {"time": 1.6333, "x": -1, "y": -1, "curve": "stepped"}, {"time": 1.6667, "curve": "stepped"}, {"time": 1.9667, "y": -1, "curve": "stepped"}, {"time": 2.0333, "x": -1, "y": -1, "curve": "stepped"}, {"time": 2.0667, "curve": "stepped"}, {"time": 2.1667, "x": -1, "curve": "stepped"}, {"time": 2.2, "curve": "stepped"}, {"time": 2.2333, "y": -1, "curve": "stepped"}, {"time": 2.2667, "x": -1, "y": -1, "curve": "stepped"}, {"time": 2.3, "x": -1, "curve": "stepped"}, {"time": 2.3333, "curve": "stepped"}, {"time": 2.3667, "x": -1, "curve": "stepped"}, {"time": 2.4, "x": -1, "y": -1, "curve": "stepped"}, {"time": 2.4333, "y": -1, "curve": "stepped"}, {"time": 2.4667, "curve": "stepped"}, {"time": 2.5, "y": -1}, {"time": 2.5333}, {"time": 2.5667, "y": -1}, {"time": 2.6, "x": -1, "y": -1}, {"time": 2.6333, "x": -1, "curve": "stepped"}, {"time": 2.6667, "curve": "stepped"}, {"time": 2.7, "y": -1, "curve": "stepped"}, {"time": 2.7333, "x": -1, "y": -1, "curve": "stepped"}, {"time": 2.7667, "x": -1, "curve": "stepped"}, {"time": 2.8, "curve": "stepped"}, {"time": 2.8333, "x": -1, "curve": "stepped"}, {"time": 2.8667, "x": -1, "y": -1, "curve": "stepped"}, {"time": 2.9, "y": -1, "curve": "stepped"}, {"time": 2.9333, "y": -1, "curve": "stepped"}, {"time": 2.9667, "x": -1, "y": -1, "curve": "stepped"}, {"time": 3, "x": -1, "curve": "stepped"}, {"time": 3.0333, "curve": "stepped"}, {"time": 3.0667, "x": -1, "curve": "stepped"}, {"time": 3.1, "x": -1, "y": -1, "curve": "stepped"}, {"time": 3.1333, "y": -1, "curve": "stepped"}, {"time": 3.1667, "x": -1, "y": -1, "curve": "stepped"}, {"time": 3.2, "x": -1, "curve": "stepped"}, {"time": 3.2333, "curve": "stepped"}, {"time": 3.2667, "x": -1, "curve": "stepped"}, {"time": 3.3, "x": -1, "y": -1, "curve": "stepped"}, {"time": 3.3333, "curve": "stepped"}, {"time": 3.6333, "y": -1, "curve": "stepped"}, {"time": 3.7, "x": -1, "y": -1, "curve": "stepped"}, {"time": 3.7333, "curve": "stepped"}, {"time": 3.8333, "x": -1, "curve": "stepped"}, {"time": 3.8667, "curve": "stepped"}, {"time": 3.9, "y": -1, "curve": "stepped"}, {"time": 3.9333, "x": -1, "y": -1, "curve": "stepped"}, {"time": 3.9667, "x": -1, "curve": "stepped"}, {"time": 4, "curve": "stepped"}, {"time": 4.0333, "x": -1, "curve": "stepped"}, {"time": 4.0667, "x": -1, "y": -1, "curve": "stepped"}, {"time": 4.1, "y": -1, "curve": "stepped"}, {"time": 4.1333, "curve": "stepped"}, {"time": 4.1667, "y": -1}, {"time": 4.2}, {"time": 4.2333, "y": -1}, {"time": 4.2667, "x": -1, "y": -1}, {"time": 4.3, "x": -1, "curve": "stepped"}, {"time": 4.3333, "curve": "stepped"}, {"time": 4.3667, "y": -1, "curve": "stepped"}, {"time": 4.4, "x": -1, "y": -1, "curve": "stepped"}, {"time": 4.4333, "x": -1, "curve": "stepped"}, {"time": 4.4667, "curve": "stepped"}, {"time": 4.5, "x": -1, "curve": "stepped"}, {"time": 4.5333, "x": -1, "y": -1, "curve": "stepped"}, {"time": 4.5667, "y": -1, "curve": "stepped"}, {"time": 4.6, "y": -1, "curve": "stepped"}, {"time": 4.6333, "x": -1, "y": -1, "curve": "stepped"}, {"time": 4.6667, "x": -1, "curve": "stepped"}, {"time": 4.7, "curve": "stepped"}, {"time": 4.7333, "x": -1, "curve": "stepped"}, {"time": 4.7667, "x": -1, "y": -1, "curve": "stepped"}, {"time": 4.8, "y": -1, "curve": "stepped"}, {"time": 4.8333, "x": -1, "y": -1, "curve": "stepped"}, {"time": 4.8667, "x": -1, "curve": "stepped"}, {"time": 4.9, "curve": "stepped"}, {"time": 4.9333, "x": -1, "curve": "stepped"}, {"time": 4.9667, "x": -1, "y": -1, "curve": "stepped"}, {"time": 5, "curve": "stepped"}, {"time": 5.3, "y": -1, "curve": "stepped"}, {"time": 5.3667, "x": -1, "y": -1, "curve": "stepped"}, {"time": 5.4, "curve": "stepped"}, {"time": 5.5, "x": -1, "curve": "stepped"}, {"time": 5.5333, "curve": "stepped"}, {"time": 5.5667, "y": -1, "curve": "stepped"}, {"time": 5.6, "x": -1, "y": -1, "curve": "stepped"}, {"time": 5.6333, "x": -1, "curve": "stepped"}, {"time": 5.6667, "curve": "stepped"}, {"time": 5.7, "x": -1, "curve": "stepped"}, {"time": 5.7333, "x": -1, "y": -1, "curve": "stepped"}, {"time": 5.7667, "y": -1, "curve": "stepped"}, {"time": 5.8, "curve": "stepped"}, {"time": 5.8333, "y": -1}, {"time": 5.8667}, {"time": 5.9, "y": -1}, {"time": 5.9333, "x": -1, "y": -1}, {"time": 5.9667, "x": -1, "curve": "stepped"}, {"time": 6, "curve": "stepped"}, {"time": 6.0333, "y": -1, "curve": "stepped"}, {"time": 6.0667, "x": -1, "y": -1, "curve": "stepped"}, {"time": 6.1, "x": -1, "curve": "stepped"}, {"time": 6.1333, "curve": "stepped"}, {"time": 6.1667, "x": -1, "curve": "stepped"}, {"time": 6.2, "x": -1, "y": -1, "curve": "stepped"}, {"time": 6.2333, "y": -1, "curve": "stepped"}, {"time": 6.2667, "y": -1, "curve": "stepped"}, {"time": 6.3, "x": -1, "y": -1, "curve": "stepped"}, {"time": 6.3333, "x": -1, "curve": "stepped"}, {"time": 6.3667, "curve": "stepped"}, {"time": 6.4, "x": -1, "curve": "stepped"}, {"time": 6.4333, "x": -1, "y": -1, "curve": "stepped"}, {"time": 6.4667, "y": -1, "curve": "stepped"}, {"time": 6.5, "x": -1, "y": -1, "curve": "stepped"}, {"time": 6.5333, "x": -1, "curve": "stepped"}, {"time": 6.5667, "curve": "stepped"}, {"time": 6.6, "x": -1, "curve": "stepped"}, {"time": 6.6333, "x": -1, "y": -1, "curve": "stepped"}, {"time": 6.6667, "curve": "stepped"}, {"time": 6.9667, "y": -1, "curve": "stepped"}, {"time": 7.0333, "x": -1, "y": -1, "curve": "stepped"}, {"time": 7.0667, "curve": "stepped"}, {"time": 7.1667, "x": -1, "curve": "stepped"}, {"time": 7.2, "curve": "stepped"}, {"time": 7.2333, "y": -1, "curve": "stepped"}, {"time": 7.2667, "x": -1, "y": -1, "curve": "stepped"}, {"time": 7.3, "x": -1, "curve": "stepped"}, {"time": 7.3333, "curve": "stepped"}, {"time": 7.3667, "x": -1, "curve": "stepped"}, {"time": 7.4, "x": -1, "y": -1, "curve": "stepped"}, {"time": 7.4333, "y": -1, "curve": "stepped"}, {"time": 7.4667, "curve": "stepped"}, {"time": 7.5, "y": -1}, {"time": 7.5333}, {"time": 7.5667, "y": -1}, {"time": 7.6, "x": -1, "y": -1}, {"time": 7.6333, "x": -1, "curve": "stepped"}, {"time": 7.6667, "curve": "stepped"}, {"time": 7.7, "y": -1, "curve": "stepped"}, {"time": 7.7333, "x": -1, "y": -1, "curve": "stepped"}, {"time": 7.7667, "x": -1, "curve": "stepped"}, {"time": 7.8, "curve": "stepped"}, {"time": 7.8333, "x": -1, "curve": "stepped"}, {"time": 7.8667, "x": -1, "y": -1, "curve": "stepped"}, {"time": 7.9, "y": -1, "curve": "stepped"}, {"time": 7.9333, "y": -1, "curve": "stepped"}, {"time": 7.9667, "x": -1, "y": -1, "curve": "stepped"}, {"time": 8, "x": -1, "curve": "stepped"}, {"time": 8.0333, "curve": "stepped"}, {"time": 8.0667, "x": -1, "curve": "stepped"}, {"time": 8.1, "x": -1, "y": -1, "curve": "stepped"}, {"time": 8.1333, "y": -1, "curve": "stepped"}, {"time": 8.1667, "x": -1, "y": -1, "curve": "stepped"}, {"time": 8.2, "x": -1, "curve": "stepped"}, {"time": 8.2333, "curve": "stepped"}, {"time": 8.2667, "x": -1, "curve": "stepped"}, {"time": 8.3, "x": -1, "y": -1, "curve": "stepped"}, {"time": 8.3333, "curve": "stepped"}, {"time": 8.6333, "y": -1, "curve": "stepped"}, {"time": 8.7, "x": -1, "y": -1, "curve": "stepped"}, {"time": 8.7333, "curve": "stepped"}, {"time": 8.8333, "x": -1, "curve": "stepped"}, {"time": 8.8667, "curve": "stepped"}, {"time": 8.9, "y": -1, "curve": "stepped"}, {"time": 8.9333, "x": -1, "y": -1, "curve": "stepped"}, {"time": 8.9667, "x": -1, "curve": "stepped"}, {"time": 9, "curve": "stepped"}, {"time": 9.0333, "x": -1, "curve": "stepped"}, {"time": 9.0667, "x": -1, "y": -1, "curve": "stepped"}, {"time": 9.1, "y": -1, "curve": "stepped"}, {"time": 9.1333, "curve": "stepped"}, {"time": 9.1667, "y": -1}, {"time": 9.2}, {"time": 9.2333, "y": -1}, {"time": 9.2667, "x": -1, "y": -1}, {"time": 9.3, "x": -1, "curve": "stepped"}, {"time": 9.3333, "curve": "stepped"}, {"time": 9.3667, "y": -1, "curve": "stepped"}, {"time": 9.4, "x": -1, "y": -1, "curve": "stepped"}, {"time": 9.4333, "x": -1, "curve": "stepped"}, {"time": 9.4667, "curve": "stepped"}, {"time": 9.5, "x": -1, "curve": "stepped"}, {"time": 9.5333, "x": -1, "y": -1, "curve": "stepped"}, {"time": 9.5667, "y": -1, "curve": "stepped"}, {"time": 9.6, "y": -1, "curve": "stepped"}, {"time": 9.6333, "x": -1, "y": -1, "curve": "stepped"}, {"time": 9.6667, "x": -1, "curve": "stepped"}, {"time": 9.7, "curve": "stepped"}, {"time": 9.7333, "x": -1, "curve": "stepped"}, {"time": 9.7667, "x": -1, "y": -1, "curve": "stepped"}, {"time": 9.8, "y": -1, "curve": "stepped"}, {"time": 9.8333, "x": -1, "y": -1, "curve": "stepped"}, {"time": 9.8667, "x": -1, "curve": "stepped"}, {"time": 9.9, "curve": "stepped"}, {"time": 9.9333, "x": -1, "curve": "stepped"}, {"time": 9.9667, "x": -1, "y": -1}]}}}, "blast": {"slots": {"91": {"attachment": [{"time": 0.0333, "name": "91"}, {"time": 0.0667, "name": null}]}, "92": {"attachment": [{"time": 0.0667, "name": "92"}, {"time": 0.1333, "name": null}]}, "93": {"attachment": [{"time": 0.1333, "name": "93"}, {"time": 0.2, "name": null}]}, "94": {"attachment": [{"time": 0.2, "name": "94"}, {"time": 0.2667, "name": null}]}, "95": {"attachment": [{"time": 0.2667, "name": "95"}, {"time": 0.3333, "name": null}]}, "96": {"attachment": [{"time": 0.3333, "name": "96"}, {"time": 0.4, "name": null}]}, "97": {"attachment": [{"time": 0.4, "name": "97"}, {"time": 0.4667, "name": null}]}, "98": {"attachment": [{"time": 0.4667, "name": "98"}, {"time": 0.5333, "name": null}]}, "99": {"attachment": [{"time": 0.5333, "name": "99"}, {"time": 0.6, "name": null}]}, "100": {"attachment": [{"time": 0.6, "name": "100"}, {"time": 0.6667, "name": null}]}, "bomb": {"attachment": [{"time": 0.2, "name": null}]}, "bomb2": {"attachment": [{"time": 0.2, "name": null}]}, "fire": {"attachment": [{"name": "fire"}, {"time": 0.2, "name": null}]}, "line": {"attachment": [{"time": 0.2, "name": null}]}}, "bones": {"line5": {"translate": [{"x": -20.11, "y": 1.74}]}, "line6": {"translate": [{"x": -18.15, "y": 5.28}]}, "line3": {"translate": [{"x": -21.43, "y": -3.27}]}, "line4": {"translate": [{"x": -16.81, "y": -7.15}]}, "fire": {"translate": [{"x": -48.11, "y": 63.85}], "scale": [{"x": -1, "y": -1}]}, "blast": {"translate": [{"time": 0.0333, "y": -39.04}], "scale": [{}, {"time": 0.0333, "x": 2.163, "y": 2.163}]}}, "events": [{"time": 0.1333, "name": "blowup"}]}, "ignite": {"slots": {"fire": {"attachment": [{"time": 0.0333, "name": "fire"}]}}, "bones": {"line3": {"translate": [{}, {"time": 0.1667, "x": -4.81, "y": -1.7}, {"time": 0.8667, "x": -14.02, "y": -3.65}, {"time": 0.9333, "x": -17.82, "y": -2.16}, {"time": 1.6667, "x": -21.43, "y": -3.27}]}, "line4": {"translate": [{}, {"time": 0.1333, "x": -4.99, "y": -3.88}, {"time": 0.3333, "x": -8.19, "y": -6.94}, {"time": 1.6667, "x": -16.81, "y": -7.15}]}, "line5": {"rotate": [{"curve": "stepped"}, {"time": 0.1, "angle": 18.24}], "translate": [{}, {"time": 0.3333, "x": -9.68, "y": 1.73}, {"time": 1.6667, "x": -20.11, "y": 1.74}]}, "line6": {"translate": [{}, {"time": 0.1667, "x": -8.38, "y": 3.28}, {"time": 0.3333, "x": -12.85, "y": 2.04}, {"time": 1.6667, "x": -18.15, "y": 5.28}]}, "fire": {"rotate": [{"curve": "stepped"}, {"time": 0.0333, "angle": -24.87, "curve": "stepped"}, {"time": 0.0667, "angle": 66.16, "curve": "stepped"}, {"time": 0.1, "angle": 45.08}, {"time": 0.1333, "angle": 111.26, "curve": "stepped"}, {"time": 0.1667, "angle": 82.23, "curve": "stepped"}, {"time": 0.2, "angle": 110.47, "curve": "stepped"}, {"time": 0.2333, "angle": 142.72, "curve": "stepped"}, {"time": 0.2667, "angle": 173.71, "curve": "stepped"}, {"time": 0.3333, "angle": 145.17, "curve": "stepped"}, {"time": 0.4333, "angle": 112.04, "curve": "stepped"}, {"time": 0.4667, "angle": 137.72}], "translate": [{}, {"time": 0.0333, "x": -4.89, "y": 3.54}, {"time": 0.0667, "x": -12.96, "y": 11.73}, {"time": 0.1, "x": -18.45, "y": 19.23}, {"time": 0.1333, "x": -26.13, "y": 24.17}, {"time": 0.1667, "x": -30.26, "y": 28.99}, {"time": 0.8667, "x": -36.4, "y": 38.4}, {"time": 1.6667, "x": -48.11, "y": 63.85}], "scale": [{"curve": "stepped"}, {"time": 0.3, "y": -1, "curve": "stepped"}, {"time": 0.3667, "x": -1, "y": -1, "curve": "stepped"}, {"time": 0.4, "curve": "stepped"}, {"time": 0.5, "x": -1, "curve": "stepped"}, {"time": 0.5333, "curve": "stepped"}, {"time": 0.5667, "y": -1, "curve": "stepped"}, {"time": 0.6, "x": -1, "y": -1, "curve": "stepped"}, {"time": 0.6333, "x": -1, "curve": "stepped"}, {"time": 0.6667, "curve": "stepped"}, {"time": 0.7, "x": -1, "curve": "stepped"}, {"time": 0.7333, "x": -1, "y": -1, "curve": "stepped"}, {"time": 0.7667, "y": -1, "curve": "stepped"}, {"time": 0.8, "curve": "stepped"}, {"time": 0.8333, "y": -1}, {"time": 0.8667}, {"time": 0.9, "y": -1}, {"time": 0.9333, "x": -1, "y": -1}, {"time": 0.9667, "x": -1, "curve": "stepped"}, {"time": 1, "curve": "stepped"}, {"time": 1.0333, "y": -1, "curve": "stepped"}, {"time": 1.0667, "x": -1, "y": -1, "curve": "stepped"}, {"time": 1.1, "x": -1, "curve": "stepped"}, {"time": 1.1333, "curve": "stepped"}, {"time": 1.1667, "x": -1, "curve": "stepped"}, {"time": 1.2, "x": -1, "y": -1, "curve": "stepped"}, {"time": 1.2333, "y": -1, "curve": "stepped"}, {"time": 1.2667, "y": -1, "curve": "stepped"}, {"time": 1.3, "x": -1, "y": -1, "curve": "stepped"}, {"time": 1.3333, "x": -1, "curve": "stepped"}, {"time": 1.3667, "curve": "stepped"}, {"time": 1.4, "x": -1, "curve": "stepped"}, {"time": 1.4333, "x": -1, "y": -1, "curve": "stepped"}, {"time": 1.4667, "y": -1, "curve": "stepped"}, {"time": 1.5, "x": -1, "y": -1, "curve": "stepped"}, {"time": 1.5333, "x": -1, "curve": "stepped"}, {"time": 1.5667, "curve": "stepped"}, {"time": 1.6, "x": -1, "curve": "stepped"}, {"time": 1.6333, "x": -1, "y": -1}]}}}}}