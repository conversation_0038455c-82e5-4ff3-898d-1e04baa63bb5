import FightCtrl from "../../ctrl/FightCtrl";
import { CharacterRole } from "../unitControl/CharacterAttributes";
import { CharacterControl, EEnemySelectTag } from "../unitControl/CharacterControl";
import { OperatorBehavior } from "./BaseOperator";

/**观察目标列表中是否有指定到达目的的 */
export default class OBLockToTarget extends OperatorBehavior {
    /**观察目标列表 */
    observationTargetList: CharacterControl[] = [];
    /**观察到达节点 */
    lookWordPosx = 0
    lookEnemy: CharacterControl
    lookEvent: () => void
    unLookEvent: () => void
    /**实时获取观察目标 */
    checkGetObservationTargetListEvent: () => void
    constructor(
        public attackTag = CharacterRole.INVALID
    ) {
        super();
    }

    private randomEnemy(list: CharacterControl[], characterRoleType: CharacterRole) {
        if (list.length == 0) return
        // if (FightCtrl.ins.isCurChanllgeLoopBoss) return list[0]
        let fillterEnemyList = [] as CharacterControl[]
        if (characterRoleType == CharacterRole.PARTNER) {
            //如果是宠物选择的话，优先选择宠物标志的
            fillterEnemyList = list.filter(enemy => enemy.selectTag == EEnemySelectTag.partners)
        } else {
            fillterEnemyList = list.filter(enemy => enemy.selectTag == EEnemySelectTag.player)
        }
        if (fillterEnemyList.length == 0) {
            fillterEnemyList = list.filter(enemy => enemy.selectTag == EEnemySelectTag.none)
        }
        if (fillterEnemyList.length == 0 && list.length != 0) {
            fillterEnemyList = list
        }
        let enemy = fillterEnemyList[Math.floor(Math.random() * fillterEnemyList.length)]
        if (enemy && !enemy.selectTag && characterRoleType) {
            enemy.selectTag = characterRoleType == CharacterRole.PARTNER ? EEnemySelectTag.partners : EEnemySelectTag.player
        }
        return enemy
    }
    onClear(roleControl: CharacterControl): void {
        this.lookEnemy = null
        this.unLookEvent?.()
        this.observationTargetList = []
    }
    roleControl: CharacterControl
    tickOperator(roleControl: CharacterControl, dt: number): void {
        this.roleControl = roleControl
        let canChooseEnemyList: CharacterControl[] = this.getAttackList();
        if (canChooseEnemyList.length != 0) {
            if (!this.lookEnemy) {
                this.lookEnemy = this.randomEnemy(canChooseEnemyList, roleControl?.role)
            } else if (this.lookEnemy.isDead) {
                this.lookEnemy = null
            }
            this.lookEvent?.()
        } else {
            this.unLookEvent?.()
        }
    }

    checkDis(target: CharacterControl, self: CharacterControl) {
        if (!target) return false
        if (self) {
            if (self.role == CharacterRole.ENEMY) {
                let enemyX = self.node.convertToWorldSpaceAR(cc.Vec3.ZERO).x
                let targetX = target.node.convertToWorldSpaceAR(cc.Vec3.ZERO).x
                // cc.log('debug: 敌人坐标x：' + enemyX + ', 主角的坐标x:' + targetX + ',当前距离:' + dis)
                return  enemyX - this.lookWordPosx <= targetX
            } else {
                return Math.abs(target.node.convertToWorldSpaceAR(cc.Vec3.ZERO).x - self.node.convertToWorldSpaceAR(cc.Vec3.ZERO).x) < this.lookWordPosx
            }
        } else {
            return target.node.convertToWorldSpaceAR(cc.Vec3.ZERO).x < this.lookWordPosx
        }
    }

    getAttackList(isSortPox = false) {
        this.checkGetObservationTargetListEvent?.();
        let canChooseEnemyList: CharacterControl[] = [];
        this.observationTargetList.forEach(target => {
            if (this.checkDis(target, this.roleControl)) {
                canChooseEnemyList.push(target);
            }
        });
        if (isSortPox) {
            return canChooseEnemyList.sort((a, b) => a.node.x - b.node.x);
        } else {
            return canChooseEnemyList;
        }
    }
}