import FightCtrl from "../../../../ctrl/FightCtrl";
import { UtilsExtends } from "../../../../util/UtilsExtends";
import { Damage } from "../../damage/DamageInfo";
import { TimelineEvent, TimelineObj, TimelineNode, TimelineModel } from "../../../timeline/TimelineObj";
import { CharacterControl } from "../../CharacterControl";
import { SkillModel } from "../../CharacterSkillObj";
import { SkillNames } from "./SkillNames";
import { CharacterResource } from "../../CharacterAttributes";
import PropertCrtl from "../../../../ctrl/PropertCrtl";


class SkillModelHpRecoveryTimelineEvent extends TimelineEvent {
    doEvent(timelineObj: TimelineObj): void {
        let hpRecover = PropertCrtl.ins.getPlayerHpRecover()
        if (hpRecover <= 0) return;
        timelineObj.caster.getComponent(CharacterControl).modResource(new CharacterResource(hpRecover));
    }
}
/** 回血  */
export class SkillModelHpRecovery extends SkillModel {
    constructor() {
        super()
        this.name = SkillNames.playerHpRecovery;
        this.effect = {
            id: UtilsExtends.generateUniqueId(),
            name: this.name,
            lifeDuration: 0.1,
            nodes: [
                {
                    timeStartPoint: 0,
                    event: new SkillModelHpRecoveryTimelineEvent()
                } as TimelineNode
            ]
        } as TimelineModel;
        this.coldTime = 1;
    }

}