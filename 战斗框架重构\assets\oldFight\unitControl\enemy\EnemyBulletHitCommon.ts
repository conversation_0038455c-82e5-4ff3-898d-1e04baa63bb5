import FightCtrl from "../../../ctrl/FightCtrl";
import { CharacterControl } from "../CharacterControl";
import { Damage } from "../damage/DamageInfo";
import { BulletOnHit, BulletObj } from "../bullet/BulletObj";
import PropertCrtl from "../../../ctrl/PropertCrtl";

/**
 * 直接生成一个伤害，没有其他效果
 */
export class EnemyBulletHitCommon extends BulletOnHit {
    process(bullet: BulletObj, target: cc.Node) {
        if (!bullet) return;
        // 触发伤害
        let baseAttack = bullet.caster.getComponent(CharacterControl).characterAttr.attack
        let attackInfo = PropertCrtl.ins.calculateEnemyAttackPower(bullet.caster, baseAttack)
        baseAttack = Math.ceil(attackInfo.resultAttack)
     
        FightCtrl.ins.createDamage(bullet.caster, target, new Damage(baseAttack, 0, 0), []);
        FightCtrl.ins.createHertLb(baseAttack, 2, target)
        bullet.target.getComponent(CharacterControl).onHitEvent?.()
        return true
    }
}